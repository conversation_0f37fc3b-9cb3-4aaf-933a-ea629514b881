<?php

/**	\defgroup pieces Pièces d'Achat et de Vente
 * 	\ingroup oms
 *	Ce module comprend toutes les fonctions nécessaires à la gestion des pièces d'achat et de vente
 *	(Commandes, Préparations de Livraison, Bons de livraison, factures).
 */

// Dépendances
require_once('users.inc.php');
require_once('email.inc.php');
require_once('delivery.inc.php');
require_once('promotions.inc.php');
require_once('stats.inc.php');
require_once('site.inc.php');
require_once('cfg.emails.inc.php');
require_once('ord.installments.inc.php');
require_once('strings.inc.php');
require_once('prd/nomenclatures.inc.php');
require_once('prd/deposits.inc.php');
require_once('prices.inc.php');
require_once('fields.inc.php');
require_once('view.admin.inc.php');
require_once('relays.inc.php');
require_once('rights.inc.php');
require_once('ord.models.inc.php');
require_once('delivery.price.inc.php');
require_once('prd/subscriptions.inc.php');
require_once('ord.invoices.inc.php');
require_once('ord.bl.inc.php');
require_once('ord.signature.inc.php');
require_once('ord.states.inc.php');
require_once('relations.inc.php');
require_once('prd.stocks.inc.php');
require_once('comparators.inc.php');
require_once('ria.queue.inc.php');
require_once('dlv_store_plage.inc.php');
require_once('flow/notifications.inc.php');

// Sous-modules
require_once('ord/gifts.inc.php');
require_once('ord/products.inc.php');
require_once('ord/products-intentions.inc.php');
require_once('ord/promotions.inc.php');
require_once('ord/carts.inc.php');
require_once('ord/restore.inc.php');
require_once('ord/orders-index.inc.php');
require_once('ord/pl.inc.php');
require_once('ord/bl.inc.php');
require_once('ord/alert-shop-owner.inc.php');
require_once('ord/notify.inc.php');
require_once('ord/relations.inc.php');
require_once('ord/date-modified.inc.php');

// Inclusions du spécifique en fonction du tenant
if( isset($config) && in_array($config['tnt_id'], [4, 8, 20]) ){
	require_once('ord/proloisirs.inc.php'); // Fonctions spécifiques à Proloisirs et gestedition
}

/** \defgroup model_orders Gestion des paniers et des commandes
 * 	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des paniers et des commandes.
 *	La notion de panier et de commande est regroupée dans la même table. Seule une modification
 *	du statut du panier permet de le transformer en commande, ce qui facilite l'opération.
 *
 *	La gestion du panier et des commandes comprend deux tables principales, ord_orders et ord_products.
 *	La première contient les paniers/commandes, et la seconde les produits contenus dans la commande.
 *
 *	Une troisième table ord_states, contient les états de commande.
 *
 *	@{
 */

/**	Cette fonction permet la modification de la référence de la commande.
 *
 *	@param int $ord Identifiant de la commande à modifier
 *	@param string $ref Nouvelle référence de commande (17 caractères max)
 *	@param $date Indique si l'update doit mettre la date de la commande à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_ref_update( $ord, $ref, $date = false ){
	global $config;

	$sql = 'update riashop.ord_orders set ord_ref=\''.addslashes(trim($ref)).'\' ';
	if( $date ){
		$sql.=' ,ord_date = now() ' ;
	}
	$sql .= 'where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord;

	return ria_mysql_query($sql);
}

/**	Cette fonction permet la modification des commentaires attachés à la commande.
 *
 *	@param int $ord Identifiant de la commande à modifier
 *	@param string $comments Commentaires attachés à la commande
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_comments_update( $ord, $comments ){
	global $config;

	return ria_mysql_query('
		update riashop.ord_orders set ord_comments=\''.addslashes(ucfirst(trim($comments))).'\'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}

/**	Cette fonction permet la mise à jour d'information interne Kontinuum sur une commande (note de synchro)
 * @see	https://kontinuum.atlassian.net/browse/RIASHOP2-2200
 *
 * @param	int		$ord		Identifiant d'une commande
 * @param	string	$sync_notes	Information libre sur la commande
 * @return	bool	True en cas de succès, false en cas d'échec
 */
function ord_orders_sync_notes_update($ord, $sync_notes){
	global $config;

	if( !is_numeric($ord) || !$ord || !is_string($sync_notes) ){
		return false;
	}

	return ria_mysql_query('
		update
			riashop.ord_orders
		set
			ord_sync_notes=\''.addslashes(trim($sync_notes)).'\'
		where
			ord_tnt_id='.$config['tnt_id'].'
		and ord_id='.$ord
	);

}

// \cond onlyria
/** Cette fonction permet de générer la sous requête permettant de récupérer les commandes selon une origine définie
 *	@param array $origin Obligatoire, informations sur l'origine demandée
 *	@return string Le code SQL permettant de filtrer les commandes selon leur origine
 */
function ord_orders_get_sql_origin( $origin ){
	// vérification du paramètre sur les origines
	if( $origin!==false && $origin!==-1 ){
		if( !is_array($origin) ){
			return false;
		}

		foreach( $origin as $o ){
			if( !is_array($o) || !sizeof($o) ){
				return false;
			}

			foreach( $o as $col=>$val ){
				if( !in_array($col, array( 'source', 'name', 'medium' )) ){
					return false;
				}
			}
		}
	}

	if( $origin===false ){
		return '';
	}

	// Exclure les Comparateurs de prix / Place de marché qui ne seraient pas présentes dans les origines
	$rctr = ctr_comparators_get( 0, false, false, null );
	if( !$rctr || !ria_mysql_num_rows($rctr) ){
		return false;
	}

	$sub_origin = '';

	if( $origin===-1 ){
		$sub_origin = ' and IFNULL(stats_source,"") = "" and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
	}elseif( is_array($origin) && sizeof($origin) ){
		$sub_origin .= ' and ( ';

		$i = 0; $ar_source = array();
		foreach( $origin as $o ){
			if( $i>0 ){
				$sub_origin .= ' or ';
			}

			$sub_origin .= '(';

			$j = 0;
			foreach( $o as $col=>$val ){
				if( $j>0 ){
					$sub_origin .= ' and ';
				}

				switch( $col ){
					case 'source' :
						if( $val=='newsletters' ){
							$ar_alerts = array( 'alert', 'alerte', 'alerts', 'alertes', 'newsletter', 'newsletters', 'mailjet' );
							$sub_origin .= 'lower(stats_source) in (\''.implode( '\', \'', $ar_alerts ).'\')';
						}else{
							$ar_source[] = $val;
							$sub_origin .= 'lower(stats_source) = lower("'.addslashes( $val ).'")';
						}
						break;
					case 'medium' :
						$sub_origin .= 'stats_medium="'.addslashes( $val ).'"';
						break;
					case 'name' :
						$sub_origin.= 'stats_name="'.addslashes( $val ).'"';
						break;
				}

				$j++;
			}

			$sub_origin .= ')';

			$i++;
		}

		$sub_origin .= ' )';

		$exclude = array();
		while( $ctr = ria_mysql_fetch_array($rctr) ){
			if( !in_array($ctr['source'], $ar_source) ){
				$exclude[] = $ctr['source'];
			}
		}

		if( sizeof($exclude) ){
			$sub_origin .= ' and stats_source not in (\''.implode('\',\'', $exclude).'\')';
		}
	}

	return $sub_origin;
}
// \endcond

// \cond onlyria
/** Permet la récupération d'une ou plusieurs commandes, filtrées en fonction des paramètres facultatifs.
 *
 *	Les commandes à l'état 'Panier supprimé' ne sont jamais retournées.
 *
 *	@param int|array $user Optionnel, identifiant d'un utilisateur, ou tableau d'utilisateurs, dont on souhaite charger les commandes.
 *	@param int $id Optionnel, identifiant d'une commande à charger (ou tableau)
 *	@param int|array $states Optionnel, identifiant d'état de commande sur laquelle filtrer le résultat, ou tableau d'identifiants d'états.
 *	@param int $limit Optionnel, permet de limiter le nombre de résultats retournés par la fonction.
 *	@param int $seller Optionnel, identifiant du vendeur ayant enregistré la commande
 *	@param $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par date. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : piece, ref, date, total, state. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param int $year Optionnel, filtre à appliquer sur l'année de création de la commande
 *	@param bool $include_reliquats si true, les commandes comportant des reliquats seront ajoutées au résultat
 *	@param bool $is_sync Optionnel, si True, retourne uniquement les commandes ayant un numéro de pièce
 *	@param bool $usr_exist Optionnel, force le fait que la ou les commandes soient rattachées à un compte valide
 *	@param bool $go_alert Optionnel, si True, les commandes retournées seront uniquement celles à relancer pour date d'appro modifiée
 *	@param string $ref_like Optionnel, recherche une ou plusieurs commande par leur référence
 *	@param bool $is_web Optionnel, par défaut toutes les commandes sont retournées, mettre true pour ne retourner que les commandes web
 *	@param int|array $fld Optionnel, identifiant d'un champ libre sur lequel filtrer le résultat (ou tableau multiple clé => valeur)
 *	@param $or_between_val Optionnel, détermine si la séparation entre les valeurs du tableau $fld est un OR ou un AND (AND par défaut)
 *	@param int $wst_id Optionnel, Identifiant ou tableau d'identifiants du site web sur lequel filtrer le résultat.
 *	@param $or_between_fld Optionnel, détermine si la séparation entre les clés du tableau $fld est un OR ou un AND (AND par défaut)
 *	@param string $lng Optionnel, langue pour la recherche par champs avancées
 *	@param int $model_user Optionnel, identifiant d'un compte utilisateur pour lequel on souhaite savoir si un modèle est accessible ($state = 26)
 *	@param int|array $prd Optionnel, identifiant ou tableau d'identifiants de produits, retournera les commandes contenant au moins un des produits donnés
 *	@param bool $include_masked Optionnel, permet de récupérer les commandes masquées
 *	@param int $parent_id Optionnel, identifiant d'une commande parent. Ignoré si $id est spécifié.
 *	@param int $pmt_id Optionnel, identifiant d'une promotion
 *	@param $pl_list Optionnel, identifiant ou tableau d'identifiants de préparations de livraison qui font référence à la commande.
 *	@param $bl_list Optionnel, identifiant ou tableau d'identifiants de bons de livraison qui font référence à la commande.
 *	@param $inv_list Optionnel, identifiant ou tableau d'identifiants de factures qui font référence à la commande.
 *
 *	@return resource Un résultat de requête MySQL, trié par date, comprenant les colonnes suivantes :
 *				- user : identifiant du client ayant passé la commande
 *				- id : Identifiant de la commande
 *				- ref : Référence de la commande (identifiant complété par des 0, 8 caractères)
 *				- piece : numéro de pièce dans la gestion commerciale
 *				- date : date/heure de la commande
 *				- date_en : date au format US
 *				- state_id : identifiant du statut de commande
 *				- state_name : libellé du statut de commande
 *				- state_name_plural : libellé au pluriel du statut de commande
 *				- products : nombre d'articles contenus dans la commande
 *				- total_ht : montant total de la commande, hors taxes
 *				- total_ttc : montant total de la commande, ttc
 *				- adr_invoices : identifiant de l'adresse de facturation
 *				- adr_delivery : identifiant de l'adresse de livraison
 *				- srv_id : identifiant du service de livraison demandé par le client
 *				- str_id : identifiant du magasin dans lequel le client souhaite être livré
 *				- dlv-notes : consignes de livraison
 *				- pmt_id : identifiant d'un code promotion appliqué à la commande
 *				- seller_id : identifiant du représentant ayant enregistré la commande
 *				- comments : commentaires du représentant ayant enregistré la commande
 *				- cnt_id : identifiant de la commande dans le moteur de recherche
 *				- ord_livr : date de livraison prévue de la commande
 *				- is_sync : détermine si la commande est synchronisée avec la gestion commerciale
 *				- nsync : détermine si la commande nécessite une synchronisation avec la gestion commerciale
 *				- need_sync : détermine si la commande nécessite une synchronisation avec la gestion commerciale
 *				- total_ht_delivered : montant total des articles non livrés de la commande
 *				- dps_id : dépôt de livraison de la commande (commandes fournisseurs)
 *				- age : Age de la commande
 *				- relanced : Détermine si le client de la commande doit être relancé pour paiement complémentaire (Atos / Bigship uniquement)
 *				- alert-livr : Détermine si une alerte email pour les produits en rupture de la commande doit être envoyée (Bigship uniquement)
 *				- rly_id : Identifiant du point-relais de livraison
 *				- parent : Identifiant du compte parent, si la commande est liée à un compte enfant (à ignorer si la variable de configuration "parent_is_order_holder" n'est pas activée)
 *				- wst_id : Identifiant du site pour lequel la commande a été passée
 *				- date_archived : Date d'archivation
 *				- date_modified : Date de modification
 *				- state_sage : non utilisé
 *				- opt-gift : option cadeau activée oui / non
 *				- opt-gift-message : message de l'option cadeau
 *				- pay_id : identifiant du moyen de paiement de la commande
 *				- card_id : identifiant du type de CB de la commande
 *				- reliquats : est un reliquat oui / non
 *				- ord_livr_fr : date de livraison prévue au format FR
 *				- date_modified_fr : date de dernière modification au format FR
 *				- masked : commande masquée oui / non
 *				- parent_id : identifiant de la commande parent
 *				- contact_id : identifiant du contact
 *				- currency : Code de devise utilisée
 */
function ord_orders_get( $user=0, $id=0, $states=0, $limit=0, $seller=null, $sort=false, $year=false, $include_reliquats=false, $is_sync=false, $usr_exist=false, $go_alert=false, $ref_like='', $is_web=false, $fld=false, $or_between_val=false, $wst_id=false, $or_between_fld=false, $lng=false, $model_user=false, $prd=false, $include_masked=false, $parent_id=false, $pmt_id=0, $pl_list=0, $bl_list=0, $inv_list=0 ){

	// Contrôle le filtre identifiant d'un utilisateur, ou tableau d'utilisateurs, dont on souhaite charger les commandes.
	$user = control_array_integer( $user, false );
	if( $user === false ){
		return false;
	}

	// Contrôle le filtre sur l'identifiant de commandes ou les identifiants de commande sur lesquels on souhaite filtrer le résultat
	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	if( !is_numeric($limit) || $limit < 0 ){
		return false;
	}

	// Contrôle le filtre sur l'identifiant de représentant
	if( $seller !== null && !is_numeric($seller) ){
		return false;
	}

	// Si un utilisateur de type représentant est connecté, on applique automatiquement le filtre sur le seller_id pour qu'il ne puisse voir que ses commandes
	// et pas les autres. Ce filtre automatique ne s'applique que si nous sommes dans l'administration
	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
		if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
			$seller = $_SESSION['usr_seller_id'];
		}
	}

	if( $states === false ){
		$states = 0;
	}
	$states = control_array_integer( $states, false );
	if( $states === false ){
		return false;
	}

	if( $prd === false ){
		$prd = 0;
	}
	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	if( !is_numeric($pmt_id) || $pmt_id<0 ){
		return false;
	}

	if( $wst_id === false ){
		$wst_id = array();
	}
	$wst_id = control_array_integer( $wst_id, false, true );
	if( $wst_id === false ){
		return false;
	}

	$pl_list = control_array_integer( $pl_list, false );
	if( $pl_list === false ){
		return false;
	}

	$bl_list = control_array_integer( $bl_list, false );
	if( $bl_list === false ){
		return false;
	}

	$inv_list = control_array_integer( $inv_list, false );
	if( $inv_list === false ){
		return false;
	}

	if( sizeof($id) == 1 ){
		$limit = 1;
	}

	$parent_id = is_numeric($parent_id) && $parent_id > 0 ? $parent_id : false;

	global $config;

	$sql = '
		select
			ifnull(usr_id, ord_usr_id) as user, ord_id as id, ord_ref as ref, ord_piece as piece,
			date_format(ord_date, "%d/%m/%Y à %H:%i") as date, ord_date as date_en, state_id,
			ifnull(stn_name, state_name) as "state_name", state_name_plural, ord_products as products,
			ord_total_ht as total_ht, ord_total_ttc as total_ttc, ord_adr_invoices as adr_invoices,
			ord_adr_delivery as adr_delivery, ord_state_sage as state_sage, ord_srv_id as srv_id, ord_str_id as str_id, ord_dlv_notes as "dlv-notes",
			ord_opt_gift as "opt-gift", ord_opt_gift_message as "opt-gift-message", ord_pay_id as pay_id, ord_card_id as card_id,
			ord_pmt_id as pmt_id, ord_seller_id as seller_id, ord_comments as comments,
			0 as reliquats, ord_cnt_id as cnt_id, ord_date_livr as ord_livr,
			ord_need_sync as nsync, ord_need_sync as need_sync, ord_piece!="" as is_sync,
			ord_total_ht_delivered as total_ht_delivered, ord_dps_id as dps_id,
			ord_pkg_id as pkg_id, to_days(now()) - to_days(ord_date) as age, date_format(ord_date_livr, "%d/%m/%Y à %H:%i") as ord_livr_fr,
			ord_relanced as relanced, ord_alert_livr as "alert-livr", ord_rly_id as rly_id, ifnull(usr_parent_id, 0) as parent,
			ord_wst_id as wst_id, ord_date_archived as date_archived, ord_masked as "masked",
			ord_date_modified as date_modified, date_format(ord_date_modified, "%d/%m/%Y à %H:%i") as date_modified_fr, ord_parent_id as parent_id,
			ord_contact_id as contact_id, ord_currency as currency, ord_reseller_id as reseller_id, ord_reseller_contact_id as reseller_contact_id
		from
			riashop.ord_orders
			join riashop.ord_states on ord_state_id = state_id
			left join riashop.ord_states_name on ord_state_id = stn_stt_id and ord_tnt_id = stn_tnt_id and ord_wst_id = stn_wst_id
			left join riashop.gu_users on ord_tnt_id = if(usr_tnt_id = 0, '.$config['tnt_id'].', usr_tnt_id) and ord_usr_id = usr_id
	';

	if( sizeof($states)==1 && $states[0]==_STATE_MODEL ){
		$sql .= ' left join riashop.ord_models_position on ( ord_tnt_id=omd_tnt_id and ord_id=omd_ord_id )';
	}

	if( $pmt_id>0 ){
		$sql .= '	join riashop.ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)';
	}

	$sql .= '
		where
			ord_tnt_id = '.$config['tnt_id'].'
			and ord_state_id != '._STATE_BASKET_CANCEL.'
	';

	if( !$include_masked ){
		$sql .= ' and ord_masked = 0';
	}

	if( $usr_exist ){
		$sql .= ' and usr_id is not null and usr_date_deleted is null';
	}

	if( sizeof($user) ){
		$sql .= ' and ord_usr_id in ('.implode(', ', $user).')';
	}

	if( sizeof($id) && $parent_id ){
		$sql .= ' and ord_id in ('.implode(', ', $id).') and ord_parent_id = '.$parent_id;
	}elseif( sizeof($id) ){
		$sql .= ' and ord_id in ('.implode(', ', $id).')';
	}elseif( $parent_id ){
		$sql .= ' and ord_parent_id = '.$parent_id;
	}else{
		// si aucun paramètre spécifié, pas de prise en compte des commandes enfants
		$sql .= ' and ord_parent_id is null';
	}

	if( sizeof($states) ){

		// recherche de l'ID de l'ancien système d'archivage
		$archivepos = array_search(_STATE_ARCHIVE, $states);
		$get_archive = false;
		if( $archivepos !== false ){
			unset($states[ $archivepos ]);
			$get_archive = true;
		}

		if( $get_archive && sizeof($states) ){
			$sql .= ' and ( ord_state_id in ('.implode(', ', $states).') or ord_date_archived is not null )';
		}elseif( sizeof($states) ){
			$sql .= ' and ord_state_id in ('.implode(', ', $states).')';
		}elseif( $get_archive ){
			$sql .= ' and ord_date_archived is not null';
		}

	}elseif( !sizeof($id) ){
		$sql .= ' and ord_state_id != '._STATE_MODEL;
	}

	// Applique le filtre sur le représentant
	if( is_numeric($seller) ){
		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
			$sql .= ' and (
				(ord_seller_id = '.$seller.' or ( ord_seller_id is null and usr_seller_id = '.$seller.' ) )

				or exists (
					select 1
					from rel_relations_hierarchy
					where rrh_tnt_id = '.$config['tnt_id'].'
						and rrh_rrt_id = 2
						and rrh_src_0 = '.$seller.'
						and rrh_src_1 = 0
						and rrh_src_2 = 0
						and rrh_dst_0 = ord_usr_id
						and rrh_dst_1 = 0
						and rrh_dst_2 = 0
				)

				or exists (
					select 1
					from rel_relations_objects
					where rro_tnt_id = '.$config['tnt_id'].'
						and rro_rrt_id = 2
						and rro_src_0 = '.$seller.'
						and rro_src_1 = 0
						and rro_src_2 = 0
						and rro_dst_0 = ord_usr_id
						and rro_dst_1 = 0
						and rro_dst_2 = 0
				)
			)';
		}else{
			$sql .= ' and ord_seller_id = '.$seller;
		}
	}

	if( is_numeric($year) ){
		$sql .= ' and year(ord_date) = '.$year;
	}

	if( $is_sync ){
		$sql .= ' and ord_piece != ""';
	}

	if( $go_alert ){
		$sql .= ' and ord_alert_livr = 1';
	}

	if( trim($ref_like) != '' ){
		$sql .= ' and lower(ord_ref) like lower("%'.addslashes($ref_like).'%")';
	}

	if( $is_web ){
		$sql .= ' and ord_pay_id is not null';
	}

	if( sizeof($states) == 1 && $states[0] == _STATE_MODEL ){
		$usr = array('id' => 0, 'prc_id' => 0, 'country_code' => '', 'cac_id' => -1, 'prf_id' => 0);
		if( is_numeric($model_user) && $model_user > 0 ){
			$rusr = gu_users_get( $model_user );
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$usr = ria_mysql_fetch_array($rusr);
				$usr['country_code'] = sys_countries_get_code( $usr['country'] );
				if( !$usr['country_code'] ){
					$usr['country_code'] = 'FR';
				}
				if( !$usr['cac_id'] ){
					$usr['cac_id'] = -1;
				}
			}
		}
		$sql .= '
			and ifnull((
				select omu_is_allowed
				from riashop.ord_model_users
				where
					omu_tnt_id = ord_tnt_id and omu_ord_id = ord_id
					and ( omu_usr_id is null or omu_usr_id = '.$usr['id'].' )
					and ( omu_prc_id is null or omu_prc_id = '.$usr['prc_id'].' )
					and ( omu_cnt_code is null or lower(omu_cnt_code) = lower("'.$usr['country_code'].'") )
					and ( omu_cac_id is null or omu_cac_id = '.$usr['cac_id'].' )
					and ( omu_prf_id is null or omu_prf_id = '.$usr['prf_id'].' )
					and (
						omu_fld_id is null or exists (
							select 1
							from riashop.fld_object_values
							where pv_tnt_id = '.$config['tnt_id'].'
								and pv_fld_id = omu_fld_id
								and pv_value = omu_fld_value
								and pv_obj_id_0 = '.$usr['id']. '
								and pv_obj_id_1 = 0
								and pv_obj_id_2 = 0
						)
					)
				order by
					ifnull(omu_usr_id, 0) desc, ifnull(omu_prc_id, 0) desc, ifnull(omu_cnt_code, "") desc,
					ifnull(omu_cac_id, -1) desc, ifnull(omu_prf_id, 0) desc, ifnull(omu_fld_id, 0) desc, omu_is_allowed asc
				limit 0, 1
			), 0) = 1
		';
	}

	// note : pas besoin de la classe "ord_parent_id is null" sur les reliquats
	// puisqu'un reliquat est forcément sur une commande parent
	if( sizeof($user) < 2 && $include_reliquats ){

		$sql .= '
			union

			select
				ifnull(usr_id, ord_usr_id) as user, ord_id as id, ord_ref as ref, ord_piece as piece,
				date_format(ord_date,"%d/%m/%Y à %H:%i") as date, ord_date as date_en, ord_state_id as state_id, "Livraison partielle" as state_name, ord_products as products,
				ord_total_ht as total_ht, ord_total_ttc as total_ttc, ord_adr_invoices as adr_invoices,
				ord_adr_delivery as adr_delivery, ord_state_sage as state_sage, ord_srv_id as srv_id, ord_str_id as str_id, ord_dlv_notes as "dlv-notes",
				ord_opt_gift as "opt-gift", ord_opt_gift_message as "opt-gift-message", ord_pay_id as pay_id, ord_card_id as card_id,
				ord_pmt_id as pmt_id, ord_seller_id as seller_id, ord_comments as comments,
				1 as reliquats, ord_cnt_id as cnt_id, ord_date_livr as ord_livr, ord_need_sync as nsync, ord_total_ht_delivered as total_ht_delivered,
				ord_dps_id as dps_id, to_days(now()) - to_days(ord_date) as age, date_format(ord_date_livr, "%d/%m/%Y à %H:%i") as ord_livr_fr,
				ord_relanced as relanced, ord_alert_livr as "alert-livr", ord_rly_id as rly_id, ifnull(usr_parent_id, 0) as parent,
				ord_wst_id as wst_id, ord_date_archived as date_archived, ord_masked as "masked",
				ord_date_modified as date_modified, date_format(ord_date_modified, "%d/%m/%Y à %H:%i") as date_modified_fr, ord_parent_id as parent_id
			from
				riashop.ord_orders
				left join riashop.gu_users on ord_tnt_id = if(usr_tnt_id = 0, '.$config['tnt_id'].', usr_tnt_id) and ord_usr_id = usr_id
			where
				ord_tnt_id = '.$config['tnt_id'].'
				and ord_state_id not in ('._STATE_BASKET_CANCEL.', '._STATE_MODEL.')
				and ord_id in ('.ord_orders_delayed_list( $user[0] ).')
		';

		if( !$include_masked ){
			$sql .= ' and ord_masked = 0';
		}

		if( $usr_exist ){
			$sql .= ' and usr_id is not null and usr_date_deleted is null';
		}

		if( sizeof($user) ){
			$sql .= ' and ord_usr_id = '.$user[0];
		}

		if( is_numeric($seller) ){
			$sql .= ' and ord_seller_id = '.$seller;
		}

		if( is_numeric($year) ){
			$sql .= ' and year(ord_date) = '.$year;
		}

	}

	$sql .= fld_classes_sql_get( CLS_ORDER, $fld, $or_between_val, $or_between_fld, $lng );

	if( sizeof($wst_id) ){
		$sql .= ' and ifnull(ord_wst_id, 0) in ( '.implode(', ', $wst_id).' )';
	}

	if( sizeof($prd) ){
		$sql .= '
			and ord_id in (
				select prd_ord_id
				from riashop.ord_products
				where prd_tnt_id = '.$config['tnt_id'].' and prd_id in ('.implode(', ', $prd).')
			)
		';
	}

	if( $pmt_id ){
		$sql .= ' and oop_pmt_id='.$pmt_id;
	}

	if( sizeof($pl_list) ){
		$sql .= '
			and exists (
				select 1 from riashop.ord_pl_products as pp
				where pp.prd_tnt_id = '.$config['tnt_id'].' and pp.prd_ord_id = ord_id
				and pp.prd_pl_id in ('.implode(', ', $pl_list).')
			)
		';
	}

	if( sizeof($bl_list) ){
		$sql .= '
			and exists (
				select 1 from riashop.ord_bl_products as bp
				where bp.prd_tnt_id = '.$config['tnt_id'].' and bp.prd_ord_id = ord_id
				and bp.prd_bl_id in ('.implode(', ', $bl_list).')
			)
		';
	}

	if( sizeof($inv_list) ){
		$sql .= '
			and exists (
				select 1 from riashop.ord_inv_products as ip
				where ip.prd_tnt_id = '.$config['tnt_id'].' and ip.prd_ord_id = ord_id
				and ip.prd_inv_id in ('.implode(', ', $inv_list).')
			)
		';
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();
	if( is_array($sort) ){
		foreach( $sort as $col => $dir ){
			$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
			switch( strtolower(trim($col)) ){
				case 'id':
					$sort_final[] = 'id '.$dir;
					break;
				case 'piece':
					$sort_final[] = 'piece '.$dir;
					break;
				case 'ref':
					$sort_final[] = 'ref '.$dir;
					break;
				case 'date':
					$sort_final[] = 'date_en '.$dir;
					break;
				case 'total':
					$sort_final[] = 'total_ht '.$dir;
					break;
				case 'state':
					$sort_final[] = 'state_sage '.$dir;
					break;
			}
		}
	}

	if( !sizeof($sort_final) ){
		if( sizeof($states)==1 && $states[0]==_STATE_MODEL ){
			if( ord_models_order_get() ){
				$sort_final = array( 'ifnull(omd_pos, 0) asc, lower(ord_ref) asc');
			}else{
				$sort_final = array( 'lower(ord_ref) asc');
			}
		}else{
			if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_RESELLER ){
				$sort_final[] = 'state_sage asc';
			}

			$sort_final[] = 'date_en desc';
		}
	}

	$sql .= ' order by '.implode(', ', $sort_final);

	if( $limit > 0 ){
		$sql .= ' limit 0, '.$limit;
	}

	return ria_mysql_query($sql);

}
// \endcond

/** Cette fonction permet de charger des entêtes de commandes beaucoup plus light que ord_orders_get() ou ord_orders_get_with_adresses()
 * 	@param array $key Optionnel, tableau contenant des identifiants de commande :
 * 		- id : identifiant d'une commande
 * 		- parent_id : identifiant de la commande parente
 * 		- ref : référence d'une commande
 * 		- piece : numéro de pièce d'une commande
 * 	@param array $period Optionnel, tableau contenant les dates de début et/ ou de fin :
 * 		- start : date de début (format anglais)
 * 		- end : date de fin (format anglais)
 * 	@param array $filter Optionnel, tableau contenant les principaux filtres de récupération :
 * 		- is_web : true -> ne retourne que les commandes web, false -> ne retourne que les commandes gescom, null -> retourne toutes les commandes (par défaut à null)
 * 		- origin : filtre sur l'origine de commande
 * 		- wst_id : null -> retourne les commandes liées à aucun site, identifiant (ou tableau) d'un site (par défaut à 0, ignoré)
 * 		- pay_id : null -> retourne les commandes liées à aucun moyen de paiement, identifiant (ou tableau) d'un moyen de paiement (par défaut à 0, ignoré)
 * 		- seller_id : null -> retourne les commandes liées à aucun représentant, identifiant (ou tableau) d'un représentant (par défaut à false, ignoré)
 * 		- pmt_id : null -> retourne les commandes liées à aucune promotion, identifiant (ou tableau) d'une promotion (par défaut à false, ignoré)
 * 		- state_id : identifiant (ou tableau) d'un statut (par défaut à 0, ignoré)
 * 		- usr_id : identifiant (ou tableau) de compte utilisateur (par défaut à 0, ignoré)
 * 		- fld : tableau de recherche sur les champs avancés
 * 		- or_between_val : dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *		- or_between_fld : dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 * 		- parent_id : identifiant de la commande parent
 * 		- total_ht : tableau sous cette forme :  array( 'min' => 0, 'max' => 10)
 * 		- no_invoice : aucune facture n'est liée
 * 		- has_invoice : la commande est liée à une facture
 * 		- exclude_id : identifiant ou tableau d'identifiants de commande.s à exclure du résultat
 * 	@param array $other Optionnel, autre paramètre (vide pour le moment)
 * 		- sort : permet de trier le tableau (par défaut date => desc)
 * 		- limit : integer permet de limité le nombre de résultat retourné
 * 		- offset : integer permet d'ignorer un nombre de résultats à partir du premier résultat
 * 		- piece_like : permet de réaliser un like sur le numéro de pièce (valeur acceptée : start - piece%, end - %piece, contains - %piece%)
 * 	@param array $select Optionnel, permet de configurer le select de la requête, il faut un tableau contenant :
 * 		- type : complete => complète le select par défaut, replace remplace le select par défaut
 * 		- columns colonne à rajouter ('nom de la colone' => 'alias')
 *
 * 	@return resource Un résultat MySQL contenant :
 *		- id : identifiant de la commande
 *		- piece : numéro de pièce de la commande
 *		- ref : référence de la commande
 *		- usr_id : identifiant du compte client
 *		- date : date du passage de commande (ou création du panier selon le statut) au format anglais
 *		- date_livr : date de livraison souhaité pour cette commande
 *		- total_ht : total HT de la commande
 *		- total_ttc : total TTC de la commande
 *		- state_id : identifiant du statut
 *		- state_name_plural : libellé au pluriel du statut de commande
 *		- srv_id : identifiant du service de livraison
 *		- pay_id : identifiant du moyen de paiement
 *		- inv_id : identifiant de l'adresse de facturation
 *		- dlv_id : identifiant de l'adresse de livraison (null si livraison magasin ou relai)
 *		- third_id : identifiant de la troisième adresse
 *		- rly_id : identifiant du point relai choisi pour la livraison
 *		- str_id : identifiant du magasin choisi pour la livraison
 *		- seller_id : identifiant du représentant qui passe la commande
 *		- currency : devise utilisée (ISO 4217)
 */
function ord_orders_get_simple($key=array(), $period=array(), $filter=array(), $other=array(), $select=array()){

	{ // Contrôle des identifiants  en paramètre
		$key = array(
			'id' 		=> ria_array_get( $key, 'id', 0),
			'parent_id' => ria_array_get( $key, 'parent_id', 0),
			'ref' 		=> ria_array_get( $key, 'ref', ''),
			'piece' 	=> ria_array_get( $key, 'piece', ''),
			'need-sync' => isset($key['need-sync']) && $key['need-sync']
		);

		$key['id'] = control_array_integer($key['id'], false);
		if ($key['id'] === false) {
			return false;
		}

		$key['parent_id'] = control_array_integer($key['parent_id'], false);
		if ($key['parent_id'] === false) {
			return false;
		}
	}

	{ // Contrôle des filtres en paramètre
		$filter = array(
			'is_web' 			=> ria_array_get( $filter, 'is_web', null),
			'origin' 			=> ria_array_get( $filter, 'origin', false),
			'wst_id' 			=> ria_array_get( $filter, 'wst_id', 0),
			'pay_id' 			=> ria_array_get( $filter, 'pay_id', 0),
			'dps_id' 			=> ria_array_get( $filter, 'dps_id', 0),
			'seller_id' 		=> ria_array_get( $filter, 'seller_id', false),
			'pmt_id' 			=> ria_array_get( $filter, 'pmt_id', 0),
			'state_id' 			=> ria_array_get( $filter, 'state_id', 0),
			'state_line' 		=> ria_array_get( $filter, 'state_line', []),
			'exclude_state_id' 	=> ria_array_get( $filter, 'exclude_state_id', 0),
			'usr_id'			=> ria_array_get( $filter, 'usr_id', 0),
			'fld'				=> ria_array_get( $filter, 'fld', false),
			'fld-prd'			=> ria_array_get( $filter, 'fld-prd', false),
			'or_between_val'	=> ria_array_get( $filter, 'or_between_val', false),
			'or_between_fld'	=> ria_array_get( $filter, 'or_between_fld', false),
			'srv_id'			=> ria_array_get( $filter, 'srv_id', false),
			'seller_com'		=> ria_array_get( $filter, 'seller_com', false),
			'parent_id'			=> ria_array_get( $filter, 'parent_id', false),
			'total_ht' 			=> ria_array_get( $filter, 'total_ht', false),
			'piece_ci' 			=> ria_array_get( $filter, 'piece_ci', false),
			'no_invoice' 		=> ria_array_get( $filter, 'no_invoice', false),
			'has_invoice'		=> ria_array_get( $filter, 'has_invoice', false),
			'author_id'			=> ria_array_get( $filter, 'author_id', false),
			'str_id'			=> ria_array_get( $filter, 'str_id', 0),
			'include_masked'	=> ria_array_get( $filter, 'include_masked', false),
			'exclude_id'		=> ria_array_get( $filter, 'exclude_id', 0)
		);

		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE') ){
			if( $filter['state_id'] !== 0 ){
				$filter['state_line'] = control_array_integer( $filter['state_id'], true );
				if( $filter['state_line'] === false ){
					return false;
				}

				$filter['state_id'] = 0;
			}
		}

		// Si un utilisateur de type représentant est connecté, on applique automatiquement le filtre sur le seller_id pour qu'il ne puisse voir que ses commandes
		// et pas les autres. Ce filtre automatique ne s'applique que si nous sommes dans l'administration
		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
			if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
				$filter['seller_id'] = array( $_SESSION['usr_seller_id'] );
			}
		}

		// Vérifie que les filtres utilisés sont autorisés
		$allowed_filters = array('wst_id', 'seller_id', 'pmt_id', 'state_id', 'usr_id', 'srv_id', 'parent_id', 'dps_id', 'author_id', 'str_id');
		foreach ($allowed_filters as $k_filter) {
			if ($filter[$k_filter] === false || $filter[$k_filter] === null) {
				continue;
			}

			$filter[ $k_filter ] = control_array_integer($filter[ $k_filter ], false);
			if ($filter[ $k_filter ] === false) {
				return false;
			}
		}

	}

	{ // Contrôle des périodes en paramètre
		$period = array(
			'start' => ria_array_get( $period, 'start', ''),
			'end' 	=> ria_array_get( $period, 'end', ''),
			'col' 	=> ria_array_get( $period, 'col', 'ord_date'),
		);

		if (trim($period['start']) != "" && !isdateheure($period['start'])) {
			return false;
		}

		if (trim($period['end']) != "" && !isdateheure($period['end'])) {
			return false;
		}
	}

	{ // Contrôle le paramètre select
		if (!is_array($select)) {
			return false;
		}

		if (count($select)) {
			if (!ria_array_key_exists(array('type', 'columns'), $select)) {
				return false;
			}

			if (!in_array($select['type'], array('replace', 'complete'))) {
				return false;
			}

			if (!is_array($select['columns']) || !count($select['columns'])) {
				return false;
			}
		}
	}

	{ // Prépare le sql du select
		$sql_select = array(
			'ord_id'				=> 'id',
			'ord_piece'				=> 'piece',
			'ord_ref'				=> 'ref',
			'ord_usr_id'			=> 'usr_id',
			'ord_date'				=> 'date',
			'ord_date_livr'			=> 'date_livr',
			'ord_srv_id'			=> 'srv_id',
			'ord_total_ht'			=> 'total_ht',
			'ord_total_ttc'			=> 'total_ttc',
			'ord_state_id'			=> 'state_id',
			'state_name_plural'		=> 'state_name_plural',
			'ord_pay_id'			=> 'pay_id',
			'ord_adr_invoices'		=> 'inv_id',
			'ord_adr_delivery'		=> 'dlv_id',
			'ord_adr_final'			=> 'third_id',
			'ord_rly_id'			=> 'rly_id',
			'ord_str_id'			=> 'str_id',
			'ord_seller_id'			=> 'seller_id',
			'ord_currency'			=> 'currency',
			'ord_dps_id'			=> 'dps_id',
			'ord_dlv_notes'			=> '"dlv-notes"'
		);

		if (count($select)) {
			if ($select['type'] == 'replace') {
				$sql_select = $select['columns'];
			} else {
				$sql_select = array_merge($sql_select, $select['columns']);
			}
		}
	}

	{ // Préparation du SQL pour la ou les origines de commande
		$sub_origin = ord_orders_get_sql_origin($filter['origin']);

		if ($sub_origin === false) {
			return false;
		}
	}

	global $config;

	$sql = '
		select
	';

	if (count($sql_select)) {
		$first = true;
		foreach ($sql_select as $col=>$alias) {
			if (!$first) {
				$sql .= ', ';
			}

			$sql .= $col.' as '.$alias;
			$first = false;
		}
	}

	$sql .= '
		from riashop.ord_orders
			join riashop.ord_states on (ord_state_id=state_id)
	';

	{ // Gestion des jointures en fonction des paramètres
		if (is_array($filter['pmt_id']) && count($filter['pmt_id'])) {
			$sql .= ' join riashop.ord_orders_promotions on (ord_tnt_id = oop_tnt_id and ord_id = oop_ord_id)';
		}

		if (is_array($filter['origin']) && sizeof($filter['origin'])){
			$sql .= ' join riashop.stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )';
		}elseif ($filter['origin'] === -1){
			$sql .= ' left join riashop.stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )';
		}

		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
			$sql .= ' left join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and usr_id = ord_usr_id)';
		}

		if( count($filter['state_line']) > 0 ){
			$sql .= ' join ord_products on (prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = ord_id)';
		}
	}

	$sql .= '
		where ord_tnt_id = '.$config['tnt_id'].'
			'.( !$key['need-sync'] && !$filter['include_masked'] ? 'and ord_masked = 0' : '' ).'
	';

	if( $key['need-sync'] ){
		$key['id'] = array( 0 );
		$rorders = ord_orders_get_new_to_import();
		while( $r = ria_mysql_fetch_assoc($rorders) ){
			$key['id'][] = $r['id'];
		}
		$period['start'] = $period['end'] = '';
	}

	{ // Identifiant
		if (count($key['id'])) {
			$sql .= ' and ord_id in ('.implode(', ', $key['id']).')';
		}

		if (count($key['parent_id'])) {
			$sql .= ' and ord_parent_id in ('.implode(', ', $key['parent_id']).')';
		}elseif (!count($key['id'])  && (!array_key_exists('parent_id', $filter) || $filter['parent_id'] === false)) {
			$sql .= ' and ord_parent_id is null';
		}

		if ($key['ref']) {
			if(array_key_exists('ref_like', $other)){
				switch( $other['ref_like'] ){
					case 'start':
						$sql .= ' and ord_ref like "'.addslashes($key['ref']).'%"';
						break;
					case 'end':
						$sql .= ' and ord_ref like "%'.addslashes($key['ref']).'"';
						break;
					case 'contains':
						$sql .= ' and ord_ref like "%'.addslashes($key['ref']).'%"';
						break;
				}
			}else{
				$sql .= ' and ord_ref = "'.addslashes($key['ref']).'"';
			}
		}

		if( $key['piece'] ){
			// Le paramètre "piece_like" permet de faire un "where like" au lieu d'un "where =".
			// Les valeurs possibles sont décrites dans l'entête.
			if(array_key_exists('piece_like', $other)){
				switch( $other['piece_like'] ){
					case 'start':
						$sql .= ' and ord_piece like "'.addslashes($key['piece']).'%"';
						break;
					case 'end':
						$sql .= ' and ord_piece like "%'.addslashes($key['piece']).'"';
						break;
					case 'contains':
						$sql .= ' and ord_piece like "%'.addslashes($key['piece']).'%"';
						break;
				}
			} else {
				if( $filter['piece_ci'] ){
					// recherche de numéro de pièce insensible à la casse
					$sql .= ' and lower(ord_piece) = lower("'.addslashes($key['piece']).'")';
				}else{
					$sql .= ' and ord_piece = "'.addslashes($key['piece']).'"';
				}
			}
		}
	}

	{ // Filtres
		if ($filter['is_web'] !== null) {
			if ($filter['is_web']) {
				$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
			}else{
				$sql .= ' and ord_state_id>=3 and ord_pay_id is null';
			}
		}

		foreach ($filter as $key => $value) {
			if (!in_array($key, $allowed_filters)) {
				continue;
			}

			if ($value === null) {
				$sql .= ' and ifnull(ord_'.$key.', "vide") = "vide"';
				continue;
			}

			if (!is_array($value) || !count($value)) {
				continue;
			}
			if( $key === 'seller_id' ){
				if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
					$sql .= ' and (
						(ord_'.$key.' in ('.implode(', ', $value).') or ( ord_'.$key.' is null and usr_seller_id in ('.implode(', ', $value).') ) )

						or exists (
							select 1
							from rel_relations_hierarchy
							where rrh_tnt_id = '.$config['tnt_id'].'
								and rrh_rrt_id = 2
								and rrh_src_0 in ('.implode(', ', $value).')
								and rrh_src_1 = 0
								and rrh_src_2 = 0
								and rrh_dst_0 = ord_usr_id
								and rrh_dst_1 = 0
								and rrh_dst_2 = 0
						)

						or exists (
							select 1
							from rel_relations_objects
							where rro_tnt_id = '.$config['tnt_id'].'
								and rro_rrt_id = 2
								and rro_src_0 in ('.implode(', ', $value).')
								and rro_src_1 = 0
								and rro_src_2 = 0
								and rro_dst_0 = ord_usr_id
								and rro_dst_1 = 0
								and rro_dst_2 = 0
								and rro_date_deleted is null
						)
					)';
					continue;
				}
				$sql .= ' and ord_'.$key.' in ('.implode(', ', $value).')';
				continue;
			}

			if( $key == 'dps_id' ){
				$sql .= ' and (
					ord_'.$key.' in ('.implode(', ', $value).')
					or exists(
						SELECT DISTINCT prd_ord_id
						FROM ord_products
						WHERE prd_tnt_id = '.$config['tnt_id'].'
						AND prd_ord_id = ord_id
						AND prd_dps_id in ('.implode(', ', $value).')
					)
				)';

				continue;
			}

			$sql .= ' and ord_'.$key.' in ('.implode(', ', $value).')';
		}

		if( is_array($filter['total_ht'])){
			if( isset($filter['total_ht']['min']) ){
				$sql.= ' and ord_total_ht >= '.$filter['total_ht']['min'];
			}
			if( isset($filter['total_ht']['max']) ){
				$sql.= ' and ord_total_ht <= '.$filter['total_ht']['mex'];
			}
		}

		if (is_numeric($filter['fld']) || is_array($filter['fld'])) {
			$or_between_val = is_bool($filter['or_between_val']) ? $filter['or_between_val'] : false;
			$or_between_fld = is_bool($filter['or_between_fld']) ? $filter['or_between_fld'] : false;
			$sql .= fld_classes_sql_get( CLS_ORDER, $filter['fld'], $or_between_val, $or_between_fld);
		}

		if (is_numeric($filter['fld-prd']) || is_array($filter['fld-prd'])) {
			$or_between_val = is_bool($filter['or_between_val']) ? $filter['or_between_val'] : false;
			$or_between_fld = is_bool($filter['or_between_fld']) ? $filter['or_between_fld'] : false;
			$sql .= fld_classes_sql_get( CLS_ORD_PRODUCT, $filter['fld-prd'], $or_between_val, $or_between_fld);
		}

		// Filtre : aucune facture liée
		if( $filter['no_invoice'] ){
			$sql .= '
				and not exists (
					select 1
					from ord_inv_products
					where prd_tnt_id = '.$config['tnt_id'].'
						and prd_ord_id = ord_id
				)
			';
		}

		// Filtre : liée à une facture
		if( $filter['has_invoice'] ){
			$sql .= '
				and exists (
					select 1
					from ord_inv_products
					where prd_tnt_id = '.$config['tnt_id'].'
						and prd_ord_id = ord_id
				)
			';
		}

		// Filtre sur l'exclusion de statut
		$filter['exclude_state_id'] = control_array_integer($filter['exclude_state_id'], false);
		if( is_array($filter['exclude_state_id']) && count($filter['exclude_state_id']) ){
			$sql .= ' and ord_state_id not in ('.implode(', ', $filter['exclude_state_id']).')';
		}

		// Filtre sur l'exclusion de commande
		$filter['exclude_id'] = control_array_integer($filter['exclude_id'], false);
		if( is_array($filter['exclude_id']) && count($filter['exclude_id']) ){
			$sql .= ' and ord_id not in ('.implode(', ', $filter['exclude_id']).')';
		}

		$sql .= $sub_origin;
	}

	{ // Période
		if (trim($period['start']) != '') {
			if( isdate($period['start']) ){
				$period['start'] = $period['start'].' 00:00:00';
			}
			$sql .= ' and '.addslashes($period['col']).' >= "'.addslashes($period['start']).'"';
		}

		if (trim($period['end']) != '') {
			if( isdate($period['end']) ){
				$period['end'] = $period['end'].' 23:59:59';
			}
			$sql .= ' and '.addslashes($period['col']).' <= "'.addslashes($period['end']).'"';
		}
	}

	{ // Moyen de paiement
		if (array_key_exists('pay_id', $filter)) {
			$pay_id = $filter['pay_id'];
			if (is_null($pay_id)) {
				$sql .= ' and ord_pay_id is null';
			}elseif (is_numeric($pay_id)){
				if($pay_id > 0){
					$sql .= ' and ord_pay_id =' .$pay_id. ' ';
				}
			}
		}
	}

	{ // Choix du dépôt
		if (array_key_exists('deposit_id', $filter)) {
			$dps_id = $filter['dps_id'];
			if (is_null($dps_id)) {
				$sql .= ' and ord_dps_id is null';
			}elseif (is_numeric($dps_id)){
				if($dps_id > 0){
					$sql .= ' and ord_dps_id =' .$dps_id. ' ';
				}
			}
		}
	}

	{ // commercial commisioné
		if (array_key_exists('seller_com', $filter)) {
			$seller_com = $filter['seller_com'];
			if (is_null($seller_com)) {
				$sql .= ' and ord_seller_com is null';
			}elseif (is_numeric($seller_com)){
				if($seller_com > 0){
					$sql .= ' and ord_seller_com =' .$seller_com . ' ';
				}else{
					$sql .= ' and ord_seller_com == 0 ';
				}
			}
		}
	}

	{ // choix de l'auteur
		if (array_key_exists('author_id', $filter)) {
			$author_id = $filter['author_id'];
			if( $author_id > 0 ){
				$sql .= '
					and exists (
						select 1 from riashop.ord_orders_states as ip
						where ip.prd_tnt_id = '.$config['tnt_id'].' and ip.oos_ord_id = ord_id
							and (
								case
								when ord_state_id='._STATE_BASKET.' then oos_state_id='._STATE_BASKET.'
								when ord_state_id='._STATE_INTERVENTION_DEVIS.' then oos_state_id='._STATE_INTERVENTION_DEVIS.'
								else oos_state_id='._STATE_WAIT_PAY.'
								end
							)
							and oos_usr_id = '.$author_id.'
					)
				';
			}
		}
	}

	{ // Statut à la ligne
		if( count($filter['state_line']) > 0 ){
			$sql .= ' and prd_state_line in ('.implode( ', ', $filter['state_line'] ).')';
		}
	}

	if( count($filter['state_line']) > 0 || count($filter['dps_id']) > 0 ){
		$sql .= ' group by ord_id';
	}

	{ // Tri du résultat
		$sort_final = array();

		if (array_key_exists('sort', $other)) {
			$sort = $other['sort'];
			if (is_array($sort)) {
				foreach ($sort as $col => $dir) {
					$dir = $dir == 'desc' ? 'desc' : 'asc';

					switch ($col) {
						case 'date':
							array_push($sort_final, 'ord_date '.$dir);
							break;
						case 'date_livr':
							array_push($sort_final, 'ord_date_livr '.$dir);
							break;
						case 'piece':
							array_push($sort_final, 'ord_piece '.$dir);
							break;
						default:
							array_push($sort_final, addslashes($col).' '.$dir);
							break;
					}
				}
			}
		}

		if (!count($sort_final)) {
			$sort_final[] = 'ord_date desc';
		}

		$sql .= ' order by '.implode(', ', $sort_final);
	}

	{// Limite du résultat
		$limit = ria_array_get($other, 'limit', false);
		if (is_numeric($limit) && $limit > 0) {
			$sql .= ' limit '.$limit;

			$offset = ria_array_get($other, 'offset', false);
			if (is_numeric($offset) && $offset > 0 ) {
				$sql .= ' offset '.$offset;
			}
		}
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de charger un tableau contenant les informations sur l'adresse de facturation et de livraison d'une commande.
 * 	Son utilisation va de paire avec ord_orders_get_simple() qui permet de récupérer que des entête light des commandes.
 *
 * 	@param array $order Obligatoire, tableau contenant les informations suivantes :
 *		- inv_id : identifiant de l'adresse de facturation
 *		- dlv_id : identifiant de l'adresse de livraison (accepte 0 en fonction du type de livraison)
 *		- usr_id : identifiant du compte client
 *		- str_id : identifiant du magasin (accepte 0 en fonction du type de livraison)
 *		- rly_id : identifiant du point relais (accepte 0 en fonction du type de livraison)
 *	@param bool $check_user boolean Ajoute le vérification sur l'utitlisteur de la commande par défaut à oui
 *
 * 	@return array Un tableau contenant :
 * 		- type : type de livraison (postal -> à domicile, store -> dans un magasin, relay -> dans un point relais)
 * 		- invoice : information sur l'adresse de livraison
 * 			- title_id : identifiant de la civilité
 *			- title_name : nom de la civilité
 *			- id : identifiant de l'adresse
 *			- firstname : prénom
 *			- lastname : nom
 *			- society : société
 *			- address1 : adresse de facturaiton
 *			- address2 : complément d'adresse
 *			- address3 : complément d'adresse 2
 *			- postal_code : code postal
 *			- city : ville
 *			- country : pays
 *			- cnt_code : code ISO du pays
 *			- country_state : état / province
 *			- phone : téléphone
 *			- mobile : mobile
 * 		- delivery : information sur l'adresse de livraison
 * 			- title_id : identifiant de la civilité (vide si type != postal)
 *			- title_name : nom de la civilité (vide si type != postal)
 *			- id : identifiant de l'adresse de livraison ou du magasin ou du point relais
 *			- firstname : prénom (vide si type != postal)
 *			- lastname : nom (vide si type != postal)
 *			- society : société ou nom du magasin ou nom du point relais
 *			- address1 : adresse de facturaiton
 *			- address2 : complément d'adresse
 *			- address3 : complément d'adresse 2
 *			- postal_code : code postal
 *			- city : ville
 *			- country : pays
 *			- cnt_code : code ISO du pays
 *			- country_state : état / province (vide si type != postal)
 *			- phone : téléphone
 *			- mobile : mobile (vide si type != postal)
 */
function ord_orders_address_load($order, $check_user = true){
	global $config;

	if( isset($order['adr_delivery']) ){
		$order['dlv_id'] = $order['adr_delivery'];
	}
	if( isset($order['adr_invoices']) ){
		$order['inv_id'] = $order['adr_invoices'];
	}
	if( isset($order['user']) ){
		$order['usr_id'] = $order['user'];
	}

	if (!ria_array_key_exists(array('inv_id', 'dlv_id', 'usr_id', 'str_id', 'rly_id'), $order)) {
		return false;
	}

	$order_address = array(
		'type' => 'postal',
		'invoice' => array(
			'type_id' => 0,
			'title_id' => 0,
			'title_name' => '',
			'id' => '',
			'firstname' => '',
			'lastname' => '',
			'society' => '',
			'address1' => '',
			'address2' => '',
			'address3' => '',
			'postal_code' => '',
			'city' => '',
			'country' => '',
			'cnt_code' => '',
			'country_state' => '',
			'phone' => '',
			'mobile' => '',
			'fax' => '',
			'email' => '',
			'desc' => '',
		),
		'delivery' => array(
			'type_id' => 0,
			'title_id' => 0,
			'title_name' => '',
			'id' => '',
			'firstname' => '',
			'lastname' => '',
			'society' => '',
			'address1' => '',
			'address2' => '',
			'address3' => '',
			'postal_code' => '',
			'city' => '',
			'country' => '',
			'cnt_code' => '',
			'country_state' => '',
			'phone' => '',
			'mobile' => '',
			'fax' => '',
			'email' => '',
			'desc' => '',
		)
	);

	$tmp = array($order['inv_id']);
	if (is_numeric($order['dlv_id']) && $order['dlv_id'] != $order['inv_id']) {
		$tmp[] = $order['dlv_id'];
	}

	if( !count($tmp) ){
		return false;
	}

	if( !is_numeric($order['usr_id']) || $order['usr_id'] <= 0 ){
		return false;
	}

	$sql = '
		select
			adr_type_id as type_id, ifnull(title_id, "") as title_id, ifnull(title_name, "") as title_name,
			adr_id as id, adr_firstname as firstname, adr_lastname as lastname, adr_society as society,
			adr_address1 as address1, adr_address2 as address2, adr_address3 as address3,
			adr_postal_code as postal_code, adr_city as city, adr_country as country, adr_cnt_code as cnt_code, adr_country_state as country_state,
			adr_phone as phone, adr_mobile as mobile, adr_fax as fax, adr_email as email, adr_desc as "desc"
		from riashop.gu_adresses
			left join riashop.gu_titles on (adr_title_id = title_id)
		where adr_tnt_id in (0, '.$config['tnt_id'].')
			and adr_id in ('.implode(', ', $tmp).')
	';

	// Ajoute la vérification des adresses lié à l'utilisateur de la commande
	if( $check_user ){
		$sql .= " and adr_usr_id = " . $order['usr_id'];
	}

	$r_adr = ria_mysql_query($sql);

	if (!$r_adr || ria_mysql_num_rows($r_adr) != count($tmp)) {
		return false;
	}

	$ar_address = array();
	while ($adr = ria_mysql_fetch_assoc($r_adr)) {
		$ar_address[ $adr['id'] ] = $adr;
	}

	$order_address['invoice'] = $ar_address[ $order['inv_id'] ];

	if (is_numeric($order['str_id']) && $order['str_id']) {
		$r_store = dlv_stores_get($order['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null);

		if ($r_store && ria_mysql_num_rows($r_store)) {
			$store = ria_mysql_fetch_assoc($r_store);

			$order_address['type'] = 'store';
			$order_address['delivery']['id'] = $store['id'];
			$order_address['delivery']['society'] = $store['name'];
			$order_address['delivery']['address1'] = $store['address1'];
			$order_address['delivery']['address2'] = $store['address2'];
			$order_address['delivery']['postal_code'] = $store['zipcode'];
			$order_address['delivery']['city'] = $store['city'];
			$order_address['delivery']['country'] = $store['country'];
			$order_address['delivery']['phone'] = $store['phone'];
			$order_address['delivery']['fax'] = $store['fax'];
		}
	} elseif (is_numeric($order['rly_id']) && $order['rly_id']) {
		$r_relay = dlv_relays_get_simple($order['rly_id']);
		if ($r_relay && ria_mysql_num_rows($r_relay)) {
			$relay = ria_mysql_fetch_assoc($r_relay);

			$order_address['type'] = 'relay';
			$order_address['delivery']['id'] = $relay['id'];
			$order_address['delivery']['society'] = $relay['name'];
			$order_address['delivery']['address1'] = $relay['address1'];
			$order_address['delivery']['address2'] = $relay['address2'];
			$order_address['delivery']['postal_code'] = $relay['zipcode'];
			$order_address['delivery']['city'] = $relay['city'];
			$order_address['delivery']['country'] = $relay['country'];
			$order_address['delivery']['cnt_code'] = $relay['cnt_code'];
			$order_address['delivery']['phone'] = $relay['phone'];
		}
	} elseif (array_key_exists($order['dlv_id'], $ar_address)) {
		$order_address['delivery'] = $ar_address[ $order['dlv_id'] ];
	}

	return $order_address;
}

// \cond onlyria
/**	Cette fonction est un alias de ord_orders_get() permettant de récupérer une commande (et une seule), même si elle est masquée.
 *	@param int $id Obligatoire, identifiant de la commande à récupérer
 *	@param bool $with_address Optionnel, spécifie si ord_orders_get_with_adresses() doit être la fonction utilisée (récupère les informations de livraison).
 *	@return bool False en cas d'échec
 *	@return resource Un résultat MySQL comprenant les mêmes colonnes que ord_orders_get() (ou ord_orders_get_with_adresses() si $with_address)
 */
function ord_orders_get_masked( $id, $with_address = false ){
	if( $with_address ){
		return ord_orders_get_with_adresses( 0, $id, 0, '', false, false, false, false, null, false, false, false, false, 0, 0, false, true );
	}

	return ord_orders_get( 0, $id, 0, 0, null, false, false, false, false, false, false, '', false, false, false, false, false, false, false, false, true );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de masquer une commande.
 *	@param	int		$id		Obligatoire, identifiant de la commande à masquer
 *	@param	int		$user	Optionnel, spécifie l'identifiant de l'utilisateur associé à la commande
 *	@return	bool	True si la commande a bien été masquée, false en cas d'échec
 */
function ord_orders_set_masked( $id, $user=0 ){
	global $config;

	if( !is_numeric($id) || $id <= 0 || !ord_orders_exists($id, $user) ){
		return false;
	}

	if( ord_orders_is_masked($id) ){
		return true;
	}

	$sql = 'update riashop.ord_orders set
				ord_masked=1
			where ord_id='.$id.'
				and ord_tnt_id='.$config['tnt_id'];

	if( is_numeric($user) && $user > 0 ){
		$sql .= ' and ord_usr_id='.$user;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère l'identifiant d'une commande à partir d'un jeton MD5 spécifique aux places de marché
 *	@param string $token Obligatoire, jeton MD5 composé comme ceci : MD5(identifiant de la commande + référence de la commande + identifiant client)
 *	@param int $ctr Obligatoire, identifiant de la place de marché
 *	@return int L'identifiant de la commande, False en cas d'échec
 */
function ord_orders_get_from_ctr_token( $token, $ctr ){

	$token = trim($token);
	if( strlen($token) != 32 ){
		return false;
	}

	if( !ctr_comparators_is_marketplace( $ctr ) ){
		return false;
	}

	// chargement de l'utilisateur de la place de marché
	$ctr_param = ctr_params_get_array( $ctr, 'USR_ID' );
	if( !is_array($ctr_param) || !sizeof($ctr_param) ){
		return false;
	}

	global $config;

	$sql = '
		select ord_id
		from riashop.ord_orders
		where
			ord_tnt_id = '.$config['tnt_id'].'
			and md5(concat(ord_id, ord_ref, ord_usr_id)) = "'.addslashes($token).'"
			and ord_usr_id = '.addslashes($ctr_param['USR_ID']).'
			and ord_parent_id is null
	';

	$r = ria_mysql_query($sql);
	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);

}
// \endcond

// \cond onlyria
/** Détermine le panier moyen ainsi que d'autres indicateurs clés de performance sur les commandes
 *	@param bool $is_web Optionnel, par défaut n'en tiens pas compte, mettre true pour les commandes Web uniquement, false pour celle qui viennent de la synchronisation
 *	@param int|array $state Optionnel, identifiant ou tableau d'identifiants de status de commandes
 *	@param string $date_start Optionnel, date de début de la période
 *	@param string $date_end Optionnel, date de fin de la période
 *	@param array $exclude déprécié, cet argument n'est plus utilisé
 *	@param int|array $ord_id Optionnel, identifiant d'une commande ou tableau d'identifiants de commande sur lesquelles filtrer le résultat
 *	@param array $origin Facultatif, permet de filtrer les commandes sur son origine pour cela il faut donner un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium, si -1 est fourni alors on comptabilisera seulement les commandes sans origine connue
 *	@param array $users Facultatif, permet de filtrer les commandes sur un ou plusieurs utilisateurs, accepte un tableau d'identifiants, attention dans le cas d'utilisateur parent il n'ira pas chercher les utilisateurs enfants.
 *	@param array $min Facultatif, minimum de commande sous cette forme array('amount'=>minimum, 'is_ttc'=>true/false)
 *	@param int $wst Facultatif, identifiant ou tableau d'identifiants de site du locataire
 *	@param int $pay_id Facultatif, moyen de paiement
 *	@param int $seller_id Facultatif, identifiant du représentant de la ou des commande(s)
 *	@param int $pmt_id Optionnel, identifiant d'une promotion
 *	@param int $store Optionnel, identifiant d'un magasin
 *	@param string $currency Optionnel, code représentant la devise utilisée (ISO 4217)
 *	@param int $dps_id Optionnel, identifiant du dépôt
 *	@param int|array $profiles Optionnel, identifiant ou tableau d'identifiants de profils utilisateurs sur lesquels filtrer le résultat
 * 	@param $array_fld Optionnel, champs avancé à filtrer
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau comprenant les colonnes suivantes :
 *				- volume : nombre de commandes (Web)
 *				- total_ht : total ht des commandes
 *				- total_ttc : total ttc des commandes
 *				- average_ht : panier moyen en ht
 *				- average_ttc : panier moyen en ttc
 *				- marge : marge brute
 */
function ord_orders_get_average_totals(
	$is_web=null, $state=0, $date_start=false, $date_end=false, $exclude=false, $ord_id=0, $origin=false, $users=false, $min=0,
	$wst=0, $pay_id=0, $seller_id=0, $pmt_id=0, $store=0, $currency=false, $dps_id=0, $profiles=0, $array_fld=false
){

	$is_gescom = null;
	if ($is_web !== null) {
		$is_gescom = $is_web ? false : true;
	}

	// Todo : prendre en compte le paramètre $exclude avec un de $state
	$exclude_state = false;
	// end Todo

	$res = stats_orders_ca(
		'completed', $date_start, $date_end, '', $wst, $origin, $pay_id, $store, false, $seller_id, true, $is_gescom, $users, false,
		$exclude_state, $state, $ord_id, $pmt_id, $array_fld, false, $currency, $dps_id, $profiles
	);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}
	return ria_mysql_fetch_assoc($res);
}
// \endcond

// \cond onlyria
/** Détermine le statut d'une commande, sans prise en compte d'autres paramètres (peut récupérer l'état d'une commande masquée)
 *	@param int $id Identifiant de la commande
 *	@param bool $old_version Optionnel, convertit l'archivage en state_id 12
 *	@return bool False en cas d'échec, identifiant du statut de la commande sinon
 */
function ord_orders_get_state( $id, $old_version=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_state_id as state_id, ord_date_archived as date_archived
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$current_state = ria_mysql_result( $res, 0, 'state_id' );

	if( $old_version && ria_mysql_result( $res, 0, 'date_archived' ) ){
		$current_state = _STATE_ARCHIVE;
	}

	return $current_state;
}
// \endcond


// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant du compte rattaché à une commande.
 * 	@param int $ord_id Obligatoire, identifiant de la commande
 * 	@return int L'identifiant du compte client, ou false en cas d'erreur
 */
function ord_orders_get_user( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select ord_usr_id
		from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['ord_usr_id'];
}
// \endcond

// \cond onlyria
/** Détermine la valeur de ord_alert_livr d'une commande
 *	@param int $id Identifiant de la commande
 *	@param bool $alert Valeur booléenne de ord_alert_livr
 *	@return bool True si succès, False sinon
 */
function ord_orders_set_alert_livr( $id, $alert ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_query(
		'update riashop.ord_orders set
			ord_alert_livr='.( $alert ? '1' : '0' ).'
		where ord_id='.$id.'
			and ord_tnt_id='.$config['tnt_id']
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction calcule et retourne le total des commandes répondant aux critères optionnels fournis en paramètre.
 *	@param int $usr Optionnel, identifiant d'un utilisateur (ou groupe d'utilisateurs) sur lequel filtrer le résultat.
 *	@param int $state Optionnel, identifiant d'un état de commande sur lequel filtrer le résultat.
 *	@param string $date_start Optionnel, date de début de prise en compte des statistiques.
 *	@param string $date_end Optionnel, date de fin de prise en compte des statistiques.
 *	@param bool $groupby_usr Optionnel, détermine si regroupement par client doit être effectué (inutile si $usr spécifié et ne contenant qu'un seul élément).
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- total_ht : montant total ht.
 *			- total_ttc : montant total ttc.
 *			- count_ord : nombre de commandes.
 *			- usr_id : identifiant de l'utilisateur (uniquement si $groupby_usr activé).
 *	@return bool False en cas d'échec.
 */
function ord_orders_get_sum( $usr=0, $state=0, $date_start=false, $date_end=false, $groupby_usr=false ){

	$usr = control_array_integer( $usr, false );
	if( !$usr ){
		return false;
	}

	$state = control_array_integer( $state, false );
	if( !$state ){
		return false;
	}

	if( $date_start !== false && !isdateheure($date_start) ){
		return false;
	}

	if( $date_end !== false && !isdateheure($date_end) ){
		return false;
	}

	global $config;

	$sql = '
		select
			'.( $groupby_usr ? 'ord_usr_id as usr_id,' : '' ).'
			sum(ord_total_ht) as total_ht,
			sum(ord_total_ttc) as total_ttc,
			count(ord_id) as count_ord
		from
			riashop.ord_orders
		where
			ord_tnt_id='.$config['tnt_id'].'
			and ord_masked=0
			and ord_parent_id is null
	';

	if( sizeof($usr) ){
		$sql .= ' and ord_usr_id in ('.implode(', ', $usr).')';
	}

	if( sizeof($state) ){
		$archivepos = array_search(_STATE_ARCHIVE, $state);
		if( $archivepos !== false ){
			unset($state[ $archivepos ]);
		}

		if( $archivepos !== false && sizeof($state) ){
			$sql .= ' and ( ord_date_archived is not null or ord_state_id in ('.implode(', ', $state).') )';
		}elseif( $archivepos !== false ){
			$sql .= ' and ord_date_archived is not null';
		}elseif( sizeof($state) ){
			$sql .= ' and ord_state_id in ('.implode(', ', $state).')';
		}
	}

	if( $date_start ){
		$sql .= ' and ord_date >= "'.dateheureparse($date_start).'"';
	}

	if( $date_end ){
		$sql .= ' and ord_date < "'.dateheureparse($date_end).'"';
	}

	if( $groupby_usr ){
		$sql .= ' group by ord_usr_id';
	}

	$res = ria_mysql_query($sql);

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la référence d'une commande
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@return string Retourne la référence de la commande, ou chaîne vide si la commande n'existe pas
 */
function ord_orders_get_ref( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return '';
	}

	$res = ria_mysql_query('
		select ord_ref
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord.'
	');

	if( !$res ){
		return '';
	}

	return ria_mysql_result( $res, 0, 0 );

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'appareil qui a passé la commande
 *  @param int $id Obligatoire, Identifiant de la commande
 * 	@param int $dev_id Obligatoire, Identifiant d'un appareil
 * 	@param int $yuto_id Obligatoire, Identifiant de la commande avant switch
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function ord_orders_set_author_device( $id, $dev_id, $yuto_id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( !is_numeric($dev_id) || $dev_id <= 0 ){
		return false;
	}
	if( !is_numeric($yuto_id) || $yuto_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set
			ord_author_dev_id = '.$dev_id.',
			ord_author_yuto_id = '.$yuto_id.'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant de la commande qui aurait le même id
 *	Attention cette fonction doit être utilisé dans un cas bien précis, à savoir dans l'api, dans le cas d'un Add sur les commandes
 *	@param int $dev_id Obligatoire, identifiant de l'appareil
 *	@param int $yuto_id Obligatoire, identifiant de la commande dans yuto
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur de la commande
 *	@return string Retourne le numéro de la commande
 *	@return string Retourne une chaine vide si la commmande n'existe pas
 */
function ord_orders_get_by_author_device( $dev_id, $yuto_id, $usr_id ){
	if( !is_numeric($dev_id) || $dev_id <= 0 ){
		return false;
	}
	if( !is_numeric($yuto_id) || $yuto_id <= 0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}
	global $config;

	$res = ria_mysql_query('
		select ord_id
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_author_dev_id='.$dev_id.' and ord_usr_id='.$usr_id.' and ord_author_yuto_id='.$yuto_id.' and ord_piece=""
	');

	if( !$res || ria_mysql_num_rows($res) == 0){
		return '';
	}

	return ria_mysql_result( $res, 0, 0 );

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la pièce d'une commande
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@return string Retourne la pièce de la commande dans le cas contraire
 *	@return string Retourne une chaine vide si la commmande n'existe pas
 */
function ord_orders_get_piece( $ord ){
	global $config;

	if( !is_numeric($ord) ){
		return '';
	}

	$res = ria_mysql_query('
		select ord_piece
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return '';
	}

	return ria_mysql_result( $res, 0, 0 );

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le code de la monaie utilisée pour la commande
 * 	@param int $ord_id Obligatoire, Identifiant d'une commande
 * 	@return string|bool Retourne le code de la monaie utilisée (ISO 4217), false en cas d'erreur
 */
function ord_orders_get_currency( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select ord_currency
		from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['ord_currency'];
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le code promotion appliqué sur une commande
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@return int Retourne l'identifiant du code promotion, false si aucun code promotion
 *	@deprecated ne plus utiliser pour les nouvelles installation, plutôt ord_orders_promotions_get
 */
function ord_orders_get_pmt_id( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id<=0 ){
	    return false;
	}

	global $config;

	$res = ria_mysql_query('
		select ord_pmt_id
		from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['ord_pmt_id'];
}
// \endcond

// \cond onlyria
/**	Recherche les commandes qui doivent être importées dans la gestion commerciale
 *	@param int $wst_id Optionnel, identifiant ou tableau d'identifiants d'un site spécifique pour lequel on souhaite récupérer les commandes
 *	@param array $states Optionnel, tableau d'identifiants de statuts spécifiques
 *
 *	@return bool False en cas d'échec
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la commande
 *		- date : date de création de la commande
 *		- sync_past : 0 si la commande à été modifiée il y a moins d'une heure, 1 dans le cas contraire
 *		- sync_notes : Note de synchro
 */
function ord_orders_get_new_to_import( $wst_id=false, $states=false ){
	global $config;

	if( $wst_id !== false ){
		$wst_id = control_array_integer( $wst_id, false );
		if( $wst_id === false ){
			return false;
		}
	}

	// exclusion des adresses modifiées (si activé)
	$users_excluded_sql = '';
	if( $config['upd_address_to_import'] ){
		if( $adrs = gu_adresses_get_updated() ){
			if( ria_mysql_num_rows($adrs)>0 ){
				$usr_array = array();

				while( $adr = ria_mysql_fetch_array($adrs) ){
					$usr_array[] = $adr['usr_id'];
				}

				$users_excluded_sql = ' and u2.usr_id not in ('.implode(', ', $usr_array).')';
			}
		}
	}

	// statuts classiques - maintenir la fonction "gu_users_toimport_get" à l'identique
	// par défaut les commandes aux statuts suivantes seront synchronisées
	// 	- 3 - En attente de règlement
	//	- 4 - En attente de traitement
	// 	- 31 - Click'n'collect
	$ar_states = array( _STATE_PAY_CONFIRM, _STATE_CLICK_N_COLLECT );

	// Pour certain client, le statut "3 - En attente de règlement" ne sera plus synchronisé
	if( !in_array($config['tnt_id'], [171]) ){
		$ar_states[] = _STATE_WAIT_PAY;
	}

	// substitution
	if( is_numeric($states) && $states > 0 ){
		$ar_states = array( $states );
	}elseif( is_array($states) ){
		if( !sizeof($states) ){
			return false;
		}
		foreach( $states as $s ){
			if( !is_numeric($s) || $s <= 0 ){
				return false;
			}
		}
		$ar_states = $states;
	}

	// maintenir les conditions where à l'identique dans gu_users_toimport_get
	$sql = '
		select 	ord_id as id,
				ord_date as date,
				case when ord_date_modified < now() - INTERVAL 1 HOUR  then 1
				else 0 END as sync_past,
				ord_sync_notes as sync_notes
		from
			riashop.ord_orders
			join riashop.gu_users as u1 on ord_usr_id = u1.usr_id and ord_tnt_id = if(u1.usr_tnt_id=0, ord_tnt_id, u1.usr_tnt_id)
			join riashop.gu_users as u2 on '.( $config['parent_is_order_holder'] ? 'ifnull(u1.usr_parent_id, u1.usr_id)' : 'u1.usr_id' ).' = u2.usr_id and u1.usr_tnt_id = u2.usr_tnt_id
		where
			ord_tnt_id = '.$config['tnt_id'].'
			and ord_state_id in ('.implode(', ', $ar_states).')
			and ord_parent_id is null
			and ord_masked = 0
			and ord_date_archived is null
			and u1.usr_date_deleted is null
			and u2.usr_date_deleted is null
			'.$users_excluded_sql.'
	';

	$exists_usr_sync  = 'exists ( ';
	$exists_usr_sync .= '	select 1 ';
	$exists_usr_sync .= '	from riashop.rel_relations_hierarchy ';
	$exists_usr_sync .= '	join riashop.gu_users cu2 on rrh_tnt_id=cu2.usr_tnt_id and rrh_src_0=cu2.usr_id ';
	$exists_usr_sync .= '	where rrh_rrt_id='.REL_USR_HIERARCHY.' ';
	$exists_usr_sync .= '		and rrh_tnt_id=ord_tnt_id and rrh_dst_0=ord_usr_id and rrh_dst_1=0 and rrh_dst_2=0 ';
	$exists_usr_sync .= '		and cu2.usr_is_sync=1 ';
	$exists_usr_sync .= '		and cu2.usr_ref != "" ';
	$exists_usr_sync .= '		and cu2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') ';
	$exists_usr_sync .= ') ';

	// filtrage uniquement sur les clients synchronisés (la variable de configuration est vrai par défaut)
	if( $config['tnt_id'] == 19 ){
		$WST_PERSO = 43;
		$WST_US = 49;
		$WST_UK = 61;
		$sql .= ' and ( u2.usr_ref != "" ';
		$sql .= ' or ( ord_wst_id = '.$WST_PERSO.' and u2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') ) ';
		$sql .= ' or ( ord_wst_id = '.$WST_US.' and u2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') ) ';
		$sql .= ' or ( ord_wst_id = '.$WST_UK.' and u2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') ) ';
		$sql .= ' )';
	}elseif( $config['import_orders_from_sync_usr'] ){
		$sql .= ' and (u2.usr_ref != "" or '.$exists_usr_sync.') ';
	}else{
		$sql .= ' and (u2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') or '.$exists_usr_sync.') ';
	}

	if( isset($config['import_orders_need_sync']) && $config['import_orders_need_sync']){
		$sql .= ' and (ord_need_sync = 1 or ord_piece = "") ';
	}else{
		$sql .= ' and ord_piece = "" ';
	}

	if( $wst_id && sizeof($wst_id) ){
		$sql .= ' and ord_wst_id in ('.implode(',',$wst_id).')';
	}

	// Pour Proloisirs, les commandes issues des bornes proloisirs sont ignorées
	if( $config['tnt_id'] == 4 ){
		$sql .= ' and ord_wst_id != 26';
	}

	// Contrôle que la commande dispose d'au minimum une ligne de commande
	$sql .= '
		and exists (
			select 1 from ord_products
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_ord_id = ord_id
			limit 1
		)
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la liste des marques présentes dans une commande
 *	@param int $ord_id Identifiant d'une commande
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant d'une marque
 *				- title : nom d'une marque
 *				- prd_ids : identifiants des articles
 */
function ord_orders_get_brands( $ord_id ){
	global $config;
	if (!is_numeric($ord_id) || $ord_id<=0) {
		return false;
	}

	$sql = '
		select ifnull(brd_id, 0) as id, ifnull(if(ifnull(brd_title, "")="", brd_name, brd_title), "") as name, group_concat(op.prd_id) as prd_ids
		from riashop.ord_products as op
			join riashop.prd_products as p on (op.prd_tnt_id = p.prd_tnt_id and op.prd_id=p.prd_id)
			left join riashop.prd_brands on (brd_tnt_id=p.prd_tnt_id and brd_id = p.prd_brd_id)
		where op.prd_tnt_id = '.$config['tnt_id'].'
			and op.prd_ord_id = '.$ord_id.'
		group by id
	';

	return ria_mysql_query($sql);
}
// \endcond


/** Cette fonction permet de vérifier que la commande ne dispose d'aucun article en rupture
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $control_res Optionnel, par défaut à True, mettre False pour contrôler la quantité en préparation plutôt que la quantité réservé
 *	@return bool True si la commande contient aucun article en rupture, False dans le cas contraire ou en cas d'erreur
 */
function ord_orders_not_sold_out( $ord_id, $control_res=true ){
	if( !is_numeric($ord_id) || $ord_id<=0 ){
	    return false;
	}

	global $config;

	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}

	$sql = '
		select 1
		from riashop.ord_products as op
			join riashop.prd_products as p on ( p.prd_tnt_id = op.prd_tnt_id and p.prd_id = op.prd_id )
			left join riashop.prd_stocks on ( sto_tnt_id = op.prd_tnt_id and sto_prd_id = op.prd_id )
		where op.prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
			and ifnull(sto_dps_id, '.$dps.') = '.$dps.'
			and prd_follow_stock = 1
			and (' . prd_stocks_get_sql() . ' - ifnull('.($control_res ? prd_stocks_sto_res() : 'sto_prepa').', 0)) <= 0
	';

	if( isset($config['dlv_prd_references']) && is_array($config['dlv_prd_references']) && sizeof($config['dlv_prd_references']) ){
		$sql .= ' and op.prd_ref not in ("'.implode( '", "', $config['dlv_prd_references'] ).'")';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ( ria_mysql_num_rows($res) ? false : true );
}

// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/**	Cette fonction permet la modification de l'adresse de livraison d'une commande.
 *	L'adresse doit au préalable exister dans la base de données.
 *	@param int $ord Identifiant de la commande à modifier
 *	@param int|bool $adr Identifiant de l'adresse de livraison, ou false pour utiliser l'adresse de livraison par défaut
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_orders_adr_delivery_set( $ord, $adr=false ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	if( !gu_adresses_exists($adr) ){

		// chargement de l'adresse de livraison par défaut du client
		$radr = ria_mysql_query('
			select ifnull(usr_adr_delivery, 0) as adr_dlv, usr_adr_invoices as adr_inv
			from riashop.gu_users join riashop.ord_orders on usr_id=ord_usr_id
			where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord.' and ( usr_tnt_id='.$config['tnt_id'].' or usr_tnt_id=0 )
		');

		$adr_defaut = 'ord_adr_invoices'; // ancien comportement
		if( $radr && ria_mysql_num_rows($radr) ){
			$adr = ria_mysql_fetch_assoc($radr);

			if (gu_adresses_exists($adr['adr_dlv'])) {
				$adr_defaut = $adr['adr_dlv'];
			}else{
				$adr_defaut = $adr['adr_inv'];
			}
		}

		$res = ria_mysql_query('update riashop.ord_orders set ord_adr_delivery='.$adr_defaut.' where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord);
	}else{
		$res = ria_mysql_query('update riashop.ord_orders set ord_adr_delivery='.$adr.' where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord);
	}

	return $res;
}
// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de l'adresse de facturation d'une commande.
 *	L'adresse doit au préalable exister dans la base de données.
 *	@param int $ord Identifiant de la commande à modifier
 *	@param int|bool $adr Identifiant de l'adresse de facturation, ou false pour utiliser l'adresse de livraison
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_orders_adr_invoices_set( $ord, $adr=false ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	if( !gu_adresses_exists($adr) ){
		error_log( 'ord_orders_adr_invoices_set utilisé avec un $adr faux (tenant '.$config['tnt_id'].').' );
		return false;
	}else{
		$res = ria_mysql_query('
			update riashop.ord_orders set ord_adr_invoices='.$adr.'
			where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
		);
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de l'adresse de facturation d'une commande.
 *	L'adresse doit au préalable exister dans la base de données.
 *	@see ord_orders_adr_invoices_set
 *	@deprecated cette fonction a été renommée en ord_orders_adr_invoices_set
 */
function ord_orders_adr_ord_adr_invoice_set( $ord, $adr=false ){
	return ord_orders_adr_invoices_set( $ord, $adr );
}
// \endcond

// \cond onlyria
/** Cette fonction permet la modification du type de carte bleue utilisé pour payer une commande
 *	@param int $ord Identifiant de la commande
 *	@param $card Identifiant du type de carte bleue
 */
function ord_card_type_set( $ord, $card=false ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}
	if( !is_numeric($card) || !ord_card_types_exists($card) ){
		$card = 'null';
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_card_id='.$card.'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}
// \endcond

// \cond onlydev
/** \defgroup models_orders_val_cart Validation d'un panier
 * 	\ingroup model_orders
 * 	La validation d'un panier se fait en deux ou trois étapes
 * 		-# Définir le mode de paiement via ord_orders_pay_type_set()
 * 		-# Passer la commande au statut 3 via ord_orders_state_update()
 * 		-# Dans le cas d'une commande où le paiement est tout de suite validé (ex. paiement CB, PayPal), passer la commande au statut 4 via ord_orders_state_update()
 *	@{
 */
// \endcond

/**	Permet le renseignement du mode de paiement pour une commande donnée
 *	Si cette fonction est appellée sans son deuxième argument, le mode de paiement est effacé
 *	@param int $ord Identifiant de la commande
 *	@param int $type Identifiant du mode de paiement
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_orders_pay_type_set( $ord, $type=false ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}
	if( !is_numeric($type) || !ord_payment_types_exists($type) ){
		$type = 'null';
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_pay_id='.$type.'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}

// \cond onlydev
/// @}
// \endcond

// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/**	Cette fonction permet la modification des consignes de livraison.
 *	Appellée sans le deuxième argument, cette fonction efface les consignes de livraison.
 *	@param int $ord Identifiant de la commande
 *	@param string $notes Consignes de livraison
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_dlv_notes_set( $ord, $notes='' ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_dlv_notes=\''.addslashes(trim($notes)).'\'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}

// \cond onlydev
/// @}
// \endcond

/**	Permet l'activation ou la désactivation de l'option cadeau
 *	@param int $ord Identifiant de la commande à modifier
 *	@param $gift Booléen indiquant si l'option cadeau est activée ou non
 *	@param $gift_message Message à joindre au paquet cadeau sous la forme d'une carte
 */
function ord_orders_opt_gift_set( $ord, $gift=false, $gift_message='' ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	$options = dlv_options_get();

	$res = ria_mysql_query('
		update riashop.ord_orders set
			ord_opt_gift='.( $gift && trim($gift_message) ? '1' : '0' ).',
			ord_opt_gift_message=\''.addslashes(ucfirst(trim($gift_message))).'\'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);

	if( $gift && trim($gift_message) ){

		if( $options['gift-ref'] ){
			$rgift = prd_products_get(0,$options['gift-ref']);
			if( ria_mysql_num_rows($rgift) ){
				$g = ria_mysql_fetch_array($rgift);
				ord_products_del($_SESSION['ord_id'],$g['id']);
				ord_products_add($_SESSION['ord_id'],$g['id'],1,'',false);
			}
		}

	}elseif( $options['gift-ref'] ){
		ord_products_del_ref($_SESSION['ord_id'],$options['gift-ref']);
	}

	if( $res ){
		ord_orders_update_totals($ord); // Met à jour les totaux avec le montant de l'option cadeau
	}

	return $res;
}

/** Cette fonction récupère une ou plusieurs commandes, avec les informations de livraison, suivant des paramètres tous facultatifs
 *	Cette fonction à notamment l'avantage de pouvoir récupérer l'adresse de livraison même si celle-ci a été supprimée depuis
 *	@param int $user Facultatif, identifiant d'un client sur lequel filtrer le résultat
 *	@param int $id Facultatif, identifiant d'une commande sur laquelle filtrer le résultat
 *	@param $state Facultatif, identifiant d'un état de commande sur lequel filtrer le résultat (ou tableau)
 *	@param $piece Facultatif, numéro de pièce sur lequel filtrer le résultat (note : s'agit t-il d'un LIKE ou d'un égal ?)
 *	@param string $date_start Facultatif, date de début d'une période sur laquelle filtrer le résultat
 *	@param string $date_end Facultatif, date de fin d'une période sur laquelle filtrer le résultat
 *	@param $exclude_state Facultatif, tableau d'états dont les commandes doivent être exclues du résultat
 *	@param $origin Facultatif, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *	@param $gescom facultatif, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *	@param $fld Facultatif, paramètre de filtrage par champ avancé
 *	@param $or_between_val Facultatif, symbole logique entre les pv_value
 *	@param $or_between_fld Facultatif, symbole logique entre les champs
 *	@param string $lng Facultatif, langue pour la recherche sur les champs avancés traduits
 *	@param int|array $pay_id Facultatif, permet de spécifier un moyen de paiement spécifique (ou un tableau de moyens de paiement)
 *	@param int $wst Facultatif, identifiant ou tableau d'identifiants de site du locataire
 *	@param int $seller Facultatif, permet un filtrage par représentant (attention à spécifier le seller_id, pas le usr_id du représentant). Null permet de filtrer les commandes sans représentant
 *	@param bool $include_masked Facultatif, permet d'intégrer les commandes masquées au résultat
 *	@param int $parent_id Identifiant de la commande parent. Ignoré si $id est spécifié.
 *	@param int $pmt_id Optionnel, identifiant d'une promotion
 *	@param $sort Optionnel, paramètre de tri (actuellement disponible : date, ref, usr_adr_inv, par défaut sur date ou position si la recherche porte sur des paniers modèles)
 *	@param $ref_like Optionnel, permet de faire une recherche sur la référence de la commande ou le numéro de pièce
 *	@param int $store Optionnel, identifiant d'un magasin
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- user : Identifiant du client
 *		- id : Identifiant de la commande
 *		- date : Date de la commande, au format "jj/mm/aaaa à hh:mm"
 *		- date_en : Date de la commande, au format anglais
 *		- state_id : Identifiant de l'état de la commande
 *		- state_name : Nom de l'état de la commande
 *		- state_name_plural : Nom de l'état de la commande au pluriel
 *		- piece : Numéro de pièce
 *		- ref : Référence de la commande
 *		- str_id : Identifiant du magasin de livraison
 *		- srv_id : Identifiant du service de livraison
 *		- comments : Commentaires sur la commande
 *		- pmt_id : Identifiant de code promotion
 *		- total_ht : Montant total HT de la commande
 *		- total_ttc : Montant total TTC de la commande
 *		- opt-gift : Détermine si l'option cadeau est activée
 *		- opt-gift-msg : Détermine le message de l'option cadeau
 *		- pay_id : Identifiant du moyen de paiement
 *		- card_id : Identifiant du type de carte bleue (si paiement adéquat)
 *		- dlv-notes : Consignes de livraison
 *		- date_livr : Date de livraison estimée
 *		- date_livr_en : Date de livraison estimée, au format EN
 *		- dps : Identifiant du dépôt de livraison (commande fournisseur)
 *		- age : Age de la commande en jours
 *		- rly_id : Identifiant du point-relais de livraison
 *		- wst_id : Identifiant du site sur lequel la commande a été passée
 *		- products : Nombre de produits dans la commande
 *		- date_archived : Date d'archivation
 *		- seller_id : id de représentant
 *		- inv_id : Identifiant de l'adresse de facturation
 *		- inv_type_id : Identifiant du type de l'adresse de facturation
 *		- inv_title_name : Nom du type de l'adresse de facturation
 *		- inv_firstname : Prénom du client de facturation
 *		- inv_lastname : Nom du client de facturation
 *		- inv_society : Nom de société de facturation
 *		- inv_address1 : Adresse de facturation
 *		- inv_address2 : Complément d'adresse de facturation
 *		- inv_address3 : Suite Complément d'adresse de facturation
 *		- inv_postal_code : Code postal de facturation
 *		- inv_city : Ville de facturation
 *		- inv_country : Pays de facturation
 *		- inv_phone : Numéro de téléphone de facturation
 *		- inv_fax : Numérod e fax de facturation
 *		- inv_mobile : Numéro de téléphone mobile de facturation
 *		- inv_phone_work : Numéro de téléphone au travail de facturation
 *		- inv_email : Adresse email de l'adresse de facturation
 *		- inv_adr_desc : Description de l'adresse de facturation
 *		- dlv_id : Identifiant de l'adresse de livraison
 *		- dlv_type_id : Identifiant du type de l'adresse de livraison
 *		- dlv_title_name : Nom du type de l'adresse de livraison
 *		- dlv_firstname : Prénom de livraison
 *		- dlv_lastname : Nom de famille de livraison
 *		- dlv_society : Nomd e la société de livraison
 *		- dlv_address1 : Adresse de livraison
 *		- dlv_address2 : Complément d'adresse de livraison
 *		- dlv_address3 : Suite complément d'adresse de livraison
 *		- dlv_postal_code : Code postal de livraison
 *		- dlv_city : Ville de livraison
 *		- dlv_country : Pays de livraison
 *		- dlv_phone : Numéro de téléphone de livraison
 *		- dlv_fax : Numéro de fax de livraison
 *		- dlv_mobile : Numéro de téléphone mobile de livraison
 *		- dlv_phone_work : Numéro de téléphone au travail de livraison
 *		- dlv_email : Adresse email de l'adresse de livraison
 *		- pay_name : nom du moyen de paiement utilisé
 *		- dlv_adr_desc : Description de l'adresse de livraison
 *		- masked : commande masquée oui / non
 *		- parent_id : identifiant de la commande parent
 *		- contact_id : identifiant du contact
 *		- date_created : date de création de la commande
 *		- nsync : besoin d'être synchronisée ou non
 *		- currency : Monnaie utilisé sur la commande
 */
function ord_orders_get_with_adresses( $user=0, $id=0, $state=0, $piece='', $date_start=false, $date_end=false, $exclude_state=false, $origin=false, $gescom=null, $fld=false, $or_between_val=false, $or_between_fld=false, $lng=false, $pay_id=0, $wst=0, $seller=false, $include_masked = false, $parent_id = false, $pmt_id=0, $sort=false, $ref_like=false, $store = 0 ){
	global $config;

	{ // contrôle des paramètres
		$user_array = array();
		if( is_array($user) ){
			foreach( $user as $u ){
				if( !is_numeric($u) || $u<=0 ){ return false; }
				$user_array[] = $u;
			}
		}else{
			if( !is_numeric($user) || $user<0 ){ return false; }
			if( $user > 0 ){ $user_array[] = $user; }
		}

		if( !is_numeric($id) || $id<0 ){ return false; }

		$statearray = array();
		if( is_array($state) ){
			foreach( $state as $st ){
				if( !is_numeric($st) || $st<=0 ){
					return false;
				}
				$statearray[] = $st;
			}
		}else{
			if( !is_numeric($state) || $state<0 ){
				return false;
			}
			if( $state > 0 ){
				$statearray[] = $state;
			}
		}

		if( $date_start !== false && !isdateheure($date_start) ){
			return false;
		}

		if( $date_end !== false && !isdateheure($date_end) ){
			return false;
		}

		if( is_array($exclude_state) ){
			foreach( $exclude_state as $e ){
				if( !is_numeric($e) ){
					return false;
				}
			}
		}

		if( is_array($pay_id) ){
			foreach( $pay_id as $p ){
				if( !is_numeric($p) || $p<=0 ){ return false; }
			}
		}else{
			if( !is_numeric($pay_id) || $pay_id<0 ){ return false; }
			$pay_id = !$pay_id ? array() : array($pay_id);
		}

		$parent_id = is_numeric($parent_id) && $parent_id > 0 ? $parent_id : false;

		if( !is_numeric($pmt_id) || $pmt_id<0 ){
			return false;
		}

		$wst = control_array_integer( $wst, false );
		if( $wst === false ){
			return false;
		}

		$store = control_array_integer( $store, false );
		if( $store === false ){
			return false;
		}
	}

	// Préparation du SQL pour la ou les origines de commande
	$sub_origin = ord_orders_get_sql_origin( $origin );
	if( $sub_origin===false ){
		return false;
	}

	// infos générales
	$sql = '
		select
			ord_usr_id as user, ord_id as id, date_format(ord_date,"%d/%m/%Y à %H:%i") as date, state_id,
			ifnull(stn_name, state_name) as "state_name", state_name_plural, ord_piece as piece, ord_ref as ref,
			ord_str_id as str_id, ord_srv_id as srv_id, ord_comments as comments, ord_pmt_id as pmt_id, ord_total_ht as total_ht,
			ord_total_ttc as total_ttc, ord_opt_gift as "opt-gift", ord_opt_gift_message as "opt-gift-msg", ord_pay_id as pay_id,
			ord_card_id as card_id, ord_dlv_notes as "dlv-notes", date_format(ord_date_livr,"%d/%m/%Y à %H:%i") as date_livr,
			ord_date_livr as date_livr_en, ord_date as date_en, ord_dps_id as dps, ord_pkg_id as pkg_id, to_days(now())-to_days(ord_date) as age,
			ord_rly_id as rly_id, ord_wst_id as wst_id, ord_products as products, ord_date_archived as date_archived, ord_masked as masked,
			ord_parent_id as parent_id, ord_seller_id as seller_id, ord_contact_id as contact_id, ord_need_sync as nsync, ord_reseller_id as reseller_id,
			ord_reseller_contact_id as reseller_contact_id, ord_currency as currency,
	';

	// infos adresse de facturation
	$sql .= '
			ord_adr_invoices as inv_id, inv.adr_type_id as inv_type_id, inv_title.title_id as inv_title_id, inv_title.title_name as inv_title_name, inv.adr_firstname as inv_firstname,
			inv.adr_lastname as inv_lastname, inv.adr_society as inv_society, inv.adr_address1 as inv_address1, inv.adr_address2 as inv_address2, inv.adr_address3 as inv_address3,
			inv.adr_postal_code as inv_postal_code, inv.adr_city as inv_city, inv.adr_country as inv_country, inv.adr_phone as inv_phone,
			inv.adr_fax as inv_fax, inv.adr_mobile as inv_mobile, inv.adr_phone_work as inv_phone_work, inv.adr_email as inv_email,
			inv.adr_desc as inv_adr_desc,
	';

	// infos adresse de livraison
	$sql .= '
			ord_adr_delivery as dlv_id, dlv.adr_type_id as dlv_type_id, dlv_title.title_id as dlv_title_id,  dlv_title.title_name as dlv_title_name, dlv.adr_firstname as dlv_firstname,
			dlv.adr_lastname as dlv_lastname, dlv.adr_society as dlv_society, dlv.adr_address1 as dlv_address1, dlv.adr_address2 as dlv_address2, dlv.adr_address3 as dlv_address3,
			dlv.adr_postal_code as dlv_postal_code, dlv.adr_city as dlv_city, dlv.adr_country as dlv_country, dlv.adr_phone as dlv_phone,
			dlv.adr_fax as dlv_fax, dlv.adr_mobile as dlv_mobile, dlv.adr_phone_work as dlv_phone_work, dlv.adr_email as dlv_email, pay_name,
			dlv.adr_desc as dlv_adr_desc
	';

	if( sizeof($statearray)==1 && $statearray[0]==_STATE_MODEL ){
		$sql .= ', omd_pos as pos';
	}

	$sql .= '
		from riashop.ord_orders
	';

	if( sizeof($statearray)==1 && $statearray[0]==_STATE_MODEL ){
		$sql .= ' left join riashop.ord_models_position on ( ord_tnt_id=omd_tnt_id and ord_id=omd_ord_id )';
	}
	if( $pmt_id>0 ){
		$sql .= '	join riashop.ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)';
	}
	if( is_array($origin) && sizeof($origin) ){
		$sql .= ' join riashop.stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )';
	}elseif( $origin===-1 ){
		$sql .= ' left join riashop.stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )';
	}

	$sql .= '
			inner join riashop.ord_states on (ord_state_id=state_id)
			left join riashop.ord_states_name on ord_state_id=stn_stt_id and ord_tnt_id=stn_tnt_id and ord_wst_id=stn_wst_id
			left join riashop.gu_adresses as inv on ((0=inv.adr_tnt_id or ord_tnt_id=inv.adr_tnt_id) and ord_adr_invoices=inv.adr_id)
			left join riashop.gu_titles as inv_title on (inv.adr_title_id=inv_title.title_id)
			left join riashop.gu_adresses as dlv on ((0=dlv.adr_tnt_id or ord_tnt_id=dlv.adr_tnt_id) and ord_adr_delivery=dlv.adr_id)
			left join riashop.gu_titles as dlv_title on (dlv.adr_title_id=dlv_title.title_id)
			left join riashop.ord_payment_types on (ord_tnt_id=if(pay_tnt_id=0, ord_tnt_id,pay_tnt_id) and ord_pay_id=pay_id and pay_is_deleted=0)
	';

	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
		$sql .= ' left join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and usr_id = ord_usr_id)';
	}

	$sql .= '
		where ord_tnt_id='.$config['tnt_id'].' and ord_state_id!='._STATE_BASKET_CANCEL.'
	';

	if( !$include_masked ){
		$sql .= ' and ord_masked = 0';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	if( $gescom!==null ){
		if( $gescom ){
			$sql .= ' and ord_state_id>=3 and ord_pay_id is null';
		}else{
			$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
		}
	}

	if( sizeof($wst) ){
		$sql .= ' and ord_wst_id in ('.implode(', ', $wst).')';
	}
	if( sizeof($user_array) ){
		$sql .= ' and ord_usr_id in ('.implode(', ', $user_array).')';
	}
	if( $id && $parent_id ){
		$sql .= ' and ord_id = '.$id.' and ord_parent_id = '.$parent_id;
	}elseif( $id ){
		$sql .= ' and ord_id = '.$id;
	}elseif( $parent_id ){
		$sql .= ' and ord_parent_id = '.$parent_id;
	}else{
		// si aucun argument spécifié, les commandes enfants ne sont pas prises en compte
		$sql .= ' and ord_parent_id is null';
	}

	if( sizeof($statearray) ){
		$archivepos = array_search(_STATE_ARCHIVE, $statearray);
		if( $archivepos!==false ){
			unset( $statearray[$archivepos] );
		}
		if( $archivepos!==false && sizeof($statearray) ){
			$sql .= ' and ( ord_date_archived is not null or ord_state_id in ('.implode( ', ', $statearray ).') )';
		}elseif( $archivepos!==false ){
			$sql .= ' and ord_date_archived is not null';
		}elseif( sizeof($statearray) ){
			$sql .= ' and ord_state_id in ('.implode( ', ', $statearray ).')';
		}

	}elseif( !$id ){
		$sql .= ' and ord_state_id != '._STATE_MODEL;
	}

	if( $piece ){
		$sql .= ' and ord_piece like \''.addslashes($piece).'\'';
	}

	if( $date_start !== false ){
		if( isdate($date_start) ){
			$sql .= ' and date(ord_date) >= "'.dateparse($date_start).'"';
		}else{
			$sql .= ' and ord_date >= "'.dateheureparse($date_start).'"';
		}
	}

	if( $date_end !== false ){
		if( isdate($date_end) ){
			$sql .= ' and date(ord_date) <= "'.dateparse($date_end).'"';
		}else{
			$sql .= ' and ord_date <= "'.dateheureparse($date_end).'"';
		}
	}

	if( is_array($exclude_state) && sizeof($exclude_state) ){
		$archivepos = array_search(_STATE_ARCHIVE, $exclude_state);
		if( $archivepos!==false ){
			$sql .= ' and ord_date_archived is null';
			unset( $exclude_state[$archivepos] );
		}
		if( sizeof($exclude_state) ){
			$sql .= ' and ord_state_id not in ('.implode(',', $exclude_state).')';
		}
	}

	// Applique le filtre sur les moyens de paiement
	if( sizeof($pay_id) ){
		$sql .= ' and ord_pay_id in ('.implode(', ', $pay_id).')';
	}

	// Applique le filtre sur le point de vente
	if (count($store)){
		$sql .= ' and ord_str_id in ('.implode(',', $store).')';
	}

	// Si un utilisateur de type représentant est connecté, on applique automatiquement le filtre sur le seller_id pour qu'il ne puisse voir que ses commandes
	// et pas les autres. Ce filtre automatique ne s'applique que si nous sommes dans l'administration
	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
		if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
			$seller = $_SESSION['usr_seller_id'];
		}
	}

	// Contrôle le filtre sur le Représentant
	if( is_numeric($seller) && $seller > 0 ){
		$seller = array($seller);
	}

	// Applique le filtre sur le Représentant
	if( $seller === null ){
		$sql .= ' and ord_seller_id is null';
	}elseif( is_array($seller) && sizeof($seller) ){
		foreach( $seller as $sid ){
			if( !is_numeric($sid) || $sid <= 0 ){
				return false;
			}
		}

		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
			$sql .= ' and (
				(ord_seller_id in ('.implode(', ', $seller).') or ( ord_seller_id is null and usr_seller_id in ('.implode(', ', $seller).') ) )

				or exists (
					select 1
					from rel_relations_hierarchy
					where rrh_tnt_id = '.$config['tnt_id'].'
						and rrh_rrt_id = 2
						and rrh_src_0 in ('.implode(', ', $seller).')
						and rrh_src_1 = 0
						and rrh_src_2 = 0
						and rrh_dst_0 = ord_usr_id
						and rrh_dst_1 = 0
						and rrh_dst_2 = 0
				)

				or exists (
					select 1
					from rel_relations_objects
					where rro_tnt_id = '.$config['tnt_id'].'
						and rro_rrt_id = 2
						and rro_src_0 in ('.implode(', ', $seller).')
						and rro_src_1 = 0
						and rro_src_2 = 0
						and rro_dst_0 = ord_usr_id
						and rro_dst_1 = 0
						and rro_dst_2 = 0
				)
			)';
		}else{
			$sql .= ' and ord_seller_id in ('.implode(', ', $seller).')';
		}
	}

	if( $pmt_id ){
		$sql .= ' and oop_pmt_id='.$pmt_id;
	}

	if( trim($ref_like) != '' ){
		$sql .= ' and ( lower(ord_ref) like lower("%'.addslashes($ref_like).'%") or lower(ord_piece) like lower("%'.addslashes($ref_like).'%") )';
	}

	$sql .= fld_classes_sql_get( CLS_ORDER, $fld, $or_between_val, $or_between_fld, $lng );

	if( $sort==false || !is_array($sort) || sizeof($sort)==0 ){
		$sort = array();
	}

	$sort_final = array();
	foreach( $sort as $col=>$dir ){
		$col = strtolower(trim($col));
		$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';

		switch( $col ){
			case 'date' :
				array_push( $sort_final, 'ord_date '.$dir );
				break;
			case 'ref' :
				array_push( $sort_final, 'ord_ref '.$dir );
				break;
			case 'usr_adr_inv' :
				array_push( $sort_final, 'concat(inv.adr_lastname, inv.adr_firstname, inv.adr_society) '.$dir);
				break;
		}
	}

	if( sizeof($sort_final)==0 && (!is_numeric($id) || $id<=0) ){
		if( sizeof($statearray)==1 && $statearray[0]==_STATE_MODEL ){
			if( ord_models_order_get() ){
				$sort_final = array( 'ifnull(omd_pos, 0) asc, lower(ord_ref) asc');
			}else{
				$sort_final = array( 'lower(ord_ref) asc');
			}
		}else{
			$sort_final = array( 'ord_date desc' );
		}
	}

	if( is_array($sort_final) && sizeof($sort_final) ){
		$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	}

	if( $id ){
		$sql .= ' limit 0,1';
	}

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Cette fonction permet de récupérer toutes les commandes archivées.
 *	@param $date Optionnel, ignoré par défaut, permet de filtrer le résultat à partir d'une date
 *	@param $dir Optionnel, ignoré si $date est vide, par défaut on regarde "before" (<=) avant la date, mettre "after" (>=) pour après
 *	@return resource Un résultat MySQL contenant :
 *					- id : identifiant d'une commande
 *					- date_archived : date d'archivage d'une commande
 *					- cnt_id : identifiant de la commande dans le moteur de recherche
 */
function ord_orders_archived_get( $date='', $dir='before' ){
	if (trim($date) != '') {
		$date = dateheureparse( $date );

		if (!isdateheure($date)) {
			return false;
		}

		if (!in_array($dir, array('before', 'after'))) {
			return false;
		}
	}

	global $config;

	$sql = '
		select ord_id as id, ord_date_archived as date_archived, ord_cnt_id as cnt_id
		from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ifnull(ord_date_archived, "") != ""
	';

	if (trim($date) != '') {
		$sql .= '
			and (
				(ord_date_archived != "0000-00-00 00:00:00" and ord_date_archived '.( $dir == 'before' ? '<=' : '>=' ).' "'.addslashes( $date ).'")
				or
				(ord_date_archived = "0000-00-00 00:00:00" and ord_date '.( $dir == 'before' ? '<=' : '>=' ).' "'.addslashes( $date ).'")
			)
		';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction rattache une commande à un utilisateur donné. Elle est utilisée pour rattacher
 *	un panier à un compte utilisateur lors de sa connexion.
 *	@param int $ord Identifiant du panier
 *	@param int $usr Identifiant du compte utilisateur
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_orders_attach( $ord, $usr ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}
	if( !gu_users_exists($usr) ){
		return false;
	}

	$user = ria_mysql_fetch_array(gu_users_get($usr));
	$user['adr_delivery'] = gu_adresses_exists($user['adr_delivery']) ? $user['adr_delivery'] : $user['adr_invoices'];

	// On rattache la bonne devise sur la commande en fonction de la catégorie tarifaire du client
	$currency = 'EUR';

	// On récupère la devise utilisée par le client en fonction de sa catégorie tarifaire
	$usr_currency = gu_users_get_currency( $usr );
	if( trim($usr_currency) != '' ){
		$currency = $usr_currency;
	}

	return ria_mysql_query('
		update ord_orders
		set ord_usr_id = '.$user['id'].',
				ord_adr_invoices = '.$user['adr_invoices'].',
				ord_adr_delivery = '.$user['adr_delivery'].',
				ord_currency = "'.addslashes($currency).'"
		where ord_tnt_id = '.$config['tnt_id'].'
			and (ord_id = '.$ord.' or ord_parent_id = '.$ord.')
				and ord_usr_id is null
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction déttache une commande d'un utilisateur, il ne sera plus possible de le faire sur une commande au statut "Panier en cours de règlement CB".
*	@param int $ord Identifiant du panier
*	@return bool true en cas de succès
*	@return bool false en cas d'échec
*/
function ord_orders_dettach( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}
	return ria_mysql_query('
		update riashop.ord_orders set ord_usr_id=null, ord_adr_invoices=null, ord_adr_delivery=null
		where ord_tnt_id='.$config['tnt_id'].' and (ord_id='.$ord.' or ord_parent_id='.$ord.') and ord_state_id != '._STATE_BASKET_PAY_CB
	);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule un certain nombre de totaux sur des commandes dans une plage de temps donnée. L'état archivé
 *	n'a pas d'impact sur cette fonction, les commandes archivées sont systématiquement prises en compte si elles entrent
 *	dans la plage de temps ciblée.
 *	@param $states Optionnel, identifiant ou tableau d'identifiants de statuts sur lequels filtrer les commandes.
 *	@param $users Optionnel, identifiant ou tableau d'identifiants d'utilisateurs sur lequels filtrer les commandes.
 *	@param string $date_from Optionnel, date/heure de début du comptage. Cette date/heure est inclue dans le comptage
 *	@param string $date_to Optionnel, date/heure de fin du comptage. Cette date/heure est inclue dans le comptage
 *	@return array Un tableau comprenant les clés/valeurs suivantes :
 *			- count : nombre de commandes
 *			- total_ht : totaux hors taxes des commandes
 *			- total_ttc : totaux toutes taxes comprises
 */
function ord_orders_range_get_totals( $states=0, $users=0, $date_from=false, $date_to=false ){
	global $config;

	// Contrôle le paramètre $states
	$states = control_array_integer( $states, false );
	if( $states === false ){
		return false;
	}

	// Contrôle le paramètre $users
	$users = control_array_integer( $users, false );
	if( $users === false ){
		return false;
	}

	// Contrôle le paramètre $date_from
	if( $date_from!==false && !isdateheure($date_from) ){
		return false;
	}

	// Contrôle le paramètre $date_to
	if( $date_to!==false && !isdateheure($date_to) ){
		return false;
	}

	$sql = '
		select
			count(*) as "count",
			sum(ord_total_ht) as total_ht,
			sum(ord_total_ttc) as total_ttc
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_masked=0
	';

	// Filtre sur les états de commande
	if( sizeof($states) ){
		$sql .= ' and ord_state_id in ( '.implode(', ', $states).' )';
	}

	// Filtre sur les comptes utilisateurs
	if( sizeof($users) ){
		$sql .= ' and ord_usr_id in ('.implode(', ', $users).')';
	}

	// Filtre sur la date de début
	if( $date_from!==false && isdateheure($date_from) ){
		$sql .= ' and ord_date>="'.dateheureparse($date_from).'"';
	}

	// Filtre sur la date de fin
	if( $date_to!==false && isdateheure($date_to) ){
		$sql .= ' and ord_date<="'.dateheureparse($date_to).'"';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}
	return ria_mysql_fetch_array( $res );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'entête de commande pour les informations suivante (nombre de produit, quantité total, stats total_ht stats total_ttc et la marge).
 * 	@param int $ord_id Obligatoire, identifiant d'une commande
 * 	@param string $piece Facultatif, numéro de pièce de la commande dans la gestion commmerciale client. Si omis, il est chargé depuis la base de données.
 * 	@return bool true en cas de succès, false en cas d'erreur ou bien si les paramètres sont omis ou faux
 */
function ord_orders_update_header( $ord_id, $piece=false ){
	global $config;

	if (!is_numeric($ord_id) || $ord_id <= 0) {
		return false;
	}

	if ($piece === false) {
		$piece = ord_orders_get_piece( $ord_id );
	}

	$sql = '
		select
			ifnull( sum(
				( prd_price_ht - if(ifnull(op.prd_purchase_avg, ifnull(p.prd_purchase_avg, 0)) = 0, prd_price_ht, ifnull(op.prd_purchase_avg, p.prd_purchase_avg)))
				* prd_qte
				* if( "'.$piece.'" != "", 1, if(ifnull(prd_sell_weight, 0) = 1, ifnull(col_qte / ' . $config['weight_col_calc_lines'] . ', 1), ifnull(col_qte, 1)))
			), 0) as "marge",
			count(op.prd_id) as count_prd,
			ifnull( sum(prd_qte), 0) as sum_prd,
			ifnull( sum(prd_price_ht * prd_qte), 0) as total_ht,
			ifnull( sum(prd_price_ht * prd_tva_rate * prd_qte), 0) as total_ttc
		from riashop.ord_products as op
			join riashop.prd_products as p on (op.prd_tnt_id = p.prd_tnt_id and op.prd_id = p.prd_id)
			left join riashop.fld_object_values on (
				op.prd_tnt_id = pv_tnt_id and prd_ord_id = pv_obj_id_0 and op.prd_id = pv_obj_id_1 and prd_line_id = pv_obj_id_2
				and pv_lng_code = "'.$config['i18n_lng'].'" and pv_fld_id = '._FLD_PRD_COL_ORD_PRODUCT.'
			)
			left join riashop.prd_colisage_types on (pv_tnt_id = col_tnt_id and cast(pv_value as unsigned) = col_id and col_is_deleted = 0)
		where op.prd_tnt_id = '.$config['tnt_id'].'
			and op.prd_ord_id = '.$ord_id.'
	';

	$res = ria_mysql_query( $sql );

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );

	$r_update = ria_mysql_query('
		update riashop.ord_orders
		set ord_margin = '.$r['marge'].',
			ord_prd_count = '.$r['count_prd']. ',
			ord_date_modified = ord_date_modified
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if (!$r_update) {
		return false;
	}

	$sql_stats = $sql;

	if (isset($config["dlv_prd_references"]) && is_array($config["dlv_prd_references"]) && count($config["dlv_prd_references"])) {
		$sql_stats .= ' and op.prd_ref not in ("'.implode('", "', $config["dlv_prd_references"]).'")';
	}

	if (isset($config["ca_prd_ref_exclude"]) && is_array($config["ca_prd_ref_exclude"]) && count($config["ca_prd_ref_exclude"])) {
		$sql_stats .= ' and op.prd_ref not in ("'.implode('", "', $config["ca_prd_ref_exclude"]).'")';
	}

	$sql_stats .= '
		and not exists (
			select 1
			from riashop.fld_object_values
			where pv_tnt_id = '.$config['tnt_id'].'
				and pv_obj_id_0 = op.prd_id
				and pv_fld_id = '._FLD_PRD_EXCLUDE_FROM_STATS.'
				and pv_value = "Oui"
		)
	';

	$res = ria_mysql_query($sql_stats);
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_stats_products = '.$r['sum_prd'].',
			ord_stats_prd_count = '.$r['count_prd'].',
			ord_stats_total_ht = '.$r['total_ht'].',
			ord_stats_total_ttc = '.$r['total_ttc'].',
			ord_stats_margin = '.$r['marge']. ',
			ord_date_modified = ord_date_modified
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction calcule les totaux de commandes HT et TTC, nombre de compte client, et éventuellement la marge réalisée.
 *
 *	@param $state Optionnel, identifiant ou tableau d'identifiants de statuts sur lequels filtrer les commandes.
 *	@param int|array $user Optionnel, identifiant ou tableau d'identifiants d'utilisateurs sur lequels filtrer les commandes.
 *	@param int $id Optionnel, identifiant d'une commande sur lequel filtrer le résultat.
 *	@param $exclude_state Optionnel, identifiant ou tableau d'identifiants de statuts à exlure.
 *	@param $inc_marge Deprecated, permet d'activer le calcul de la marge. Ce paramètre n'a plus d'influence.
 *	@param $group_usr Optionnel, si activé et $user, $prf_id ou $seg_id spécifié, une ligne est retournée par client.
 *	@param int|array $prf_id Optionnel, identifiant ou tableau d'identifiants de profil client
 *	@param int $seg_id Optionnel, identifiant d'un segment client
 *	@param $with_port Optionnel, par défaut les frais de port sont inclus, mettre false pour les exclure
 *	@param int $seller Optionnel, identifiant ou tableau des comptes représentants sur lequel filtrer le résultat
 *	@param array $period Optionnel, période de récupération array('start'=> 0000-00-00, 'end' => 0000-00-00), ignoré par défaut
 *	@param int $wst_id Optionnel, identifiant ou tableau d'identifiants de sites
 *	@param int|array $pay_id Optionnel, identifiant ou tableau d'identifiants de moyens de paiement
 *	@param int $pmt_id Optionnel, identifiant ou tableau d'identifiants de promotions
 *	@param $origin Optionnel, origine des commandes (ignoré par défaut)
 *	@param $group_by_seller Optionnel, si true une ligne est retournée par représentant.
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- count : nombre de commandes
 *			- total_ht : totaux hors taxes des commandes
 *			- total_ttc : totaux toutes taxes comprises
 *			- total_margin_ht : marge totale HT
 *			- marge : marge moyenne HT par commande
 *			- [usr_id] : si $group_usr et $user, $prf_id ou $seg_id, identifiant du client de la ligne
 *			- [seller_id] : si $group_by_seller, identifiant du représentant des commandes
 *			- sum_qte : nombre de produits commandés (tient compte de la quantité)
 *			- count_prd : nombre de produits unique commandés
 */
function ord_orders_totals_get(
	$state=0, $user=0, $id=0, $exclude_state=0, $inc_marge=false, $group_usr=false, $prf_id=0, $seg_id=0, $with_port=true, $seller=false,
	$period=array(), $wst_id=0, $pay_id=0, $pmt_id=0, $origin=false, $group_by_seller=false
){

	$user = control_array_integer( $user, false );
	if( $user === false ){
		return false;
	}

	$state = control_array_integer( $state, false );
	if( $state === false ){
		return false;
	}

	$exclude_state = control_array_integer( $exclude_state, false );
	if( $exclude_state === false ){
		return false;
	}

	// traitement spécial de l'archivage
	$pos_archive = array_search(_STATE_ARCHIVE, $state);
	$get_archived = $pos_archive !== false;
	if( $get_archived ){
		unset($state[ $pos_archive ]);
	}

	$prf_id = control_array_integer( $prf_id, false );
	if( $prf_id === false ){
		return false;
	}

	if( !is_numeric($seg_id) || $seg_id<0 ){
		return false;
	}

	{ // Contrôle des périodes en paramètre
		$period = array(
			'start' => array_key_exists('start', $period) ? $period['start'] : '',
			'end' => array_key_exists('end', $period) ? $period['end'] : '',
		);

		if (trim($period['start']) != "" && !isdate($period['start'])  ){
			return false;
		}

		if (trim($period['end']) != "" && !isdate($period['end']) ){
			return false;
		}
	}

	$wst_id = control_array_integer($wst_id, false);
	if ($wst_id === false) {
		return false;
	}

	$pay_id = control_array_integer($pay_id, false);
	if ($pay_id === false) {
		return false;
	}

	$pmt_id = control_array_integer($pmt_id, false);
	if ($pmt_id === false) {
		return false;
	}

	{ // Préparation du SQL pour la ou les origines de commande
		$sub_origin = ord_orders_get_sql_origin($origin);

		if ($sub_origin === false) {
			return false;
		}
	}

	global $config;

	$pmt_ord_ids = false;
	if (count($pmt_id)) {
		$pmt_ord_ids = array();

		$res_pmt = ria_mysql_query('
			select oop_ord_id
			from riashop.ord_orders_promotions
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_pmt_id in ('.implode(', ', $pmt_id).')
		');

		if (!$res_pmt || !ria_mysql_num_rows($res_pmt)) {
			return false;
		}

		while ($r_pmt = ria_mysql_fetch_assoc($res_pmt)) {
			$pmt_ord_ids[] = $r_pmt['oop_ord_id'];
		}
	}

	$sql_group_usr = (sizeof($user) || sizeof($prf_id) || $seg_id > 0) && $group_usr && !$group_by_seller;


	$sql = '
		select
			'.( $sql_group_usr ? 'ord_usr_id as usr_id,' : '').' count(*) as "count", sum(ord_total_ht) as total_ht, sum(ord_total_ttc) as total_ttc,
			sum(ord_products) as nb_prds, count(DISTINCT ord_usr_id) as usersCount,
	';

	if ($group_by_seller) {
		$sql .= 'ord_seller_id as seller_id, ';
	}

	$sql .= ' sum(ord_margin) as "total_margin_ht",';
	if (!$with_port) {
		$sql .= ' avg(ord_stats_margin) as "marge", avg(ord_stats_products) as sum_qte, sum(ord_stats_prd_count) as count_prd';
	}else{
		$sql .= ' avg(ord_margin) as "marge", avg(ord_products) as sum_qte, sum(ord_prd_count) as count_prd';
	}

	$sql .= '
		from riashop.ord_orders
	';

	if( sizeof($prf_id) ){
		$sql .= ' join riashop.gu_users on (usr_tnt_id=ord_tnt_id and usr_id = ord_usr_id)';
	}

	if (is_array($origin) && sizeof($origin)) {
		$sql .= ' join riashop.stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )';
	} elseif ($origin === -1) {
		$sql .= ' left join riashop.stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )';
	}

	$sql .= '
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_masked = 0
	';

	$sql .= $sub_origin;

	{ // Période
		if (trim($period['start']) != '') {
			$sql .= ' and date(ord_date) >= "' . addslashes($period['start']) . '"';
		}

		if (trim($period['end']) != '') {
			$sql .= ' and date(ord_date) <= "' . addslashes($period['end']) . '"';
		}
	}

	if (count($wst_id)) {
		$sql .= ' and ord_wst_id in ('.implode(', ', $wst_id).')';
	}

	if (count($pay_id)) {
		$sql .= ' and ord_pay_id in ('.implode(', ', $pay_id).')';
	}

	if (is_array($pmt_ord_ids) && count($pmt_ord_ids)) {
		$sql .= ' and ord_id in ('.implode(', ', $pmt_ord_ids).')';
	}

	if( sizeof($state) ){
		$sql .= '
			and (
				ord_state_id in ( '.implode(', ', $state).' )
		';

		if( $get_archived ){
			$sql .= '
				or ord_date_archived is not null
			';
		}

		$sql .= '
			)
		';
	}elseif( $get_archived ){
		$sql .= ' and ord_date_archived is not null';
	}

	if( sizeof($exclude_state) ){
		$sql .= ' and ord_state_id not in ('.implode(', ', $exclude_state).')';
	}

	if( sizeof($user) ){
		$sql .= ' and ord_usr_id in ('.implode(', ', $user).')';
	}

	if( sizeof($prf_id) ){
		$sql .= ' and usr_prf_id in ('.implode(', ', $prf_id).')';
	}

	if( $seg_id > 0 ){
		$seg_usr_id = gu_users_get_by_segment( $seg_id );
		if( !is_array($seg_usr_id) || !count($seg_usr_id) ){
			$seg_usr_id = array(0);
		}

		$sql .= ' and ord_usr_id in ('.implode(', ', $seg_usr_id).')';
	}

	if( is_numeric($id) && $id > 0 ){
		$sql .= ' and ord_id = '.$id;
	}else{
		$sql .= ' and ord_parent_id is null';
	}
	$sellers = control_array_integer($seller);

	if( $sellers ){
		$sql .= ' and ord_seller_id in ('.implode(',', $sellers).') ';
	}

	if ($sql_group_usr) {
		$sql .= ' group by ord_usr_id';
	}elseif ($group_by_seller) {
		$sql .= ' group by ord_seller_id';
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/** Cette fonction permet de récupérer les consignes de livraisons d'une commande.
 *	@param int $id Obligatoire, identifiant d'une commande
 *	@return string Les consignes de livraison
 */
function ord_orders_get_dlv_notes( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_dlv_notes as dlv_notes
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'dlv_notes' );
}

/** Cette fonction permet de récupérer le commentaire d'une commande.
 *	@param int $id Obligatoire, identifiant d'une commande
 *	@return string Le commentaire sur la commande
 */
function ord_orders_get_comments( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_comments as comments
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['comments'];
}

// \cond onlydev
/// @}
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/** Cette fonction permet de récupérer le service de livraison utilisé pour une commande.
 *	@param int $id Obligatoire, identifiant d'une commande
 *	@return bool False si la commande n'existe pas ou si le service de livraison n'est pas renseigné, sinon l'identifiant du service
 */
function ord_orders_get_dlv_service( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_srv_id as srv
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'srv' );
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le moyen de paiement d'une commande.
 *	@param int $order Obligatoire, identifiant d'une commande
 *	@return bool False si la commande n'existe pas, sinon l'identifiant du moyen de paiement
 */
function ord_orders_get_pay_id( $order ){
	global $config;

	if( !is_numeric($order) || $order<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_pay_id as pay_id
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$order.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'pay_id' );
}
// \endcond

// \cond onlyria
/** Permet de récupérer le total ht ou ttc d'une commande
 *`	@param int $id Obligatoire, identifiant d'une commande
 *	@param bool $ttc Optionnel, par défaut on récupérère le total ht de la commande, mettre true pour retourner le ttc
 *	@param bool $no_invoiced Optionnel, si True, les lignes facturés sont déduites du total. Valeur par défaut : false
 *	@param array $exclude Optionnel, référence produit à exclure du total (le paramètre $no_invoiced est ignoré). Valeur par défaut : false
 *
 *	@return float Retourne le total ht ou ttc d'une commande
 *	@return bool Retourne false si le premier paramètre est omis ou bien si la commande n'existe pas
 */
function ord_orders_get_total( $id, $ttc=false, $no_invoiced=false, $exclude=false ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	// Si aucun article n'est à exclure du total
	if( !is_array($exclude) || !sizeof($exclude) ){
		$sql = 'select ord_total_ht as total_ht, ord_total_ttc as total_ttc from riashop.ord_orders where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$id;


		if( $no_invoiced ){
			// On retire du total de la commande le montant déjà facturé
			$sql = '
				select
					(ord_total_ht - ifnull(
						(select sum(prd_price_ht) from riashop.ord_inv_products where prd_tnt_id=ord_tnt_id and prd_ord_id=ord_id), 0
					)) as total_ht,
					(ord_total_ttc - ifnull(
						(select sum(ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate)) from riashop.ord_inv_products where prd_tnt_id=ord_tnt_id and prd_ord_id=ord_id), 0
					)) as total_ttc
					from riashop.ord_orders
					where ord_tnt_id='.$config['tnt_id'].'
						and ord_id='.$id.'
			';
		}
	}else{
		// On fait la somme des articles présents dans la commande en excluant ceux donnés en paramètre
		$sql = '
			select sum(prd_price_ht*prd_qte) as total_ht, sum(prd_price_ht*prd_tva_rate*prd_qte) as total_ttc
			from riashop.ord_products
			where prd_tnt_id='.$config['tnt_id'].' and prd_ord_id='.$id.'
				and prd_ref not in ("'.implode( '", "', $exclude ).'")
		';
	}

	$res = ria_mysql_query( $sql );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return $ttc ? ria_mysql_result( $res, 0, 'total_ttc' ) : ria_mysql_result( $res, 0, 'total_ht' );
}
// \endcond

/** Permet de tester l'existance d'une commande.
 *
 *	@param int $id Obligatoire, identifiant de commande à tester
 *	@param int $usr Facultatif, identifiant de l'utilisateur émetteur de la commande
 *	@param int|array $state Facultatif, identifiant permettant de restreindre le test aux seules commandes dans un état donné (exemple: 1-panier) ou tableau d'identifiants
 *	@param bool $not_masked Facultatif, par défaut on ne tient pas compte du paramètre masqué sur les commandes, mettre True pour vérifier que la commande n'est pas masquée
 *	@param int $wst_id Facultatif, identifiant d'un site web source sur lequel filtrer le résultat
 *
 *	@return bool true si la commande existe
 *	@return bool false si la commande n'existe pas ou si une erreur s'est produite.
 *
 */
function ord_orders_exists( $id, $usr=0, $state=0, $not_masked=false, $wst_id=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$usr = control_array_integer( $usr, false );
	if( $usr === false ){
		return false;
	}

	if( is_array($state) ){
		foreach( $state as $s ){
			if( !is_numeric($s) || $s<=0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($state) || $state<0 ){ return false; }
		if( $state==0 ){ $state = array(); }
		else{ $state = array( $state ); }
	}

	$sql = '
		select ord_id
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$id.'
	';
	if( $usr && sizeof($usr) ){
		$sql .= ' and ord_usr_id in ('.implode(',', $usr).')';
	}
	if( sizeof($state) ){
		$archivepos = array_search( _STATE_ARCHIVE, $state );
		if( $archivepos!==false ){
			unset( $state[$archivepos] );
		}
		if( $archivepos!==false && sizeof($state) ){
			$sql .= ' and ( ord_date_archived is not null or ord_state_id in ('.implode(', ', $state).') )';
		}elseif( $archivepos!==false ){
			$sql .= ' and ord_date_archived is not null';
		}elseif( sizeof($state) ){
			$sql .= ' and ord_state_id in ('.implode(', ', $state).')';
		}
	}

	if( $not_masked ){
		$sql .= ' and ord_masked!=1';
	}

	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and ord_wst_id = '.$wst_id;
	}

	$r = ria_mysql_query($sql);
	if( !$r ){
		return false;
	}

	return ria_mysql_num_rows($r);
}

// \cond onlyria
/** Permet de tester l'existence d'une commande, par son numéro de pièce
 *
 *	@param string $piece Numéro de piece de la commande à tester
 *
 *	@return bool true si la commande existe
 *	@return bool false si la commande n'existe pas ou si une erreur s'est produite.
 *
 */
function ord_orders_exists_piece( $piece ){
	global $config;

	if( !trim($piece) ){
		return false;
	}

	return ria_mysql_num_rows(ria_mysql_query(
		'select ord_id from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_piece=\''.addslashes($piece).'\''
	))>0;
}
// \endcond

// \cond onlyria
/** Permet de tester l'existence d'une commande, par sa référence
 *	@param string $ref Référence de la commande à tester
 *	@return bool true si la commande existe
 *	@return bool false si la commande n'existe pas ou si une erreur s'est produite.
 *
 */
function ord_orders_exists_ref( $ref ){
	global $config;
	if( !trim($ref) ) return false;
	$sql = 'select ord_id from riashop.ord_orders where ord_tnt_id='.$config['tnt_id'].' and ord_ref="'.addslashes($ref).'"';
	return ria_mysql_num_rows(ria_mysql_query($sql))>0;
}
// \endcond

// \cond onlyria
/**	Affiche une commande masquée dans la boutique
 *	@param int $id Identifiant interne de la commande
 *	@param bool $reverse Optionnel. Si true, masque la commande. Valeur par défaut : false
 *	@param bool $no_check Optionnel. Si true, n'applique pas un recalcul des totaux. Valeur par défaut : false
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_orders_unmask( $id, $reverse=false, $no_check=false ){
	global $config;

	if( !ord_orders_exists($id) ){
		return false;
	}

	$result = ria_mysql_query('
		update riashop.ord_orders set
			ord_masked='.( $reverse===true ? '1' : '0' ).'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$id
	);

	if( !$no_check && $reverse!==true ){
		ord_orders_update_totals( $id );
		ord_orders_update_header( $id );

		$rord = ord_orders_get( 0,$id );
		if( $rord!=false && ria_mysql_num_rows($rord) ){
			$ord = ria_mysql_fetch_array($rord);

			if( $ord['relanced']==1 ){

				$ritms = ord_installments_get( 0,1,$id );

				$itms_total = 0;
				if( $ritms!=false && ria_mysql_num_rows($ritms) ){
					while( $itm = ria_mysql_fetch_array($ritms) ){
						$itms_total += ( strtotime($itm['expire'])>time() ? $itm['total'] : $itm['spend'] );
					}
				}

				$solde = $ord['total_ttc'] - $itms_total;

				if( $solde>0.1 ){
					ord_installment_notify_order( $id );
					ord_orders_set_relanced( $id,false );
				}

			}
		}
	}

	if ($result && $reverse === false) {
		// Débite les points de fidélités si un produit de la commande appartient au catalogue du programme de fidélité.
		rwd_actions_apply('RWA_MANAGE_GIFT', CLS_ORDER, $id);
	}

	return $result;
}
// \endcond

// \cond onlyria
/** Teste la présence d'un produit dans une commande, par son identifiant.
 *
 *	@param int $order Obligatoire, identifiant de la commande
 *	@param int $prd Obligatoire, identifiant du produit à tester
 *	@param bool $include_childs Optionnel, True recherche sur toutes les lignes, False uniquement sur les lignes parents
 *	@param int $colisage Optionnel, identifiant d'un conditionnement sur lequel filtrer le résultat
 *
 *	@return bool true si le produit appartient à la commande, false dans les autres cas.
 *
 */
function ord_orders_contains_product( $order, $prd, $include_childs=false, $colisage=false ){
	global $config;

	if( !is_numeric($order) || $order<=0 ){
		return false;
	}
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	$sql = '
		select prd_id
		from riashop.ord_products
	';

	if( $colisage ){
		$sql .= '
			inner join riashop.fld_object_values on (
				pv_tnt_id=prd_tnt_id and prd_ord_id=pv_obj_id_0 and prd_id=pv_obj_id_1 and prd_line_id=pv_obj_id_2
			)
		';
	}

	$sql .= '
		where
			prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$order.'
			and prd_id='.$prd.'
	';

	if( $colisage ){
		$sql .= '
			and pv_fld_id='._FLD_PRD_COL_ORD_PRODUCT.'
			and pv_value='.$colisage.'
		';
	}

	if( $include_childs!=true ){
		$sql .= ' and prd_parent_id is null';
	}

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}
// \endcond

// \cond onlyria
/** Teste la présence d'un produit dans une commande, par sa référence.
 *
 *	@param int $order Obligatoire, identifiant de la commande
 *	@param int $prd Obligatoire, référence du produit à tester
 *	@param bool $include_childs Optionnel, True recherche sur toutes les lignes, False uniquement sur les lignes parents
 *	@param int $colisage Optionnel, identifiant d'un conditionnement sur lequel filtrer le résultat
 *
 *	@return bool true si le produit appartient à la commande, false dans les autres cas.
 *
 */
function ord_orders_contains_product_ref( $order, $prd, $include_childs=false, $colisage=false ){

	if( !( $rprd = prd_products_get( 0, $prd ) ) ) return false;
	if( !ria_mysql_num_rows($rprd) ) return false;
	$p = ria_mysql_fetch_array( $rprd );

	return ord_orders_contains_product( $order, $p['id'], $include_childs, $colisage );

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la transmission d'une commande d'un utilisateur à un autre.
 *	Son usage est réservé aux seuls administrateurs et représentants.
 *	@param int $order Obligatoire, identifiant de la commande
 *	@param int $user Obligatoire, identifiant du nouveau propriétaire de la commande
 *	@param bool $adr Facultatif, détermine si les adresses de facturation et de livraison doivent égalemetn être mises à jour
 *
 *	@return bool true en cas de succès, False en cas d'échec
 */
function ord_orders_update_user( $order, $user, $adr=true ){
	global $config;

	if( !ord_orders_exists($order) || !gu_users_exists($user) ){
		return false;
	}

	$rusr = gu_users_get($user);
	if( !$rusr || !ria_mysql_num_rows( $rusr ) ){
		return false;
	}

	$usr = ria_mysql_fetch_array( $rusr );
	$usr['adr_delivery'] = gu_adresses_exists($usr['adr_delivery']) ? $usr['adr_delivery'] : $usr['adr_invoices'];

	return ria_mysql_query('
		update riashop.ord_orders set
			ord_usr_id='.$usr['id'].( $adr ? ', ord_adr_invoices='.$usr['adr_invoices'].', ord_adr_delivery='.$usr['adr_delivery'] : '' ).'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$order
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction calcule les bénéfices réalisés par les ventes (commandes) dans un contexte donnée
 *	@param int $usr_id Optionnel, identifiant d'un utilisateur dont on souhaite connaitre les bénéfices qu'il a engrendré
 *	@param int|array $state Optionnel, identifiant d'un état de commande, ou tableau d'identifiants
 *	@param string $date_start Optionnel, date de début de prise en compte des commandes pour le calcul
 *	@param string $date_end Optionnel, date de fin de prise en compte des commandes pour le calcul
 *	@param int $wst_id Optionnel, identifiant d'un site en particulier (par défaut tous les sites)
 *	@param null|bool $web Optionnel, commandes passées sur la boutique [Null / True / False]
 *	@param null|bool $sync Optionnel, commandes synchronisées avec la gestion commerciale [Null / True / False]
 *	@param int|array $prd Optionnel, identifiant ou tableau d'identifiants de produits à prendre en compte (par défaut tous)
 *	@param int|array $ord_ids Optionnel, identifiant ou tableau d'identifiants de commandes à prendre en compte (par défaut tous)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- total_ht : montant total HT des bénéfices
 *		- total_ttc : montant total TTC des bénéfices
 *		- count_qte : quantité total achetée, tous produits confondus
 */
function ord_orders_get_profit( $usr_id=0, $state=0, $date_start=false, $date_end=false, $wst_id=0, $web=null, $sync=null, $prd=0, $ord_ids=0 ){
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	if( is_numeric($state) && $state > 0 ){
		$state = array($state);
	}elseif( is_array($state) ){
		foreach( $state as $s ){
			if( !is_numeric($s) || $s <= 0 ){
				return false;
			}
		}
	}else{
		$state = array();
	}

	$prd = control_array_integer($prd, false);
	if ($prd === false) {
		return false;
	}

	$ord_ids = control_array_integer($ord_ids, false);
	if ($ord_ids === false) {
		return false;
	}

	global $config;

	$sub_sql = 'prd_qte * if(ifnull(ord_piece, "") != "", 1, if(ifnull(p.prd_sell_weight, 0)=1, ifnull(col_qte / '.$config['weight_col_calc_lines'].', 1), ifnull(col_qte, 1)))';
	$lng = addslashes(strtolower(trim($config['i18n_lng'])));

	$sql = '
		select
			sum((prd_price_ht - ifnull(ifnull(op.prd_purchase_avg, p.prd_purchase_avg), prd_price_ht)) * ('.$sub_sql.')) as total_ht,
			sum((prd_price_ht - ifnull(ifnull(op.prd_purchase_avg, p.prd_purchase_avg), prd_price_ht)) * ('.$sub_sql.') * prd_tva_rate) as total_ttc,
			sum('.$sub_sql.') as count_qte
		from riashop.ord_orders
			join riashop.ord_products as op on ord_id = prd_ord_id and ord_tnt_id = prd_tnt_id
			left join riashop.prd_products as p on op.prd_id = p.prd_id and op.prd_tnt_id = p.prd_tnt_id
			left join riashop.fld_object_values on
				( op.prd_tnt_id = pv_tnt_id and prd_ord_id = pv_obj_id_0 and op.prd_id = pv_obj_id_1 and prd_line_id = pv_obj_id_2 and ifnull(pv_fld_id, '._FLD_PRD_COL_ORD_PRODUCT.') = '._FLD_PRD_COL_ORD_PRODUCT.' and ifnull(pv_lng_code, "'.$lng.'") = "'.$lng.'" )
			left join riashop.prd_colisage_types on op.prd_tnt_id = col_tnt_id and ifnull(pv_value, 0) = col_id and col_is_deleted = 0
		where
			ord_tnt_id = '.$config['tnt_id'].'
			and ord_masked = 0
	';

	if( $usr_id ){
		$sql .= ' and ord_usr_id = '.$usr_id;
	}

	if( sizeof($state) ){
		$sql .= ' and ord_state_id in ('.implode(', ', $state).')';
	}

	if( isdateheure($date_start) ){
		$sql .= ' and ord_date >= "'.dateheureparse($date_start).'"';
	}

	if( isdateheure($date_end) ){
		$sql .= ' and ord_date <= "'.dateheureparse($date_end).'"';
	}

	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and ord_wst_id = '.$wst_id;
	}

	if( $web !== null ){
		if( $web ){
			$sql .= ' and ord_pay_id is not null';
		}else{
			$sql .= ' and ord_pay_id is null';
		}
	}

	if( $sync !== null ){
		if( $sync ){
			$sql .= ' and ord_piece != ""';
		}else{
			$sql .= ' and ord_piece = ""';
		}
	}

	if( sizeof($prd) ){
		$sql .= ' and op.prd_id in ('.implode(', ', $prd).')';
	}

	if( sizeof($ord_ids) ){
		$sql .= ' and ord_id in ('.implode(', ', $ord_ids).')';
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria

/**
 * Cette fonction permet de générer un bout de sql pour avoir le colisage ou non dans le calcule des totaux de ligne produit ou de commandes
 *
 * @param string $piece Numéro de la pièce de la commande peux être une chaine vide
 * @param string $product_prefix Facultatif, Le préfix utilisé pour la table ord_products dans la requete principale
 *
 * @return string Retourne le sous sql pour la prise en compte du colisage sur les totaux des lignes ou des commandes
 */
function ord_orders_piece_sub_sql($piece, $product_prefix=''){
	global $config;

	$piece_sql = trim($piece).' != "" ';
	if( $config['ord_orders_piece_tmp_used'] ){
		$piece_sql .= ' and '.trim($piece).' not like "TMP%" ';
	}
	if( $config['ord_prd_line_is_sync_used'] ){
		$piece_sql .= ' and '.$product_prefix.'prd_line_is_sync = 1 ';
	}

	return $piece_sql;
}

/**
 * Cette fonction permet de générer un bout de sql pour avoir le colisage ou non dans le calcule des totaux de ligne produit ou de commandes
 *
 * @param string $piece Numéro de la pièce de la commande peux être une chaine vide
 * @param string $ratio Le ratio pour les convertions quantité poid via weight_col_calc_lines
 * @param string $product_prefix Facultatif, Le préfix utilisé pour la table ord_products dans la requete principale
 * @param boolean $with_prd_qte Facultatif, permet d'ajoute "prd_qte *" a la sous requete
 *
 * @return string Retourne le sous sql pour la prise en compte du colisage sur les totaux des lignes ou des commandes
 */
function ord_orders_colisage_sub_sql($piece, $ratio, $product_prefix='', $with_prd_qte=true){
	global $config;

	$piece_sql = ord_orders_piece_sub_sql($piece, $product_prefix);

	$sub_sql = 'if('.$piece_sql.', 1, if( ifnull(p.prd_sell_weight, 0)=1, ifnull(col_qte/'.$ratio.', 1), ifnull(col_qte,1) ))';

	if( $with_prd_qte ){
		$sub_sql = 'prd_qte * '.$sub_sql;
	}

	return $sub_sql;
}
// \endcond
// \cond onlyria
/**
 * Cette fonction permet de calculer le total ht, ttc d'une commande
 * @param int $ord_id Identifiant de la commande
 *
 * @return resource Retourne un résultat mysql avec les colonnes suivante
 * 					- total_ht
 * 					- total_ttc
 * 					- products => nombre de lignes produits
 */
function ord_orders_calculate_totals($ord_id){
	// Récupère la commande même si elle est masquée.
	$r_order = ord_orders_get_masked($ord_id);

	if( !$r_order || !ria_mysql_num_rows($r_order) ){
		return false;
	}

	global $config;

	$ord = ria_mysql_fetch_assoc($r_order);
	$prc = isset($_SESSION['usr_prc_id']) && prd_prices_categories_exists($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : $config['default_prc_id'];
	if( $ord['user'] ){
		if( $rusr = gu_users_get($ord['user']) ){
			if( $usr_data = ria_mysql_fetch_array($rusr) ){
				$prc = $usr_data['prc_id'];
			}
		}
	}

	// Détermine si le tarif TTC doit être arrondi (suivant si le client est HT ou non).
	$round_ttc = prd_prices_categories_get_ttc($prc);

	$ratio = isset($config['weight_col_calc_lines']) && is_numeric($config['weight_col_calc_lines']) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;

	$sub_sql = ord_orders_colisage_sub_sql('"'.addslashes($ord['piece']).'"', $ratio, 'op.');

	$round_ttc_count = isset($config['round_digits_count_header']) && $config['round_digits_count_header'] > -1 ? $config['round_digits_count_header'] : $config['round_digits_count'];
	$round_dec = $round_ttc_count;
	if( $ord['piece']!='' && $config['tnt_id']==13 ){
		$round_dec = '( if(ifnull(p.prd_sell_weight, 0) = 1, '.$config['round_digits_count'].' + log10('.$ratio.'), '.$round_ttc_count.') )';
	}

	$sum_ht = 'sum( prd_price_ht * ('.$sub_sql.') )';
	$sum_ttc = 'sum( '.($round_ttc ? 'round(' : '' ).'ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate)'.($round_ttc ? ','.$round_dec.')' : '').' * ('.$sub_sql.') ) ';

	$round_after_qte = isset($config['round_digits_after_qte']) && $config['round_digits_after_qte'];
	if( $round_after_qte ){
		$sum_ht = 'sum( round(prd_price_ht * ('.$sub_sql.'),'.$round_dec.') ) ';
		$sum_ttc = 'sum( round(ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) * ('.$sub_sql.'),'.$round_dec.') ) ';
	}

	$sql = '
		select
			('.$sum_ht.') as total_ht,
			('.$sum_ttc.') as total_ttc,
			sum( '.$sub_sql.' ) as products
		from
			riashop.ord_products as op
				left join riashop.prd_products as p on ( op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
				left join riashop.fld_object_values on ( op.prd_tnt_id=pv_tnt_id and prd_ord_id=pv_obj_id_0 and op.prd_id=pv_obj_id_1 and prd_line_id=pv_obj_id_2 and pv_fld_id='._FLD_PRD_COL_ORD_PRODUCT.' )
				left join riashop.prd_colisage_types on ( op.prd_tnt_id=col_tnt_id and ifnull(pv_value, 0)=col_id and col_is_deleted = 0 )
		where
			op.prd_tnt_id='.$config['tnt_id'].'
	';

	if( $ord['parent_id'] ){
		$sql .= ' and prd_ord_child_id = '.$ord_id;
	}else{
		$sql .= ' and prd_ord_id = '.$ord_id;
	}

	return ria_mysql_query($sql);
}
/** Recalcule le total ht et ttc de la commande, et met à jour la commande.
 *	Cette fonction est à utilisée après chaque modification du contenu de la commande.
 *	@param int $order Identifiant de la commande à mettre à jour (peut être un identifiant de commande enfant pour le calcul d'un sous-total).
 *	@param array $force_off Optionnel, tableau associatif permettant de forcer ou substituer un code promotion. Il est composé des clés suivantes :
 *		- discount : obligatoire, montant de la remise.
 *		- apply_on : obligatoire, mode d'application de la remise (order, min-prd, max-prd, min-line, max-line).
 *		- discount_type : obligatoire, type de remise (0 pour montant, 1 pour pourcentage).
 *		- prd_list : optionnel, tableau des identifiants de produit inclus dans la promotion. Si vide ou non spécifié, tous les produits sont inclus.
 *		- free_shipping : optionnel, frais de port offerts ou non.
 *		- tva_rate : optionnel, taux de TVA
 *	@param int $reward Optionnel, identifiant d'une config de programme de fidélité ou directement la config complète
 *	@param bool $apply_promo Optionnel, par défaut les promotions sont prises en compte dans le calcul des totaux, mettre faux pour les ignorer
 *			(commande déjà synchronisée avec remise appliquée sur chaque ligne de produit).
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_update_totals( $order, $force_off=false, $reward=false, $apply_promo=true ){
	// Récupère la commande même si elle est masquée.
	$r_order = ord_orders_get_masked($order);

	if( !$r_order || !ria_mysql_num_rows($r_order) ){
		return false;
	}

	$ord = ria_mysql_fetch_assoc($r_order);

	if( $force_off !== false ){
		if( !is_array($force_off) ){
			return false;
		}

		if( !isset($force_off['discount'], $force_off['discount_type'], $force_off['apply_on']) ){
			return false;
		}

		if( !in_array($force_off['apply_on'], array('order', 'min-prd', 'max-prd', 'min-line', 'max-line')) ){
			return false;
		}

		if( !is_numeric($force_off['discount_type']) || $force_off['discount_type'] < 0 ){
			return false;
		}

		$force_off['discount'] = str_replace(array(' ', ','), array('', '.'), $force_off['discount']);

		if( !is_numeric($force_off['discount']) ){
			return false;
		}

		if( !isset($force_off['prd_list']) ){
			$force_off['prd_list'] = array();
		}

		$force_off['prd_list'] = control_array_integer($force_off['prd_list'], false);

		if( $force_off['prd_list'] === false ){
			return false;
		}

		$force_off['free_shipping'] = isset($force_off['free_shipping']) ? $force_off['free_shipping'] : false;
		$force_off['tva_rate'] = ria_array_get($force_off, 'tva_rate', _TVA_RATE_DEFAULT);
	}

	global $config;

	// Charge la catégorie tarifaire à partir du compte client
	$prf = isset($_SESSION['usr_prf_id']) && is_numeric($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] ? $_SESSION['usr_prf_id'] : false;
	$prc = isset($_SESSION['usr_prc_id']) && prd_prices_categories_exists($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : $config['default_prc_id'];
	if( $ord['user'] ){
		if( $rusr = gu_users_get($ord['user']) ){
			if( $usr_data = ria_mysql_fetch_array($rusr) ){
				$prc = $usr_data['prc_id'];
				$prf = $usr_data['prf_id'];
			}
		}
	}

	// Détermine si le tarif TTC doit être arrondi (suivant si le client est HT ou non).
	$round_ttc = prd_prices_categories_get_ttc($prc);
	$ord_update_totals_with_ttc = !isset($config['ord_update_totals_with_ttc']) || $config['ord_update_totals_with_ttc'];

	$ratio = isset($config['weight_col_calc_lines']) && is_numeric($config['weight_col_calc_lines']) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;

	$sub_sql = ord_orders_colisage_sub_sql('"'.addslashes($ord['piece']).'"', $ratio, 'op.');

	$round_ttc_count = isset($config['round_digits_count_header']) && $config['round_digits_count_header'] > -1 ? $config['round_digits_count_header'] : $config['round_digits_count'];
	$round_dec = $round_ttc_count;
	if( $ord['piece']!='' && $config['tnt_id']==13 ){
		$round_dec = '( if(ifnull(p.prd_sell_weight, 0) = 1, '.$config['round_digits_count'].' + log10('.$ratio.'), '.$round_ttc_count.') )';
	}

	$prd_price_ht = 'prd_price_ht';
	if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
		$prd_price_ht = 'round( (prd_price_ht * (1 - (prd_discount / 100))), 2)';
	}

	$sum_ht = 'sum( '.$prd_price_ht.' * ('.$sub_sql.') )';
	$sum_ttc = 'sum( '.($round_ttc ? 'round(' : '' ).'ifnull('.($ord_update_totals_with_ttc ? 'prd_price_ttc' : 'null').', '.$prd_price_ht.' * prd_tva_rate)'.($round_ttc ? ','.$round_dec.')' : '').' * ('.$sub_sql.') ) ';

	$round_after_qte = isset($config['round_digits_after_qte']) && $config['round_digits_after_qte'];
	if( $round_after_qte ){
		$sum_ht = 'sum( round('.$prd_price_ht.' * ('.$sub_sql.'),'.$round_dec.') ) ';
		$sum_ttc = 'sum( round(ifnull('.($ord_update_totals_with_ttc ? 'prd_price_ttc' : 'null').', '.$prd_price_ht.' * prd_tva_rate) * ('.$sub_sql.'),'.$round_dec.') ) ';
	}

	$sql = '
		select
			('.$sum_ht.') as total_ht,
			('.$sum_ttc.') as total_ttc,
			sum( '.$sub_sql.' ) as products,
			ifnull( sum(
				( '.$prd_price_ht.' - if(ifnull(op.prd_purchase_avg, ifnull(p.prd_purchase_avg, 0)) = 0, '.$prd_price_ht.', ifnull(op.prd_purchase_avg, p.prd_purchase_avg)))
				* prd_qte
				* if( "'.$ord['piece'].'" != "", 1, if(ifnull(prd_sell_weight, 0) = 1, ifnull(col_qte / ' . $config['weight_col_calc_lines'] . ', 1), ifnull(col_qte, 1)))
			), 0) as "margin"
		from
			riashop.ord_products as op
				left join riashop.prd_products as p on ( op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
				left join riashop.fld_object_values on ( op.prd_tnt_id=pv_tnt_id and prd_ord_id=pv_obj_id_0 and op.prd_id=pv_obj_id_1 and prd_line_id=pv_obj_id_2 and pv_fld_id='._FLD_PRD_COL_ORD_PRODUCT.' )
				left join riashop.prd_colisage_types on ( op.prd_tnt_id=col_tnt_id and ifnull(pv_value, 0)=col_id and col_is_deleted = 0 )
		where
			op.prd_tnt_id='.$config['tnt_id'].'
	';

	if( $ord['parent_id'] ){
		$sql .= ' and prd_ord_child_id = '.$order;
	}else{
		$sql .= ' and prd_ord_id = '.$order;
	}

	$result_final = false;

	$product_promotion_ids = array();

	if( $r_totals = ria_mysql_query($sql) ){
		// Totaux produits
		$totals = ria_mysql_fetch_array($r_totals);
		$total_ht = $totals['total_ht'] ? $totals['total_ht'] : 0;
		$total_ttc = $totals['total_ttc'] ? $totals['total_ttc'] : 0;
		$prd_count = $totals['products'] ? $totals['products'] : 0;
		$gross_margin = $totals['margin'] ? $totals['margin'] : 0;

		if( $prd_count<0 ){
			$prd_count = $prd_count * -1;
		}

		$port_ht = $port_ttc = 0;

		$sum_ht = 'sum(op.prd_price_ht*op.prd_qte) ';
		$sum_ttc = 'sum( '.($round_ttc ? 'round(' : '' ).'ifnull(op.prd_price_ttc, op.prd_price_ht * op.prd_tva_rate)'.($round_ttc ? ', '.$round_dec.')' : '').' * op.prd_qte ) ';
		if( $round_after_qte ){
			$sum_ht = 'sum( round(op.prd_price_ht * op.prd_qte, '.$round_dec.') ) ';
			$sum_ttc = 'sum( round(ifnull(op.prd_price_ttc, op.prd_price_ht * op.prd_tva_rate) * op.prd_qte, '.$round_dec.') ) ';
		}

		$sql_port = '
			select
				'.$sum_ht.' as total_ht,
				'.$sum_ttc.' as total_ttc
			from
				riashop.ord_products as op
				left join riashop.prd_products as p on ( op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
			where
				op.prd_tnt_id='.$config['tnt_id'].'
				and op.prd_parent_id is null
				and op.prd_ref in (\''.implode('\',\'', $config['dlv_prd_references']).'\')
		';

		if( $ord['parent_id'] ){
			$sql_port .= ' and prd_ord_child_id = '.$order;
		}else{
			$sql_port .= ' and prd_ord_id = '.$order;
		}

		// Totaux Frais de port
		if( $p_totals = ria_mysql_query( $sql_port ) ){
			$port_ht = ria_mysql_result($p_totals,0,0);
			$port_ttc = ria_mysql_result($p_totals,0,1);
		}

		$ord_pmt = false;

		if( $apply_promo ){
			$ord_pmt = ord_orders_promotions_get($ord['id']);
		}

		if( $force_off || is_array($ord_pmt) ){
			if( $force_off && empty($ord_pmt) ){
				$ord_pmt[] = 1;
			}

			foreach($ord_pmt as $pmt_id){
				if(!$force_off && !($rpmt = pmt_codes_get($pmt_id )) ){
					continue;
				}

				if( !$force_off && !($pmt = ria_mysql_fetch_assoc($rpmt)) ){
					continue;
				}

				$off_type = $force_off ? _PMT_TYPE_CODE : $pmt['type'];

				switch( $off_type ){
					case _PMT_TYPE_CODE:
					case _PMT_TYPE_CREDIT:
					case _PMT_TYPE_REWARD:
					case _PMT_TYPE_BA:
					case _PMT_TYPE_CHEEKBOOK:
					case _PMT_TYPE_GIFTS:
						if( !$force_off ){
							$roff = pmt_offers_get($pmt['parent'] ? $pmt['parent'] : $pmt['id']);

							if( $roff && ria_mysql_num_rows($roff) ){
								$off = ria_mysql_fetch_assoc($roff);
							}
						}else{
							$off = $force_off;
						}

						if( $off ){
							// Détermine si la commande est entièrement en HT ou non
							$ord_in_ht = fld_object_values_get($ord['id'], _FLD_ORD_IN_HT, '', false, true);

							$products = ria_mysql_query('
								select op.prd_id as id, op.prd_line_id as line, op.prd_ref as ref, op.prd_price_ht as price_ht, ('.$sub_sql.') as qte, (prd_price_ht*('.$sub_sql.')) as total_ht, (op.prd_price_ht*op.prd_tva_rate*('.$sub_sql.')) as total_ttc, (op.prd_price_ht*op.prd_tva_rate) as price_ttc, op.prd_tva_rate as tva_rate, op.prd_discount as discount, op.prd_discount_type as discount_type, op.prd_ord_id as ord_id
								from riashop.ord_products as op
									left join riashop.prd_products as p on ( op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
									left join riashop.fld_object_values on ( op.prd_tnt_id=pv_tnt_id and prd_ord_id=pv_obj_id_0 and op.prd_id=pv_obj_id_1 and prd_line_id=pv_obj_id_2 and pv_fld_id='._FLD_PRD_COL_ORD_PRODUCT.' )
									left join riashop.prd_colisage_types on ( op.prd_tnt_id=col_tnt_id and ifnull(pv_value, 0)=col_id and col_is_deleted = 0 )
								where op.prd_tnt_id='.$config['tnt_id'].'
									and ( op.prd_ord_id='.$ord['id'].' or op.prd_ord_child_id = '.$ord['id'].' )
									'.( isset($config['pmt_exclude_prd_total']) && is_array($config['pmt_exclude_prd_total']) && sizeof($config['pmt_exclude_prd_total']) ? ' and op.prd_ref not in (\''.implode( '\', \'', $config['pmt_exclude_prd_total'] ).'\')' : '' ).'
							');

							if( empty($ord['piece']) || !$config['ord_pmt_sync_update'] ){
								if( $off['apply_on'] == 'order' ){
									if( $off['discount_type'] == 0 ){ // Remise en valeur
										// Contrôle que la commande contient au moins un produit :
										// qui n'est pas un don LPO
										// qui n'est pas un FDP
										// qui est inclus dans le code promo
										$sub_total_ht = 0;

										while( $prd = ria_mysql_fetch_assoc($products) ){
											if( $prd['price_ht'] <= 0 ){
												continue;
											}

											if( ($config['tnt_id'] != 5 || !prd_products_categories_exists($prd['id'], 408)) && !prd_products_is_port($prd['ref']) ){
												$prd_in = false;

												if( $force_off ){
													$prd_in = count($force_off['prd_list']) > 0 ? in_array($prd['id'], $force_off['prd_list']) : true;
												}else{
													$prd_in = pmt_products_is_included($pmt, $prd['id'], $ord['user'] ?: 0, $prd['qte']);
												}

												if( $prd_in ){
													$sub_total_ht += $prd['total_ht'];

													// Enregistre les promotions applicables dans un tableau.
													if( !array_key_exists($prd['id'], $product_promotion_ids) ){
														$product_promotion_ids[$prd['id']] = array(
															'line_id' => $prd['line'],
															'promotion_ids' => array(),
														);
													}

													$product_promotion_ids[$prd['id']]['promotion_ids'][] = $pmt['id'];
												}
											}

											$remise_ht = 0;

											// Récupère la remise sur le champ avancé.
											$fld_discount = fld_object_values_get(array($prd['ord_id'], $prd['id'], $prd['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

											// Si renseigné, passe en priorité sur la remise de la ligne de commande. Sinon, applique la remise spéciale sur la ligne de commande si renseignée
											if( (!is_numeric($fld_discount) || $fld_discount <= 0) && $prd['discount'] ){
												$discount = $prd['discount'];
												$discount_type = $prd['discount_type'];

												if( $discount_type ){ // Si 1, alors promotion en %
													$remise_ht = $prd['total_ht'] * ($discount / 100);
												}else{ // Sinon 0 donc remise en €
													$remise_ht = $discount;
												}
											}
										}
										if( $sub_total_ht > 0 ){
											// Remise en euro, ne peut excéder $sub_total_ht
											$real_disc = $sub_total_ht < $off['discount'] ? $sub_total_ht : $off['discount'];
											$real_disc += $remise_ht > ($total_ht - $real_disc) ? ($total_ht - $real_disc) : $remise_ht;
											if( $round_after_qte ){
												$total_ht -= round($real_disc, $round_dec);
											}else{
												$total_ht -= $real_disc;
											}

											$tmp_tva_rate = in_array( $ord_in_ht, array('Oui', 'oui', '1') ) ? 1.000 : $off['tva_rate'];
											if( $round_ttc ){
												if( $round_after_qte ){
													$total_ttc -= round($real_disc * $tmp_tva_rate, $round_dec);
												}else{
													$total_ttc -= $real_disc * $tmp_tva_rate;
												}
											}else{
												$total_ttc -= $real_disc * $tmp_tva_rate;
											}
										}
									}else{ // Remise en pourcentage
										while( $prd = ria_mysql_fetch_array($products) ){
											if( !prd_products_is_port($prd['ref']) && ($config['tnt_id'] != 5 || !prd_products_categories_exists($prd['id'], 408)) ){
												$prd_in = false;

												if( $force_off ){
													$prd_in = sizeof($force_off['prd_list']) ? in_array($prd['id'], $force_off['prd_list']) : true;
												}else{
													$prd_in = pmt_products_is_included($pmt, $prd['id'], $ord['user'] ?: 0, $prd['qte']);
												}

												if( $prd_in ){
													$discount = $off['discount'];

													// Enregistre les promotions applicables dans un tableau.
													if( !array_key_exists($prd['id'], $product_promotion_ids) ){
														$product_promotion_ids[$prd['id']] = array(
															'line_id' => $prd['line'],
															'promotion_ids' => array(),
														);
													}

													$product_promotion_ids[$prd['id']]['promotion_ids'][] = $pmt['id'];

													// spec bigship ponctuelle : code promo ou le taux de remise diffère selon le produit
													if( isset($pmt) && $config['tnt_id'] == 1 && in_array( substr( strtoupper( $pmt['code'] ), 0, 9 ), array('HEOBALISE', 'HEOSURVIE') ) ){
														$promo_single = fld_object_values_get( $prd['id'], 2532, '', false, true );
														$promo_single = str_replace( array(',', ' '), array('.', ''), $promo_single );
														if( is_numeric($promo_single) && $promo_single <= 100 ){
															$discount = $promo_single;
														}
													}

													if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
														$remise_ht = 0;

														// Récupère la remise sur le champ avancé.
														$fld_discount = fld_object_values_get(array($prd['ord_id'], $prd['id'], $prd['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

														// Si renseigné, passe en priorité sur la remise de la ligne de commande. Sinon, applique la remise spéciale sur la ligne de commande si renseignée
														if( (!is_numeric($fld_discount) || $fld_discount <= 0) && $prd['discount'] ){
															$line_discount = $prd['discount'];
															$line_discount_type = $prd['discount_type'];

															if( $line_discount_type ){ // Si 1, alors promotion en %
																$remise_ht = round( $prd['price_ht'] * ($line_discount / 100), 2);
															}else{ // Sinon 0 donc remise en €
																$remise_ht = $line_discount;
															}
														}

														if( $remise_ht > 0 ){
															$prd['price_ht']  -= $remise_ht;
															$prd['price_ttc'] -= $remise_ht * $prd['tva_rate'];
														}
													}

													if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
														$ecotaxe = prd_products_get_ecotaxe( $prd['id'] );
														$ecotaxe_ttc = $ecotaxe * $prd['tva_rate'];

														$prd['price_ht']  -= $ecotaxe;
														$prd['price_ttc'] -= $ecotaxe_ttc;
													}

													if( $round_after_qte ){
														$total_ht -= round($prd['price_ht'] * $prd['qte'] * $discount / 100, $round_dec);
													}else{
														$total_ht -= ( $prd['price_ht'] * $prd['qte'] * $discount / 100 );
													}

													if( $round_ttc ){
														if( $round_after_qte ){
															$total_ttc -= round($prd['price_ttc'] * $prd['qte'] * $discount / 100, $round_dec);
														}else{
															$total_ttc -= round($prd['price_ttc'], $round_dec) * $prd['qte'] * $discount / 100;
														}
													}else{
														$total_ttc -= ( $prd['price_ttc'] * $prd['qte'] * $discount / 100 );
													}
												}
											}
										}
									}
								}else{
									$rem_qty = 1;
									$old = $old_ttc = $old_eco = $old_eco_ttc = false;
									$selected_product = array(
										'id' => null,
										'line_id' => null,
									);

									while( $prd = ria_mysql_fetch_array($products) ){
										$prd_in = false;

										if( $force_off ){
											$prd_in = count($force_off['prd_list']) ? in_array($prd['id'], $force_off['prd_list']) : true;
										}else{
											$prd_in = pmt_products_is_included($pmt, $prd['id'], ( $ord['user'] ? $ord['user'] : 0 ), $prd['qte'], 0, true);
										}

										if (!$prd_in) {
											continue;
										}

										if( !prd_products_is_port( $prd['ref'] ) && ( $config['tnt_id']!=5 || !prd_products_categories_exists( $prd['id'], 408 ) ) ){
											$pass_here = false;

											switch( $off['apply_on'] ){
												case 'min-prd' :
													if( !$old || $old > $prd['price_ht'] ){
														$old = $prd['price_ht'];
														$old_ttc = $prd['price_ttc'];
														$rem_qty = $prd['qte'];

														$pass_here = true;
													}
													break;
												case 'max-prd' :
													if( !$old || $old<$prd['price_ht'] ){
														$old = $prd['price_ht'];
														$old_ttc = $prd['price_ttc'];
														$rem_qty = $prd['qte'];

														$pass_here = true;
													}
													break;
												case 'min-line' :
													if( !$old || $old>($prd['total_ht']) ){
														$old = $prd['total_ht'];
														$old_ttc = $prd['total_ttc'];

														$pass_here = true;
													}
													break;
												case 'max-line' :
													if( !$old || $old < $prd['total_ht'] ){
														$old = $prd['total_ht'];
														$old_ttc = $prd['total_ttc'];

														$pass_here = true;
													}
													break;
											}

											if( $pass_here ){
												if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
													$old_eco = prd_products_get_ecotaxe( $prd['id'] );
													$old_eco = $old_eco * $prd['qte'];

													$old_eco_ttc = $old_eco * $prd['tva_rate'];
												}

												$selected_product = array(
													'id' => $prd['id'],
													'line_id' => $prd['line'],
												);
											}
										}
									}

									if( !is_null($selected_product['id']) && !is_null($selected_product['line_id']) ){
										// Enregistre les promotions applicables dans un tableau.
										if( !array_key_exists($selected_product['id'], $product_promotion_ids) ){
											$product_promotion_ids[$selected_product['id']] = array(
												'line_id' => $selected_product['line_id'],
												'promotion_ids' => array(),
											);
										}

										$product_promotion_ids[$selected_product['id']]['promotion_ids'][] = $pmt['id'];
									}

									if( $off['discount_type']==0 ){
										if( $round_after_qte ){
											$total_ht -= round($off['discount'], $round_dec);
										}else{
											$total_ht -= $off['discount'];
										}
										if( $round_ttc ){
											if( $round_after_qte ){
												$total_ttc -= round($off['discount'] * ( in_array( $ord_in_ht, array('Oui', 'oui', '1') ) ? 1.000 : $off['tva_rate'] ), $round_dec);
											}else{
												$total_ttc -= $off['discount'] * round(( in_array( $ord_in_ht, array('Oui', 'oui', '1') ) ? 1.000 : $off['tva_rate'] ), $round_dec);
											}
										}else{
											$total_ttc -= $off['discount'] * ( in_array( $ord_in_ht, array('Oui', 'oui', '1') ) ? 1.000 : $off['tva_rate'] );
										}
									} else {
										$old = $old * $rem_qty;
										$old_ttc = $old_ttc * $rem_qty;

										if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
											$old -= $old_eco;
											$old_ttc -= $old_eco_ttc;
										}

										if( $round_after_qte ){
											$total_ht -= round(( $old * $off['discount'] / 100 ), $round_dec);
										}else{
											$total_ht -= ( $old * $off['discount'] / 100 );
										}
										if( $round_ttc ){
											if( $round_after_qte ){
												$total_ttc -= round($old_ttc * $off['discount'] / 100, $round_dec);
											}else{
												$total_ttc -= round($old_ttc, $round_dec) * $off['discount'] / 100;
											}
										}else{
											$total_ttc -= ( $old_ttc * $off['discount'] / 100 );
										}
									}
								}
							}
						}
						break;
				}

				if( $total_ht-$port_ht<0 ){
					$total_ht = $port_ht;
				}

				if( $total_ttc-$port_ttc<0 ){
					$total_ttc = $port_ttc;
				}

				if( ( $force_off && $force_off['free_shipping'] ) || ( isset($pmt['free_shipping']) && $pmt['free_shipping'] ) ){
					$srv_in = $force_off && $force_off['free_shipping'];
					if( !$force_off && isset($pmt['free_shipping']) && $pmt['free_shipping'] ){
						$code_services = pmt_codes_get_services( $pmt['parent'] ? $pmt['parent'] : $pmt['id'] );
						$srv_in = in_array($ord['srv_id'], $code_services) || ( !$ord['srv_id'] && !$ord['str_id'] ) || ( $ord['str_id'] && in_array(-1, $code_services) );
					}
					if( $srv_in ){
						$remove_port = true;
					}
				}
			}
		}

		if(isset($remove_port)){
			$total_ht -= $port_ht;
			$total_ttc -= $port_ttc;
		}

		if (trim($ord['piece']) != '') {
			$amount = fld_object_values_get( $order, _FLD_ORD_RWD_OPT4_HT );
			if (is_numeric($amount) && $amount > 0) {
				$total_ht -= $amount;
				$total_ttc -= $amount * _TVA_RATE_DEFAULT;
			}
		}else{
			if ($reward) {
				if (is_numeric($reward) && $reward > 0) {
					$r_reward = rwd_rewards_get( $reward );
					if ($r_reward && ria_mysql_num_rows($r_reward)) {
						$reward = ria_mysql_fetch_assoc( $r_reward );
					}
				}
			}else{
				// Application des remises provenant des points de fidélité
				if (is_numeric($prf) && $prf > 0) {
					$r_reward = rwd_rewards_get( 0, $prf );
					if ($r_reward && ria_mysql_num_rows($r_reward)) {
						$reward = ria_mysql_fetch_assoc( $r_reward );
					}
				}
			}

			if (is_array($reward) && ria_array_key_exists(array('system'), $reward)) {
				$pts_used = fld_object_values_get( $order, _FLD_ORD_PTS, '', false, true );
				if ($reward['system'] == _RWD_SYSTEM_REMISE && $pts_used >= $reward['nb_pts']) {
					$include_ht = $include_ttc = 0;

					if (ria_array_get($config, 'rwd_discount_on_discounted_order', false)) {
						$include_ht = $total_ht ;
						$include_ttc = $total_ttc;
					}else{
						// Récupère les articles toujours exclus des promotions
						$exclu_prd_ref = array( 'none_sql_exclu_rwd' );
						if (isset($config['pmt_excluded_auto']) && trim($config['pmt_excluded_auto']) != '') {
							$tmp = json_decode( $config['pmt_excluded_auto'], true );

							if (is_array($tmp) && array_key_exists('ref', $tmp) && is_array($tmp['ref']) && count($tmp['ref'])) {
								$exclu_prd_ref = array_merge( $exclu_prd_ref, $tmp['ref'] );
							}
						}

						$sql_include_rwd = '
							select sum(prd_price_ht * prd_qte) as price_ht, sum(prd_price_ht * prd_tva_rate * prd_qte) as price_ttc
							from riashop.ord_products
							where prd_tnt_id = '.$config['tnt_id'].'
								and prd_ord_id = '.$order.'
								and (
									prd_ref not in ("'.implode('", "', $exclu_prd_ref).'")
						';

						if ($reward['exclu-promo']) {
							$sql_include_rwd .= '
									and not exists (
										select 1
										from riashop.prc_prices
										where prc_tnt_id = '.$config['tnt_id'].'
											and prc_prd_id = prd_id
											and prc_is_deleted = 0
											and prc_is_promotion = 1
											and date(prc_date_start) <= now() and date(prc_date_end) >= now()
											and prd_qte >= prc_qte_min and prd_qte <= prc_qte_max
									)
							';
						}

						$sql_include_rwd .= '
								)
						';

						$r_tmp = ria_mysql_query( $sql_include_rwd );
						if ($r_tmp && ria_mysql_num_rows($r_tmp)) {
							$tmp = ria_mysql_fetch_assoc( $r_tmp );
							$include_ht  = $tmp['price_ht'];
							$include_ttc = round( $tmp['price_ttc'], 2 );
						}
					}

					if ($reward['discount_type'] == 1) {
						$rwd_remise_ht  = $include_ht  * $reward['discount'] / 100;
						$rwd_remise_ttc = $include_ttc * $reward['discount'] / 100;
					}else{
						$rwd_remise_ht  = $reward['discount'] <= $include_ht ? $reward['discount'] : $include_ht;
						$rwd_remise_ttc  = ($reward['discount'] * _TVA_RATE_DEFAULT) <= $include_ttc ? ($reward['discount'] * _TVA_RATE_DEFAULT) : $include_ttc;
					}

					fld_object_values_set( $order, _FLD_ORD_RWD_OPT4_HT, $rwd_remise_ht );
					$total_ht  = $total_ht - $rwd_remise_ht;
					$total_ttc = $total_ttc - $rwd_remise_ttc;
				}
			}
		}

		if( !$force_off && (!is_array($ord_pmt) || empty($ord_pmt))){
			if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
				$prd_price_ht = '
					if( prd_discount <= 0, prd_price_ht, if(prd_discount_type = 1,
						round( (prd_price_ht * (1 - (prd_discount / 100))), 2),
						if(prd_discount > prd_price_ht, 0, (prd_price_ht - prd_discount))
					))
				';

				$sql = '
					select
						sum('.$prd_price_ht.' * prd_qte) as total_ht,
						sum('.$prd_price_ht.' * prd_tva_rate * prd_qte) as total_ttc
					from
						riashop.ord_products
					where
						prd_tnt_id = '.$config['tnt_id'].'
						and prd_ord_id = '.$order.'
				';

				$res = ria_mysql_query( $sql );

				if( $res && ria_mysql_num_rows($res) ){
					$r = ria_mysql_fetch_assoc( $res );

					$total_ht = $r['total_ht'];
					$total_ttc = $r['total_ttc'];
				}
			}else{
				$r_products = ord_products_get( $order );

				if( $r_products && ria_mysql_num_rows($r_products) ){
					while( $product = ria_mysql_fetch_assoc($r_products) ){
						if( !$product['discount'] || $product['price_ht'] <= 0 ){
							continue;
						}

						// Si une remise a déjà été renseignée avec le champ avancé, elle est prioritaire sur la remise de la ligne de commande.
						// Etant déjà calculé sur le total de la ligne, il n'est pas nécessaire de la calculer sur le total de la commande
						$fld_discount = fld_object_values_get(array($product['ord_id'], $product['id'], $product['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);
						if( is_numeric($fld_discount) && $fld_discount > 0 ){
							continue;
						}

						$discount = $product['discount'];
						$discount_type = $product['discount_type'];

						if ($discount_type){ //Si 1, alors promotion en %
							$remise_ht = $product['total_ht'] * ($discount / 100);
						} else { //Sinon 0 donc remise en €
							$remise_ht = $discount;
						}

						if ($remise_ht == $product['total_ht']){
							$remise_ttc = $product['total_ttc'];
						} else {
							$remise_ttc = $remise_ht * $product['tva_rate'];
						}

						$total_ht -= $remise_ht > $total_ht ? $total_ht : $remise_ht;
						$total_ttc -= $remise_ttc > $total_ttc ? $total_ttc : $remise_ttc;
					}
				}
			}
		}

		// Mise en forme des total HT et total TTC pour l'insertion en base de données
		$total_ht  = str_replace(array(' ', ','), array('', '.'), round($total_ht, 2));
		$total_ttc = str_replace(array(' ', ','), array('', '.'), round($total_ttc, 2));

		$result_final = ria_mysql_query('
			update riashop.ord_orders
			set ord_total_ht = '.$total_ht.', ord_total_ttc = '.$total_ttc.', ord_products = '.$prd_count.', ord_margin='.$gross_margin.'
			where ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$order.'
		');

	}

	// Pour la synchro, nous avons besoin d'enregistrer dans un champ avancé
	// les ID des promotions applicables sur chaque ligne de commande.
	ria_mysql_query('
		delete from riashop.fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_obj_id_0 = '.$order.'
			and pv_fld_id = '._FLD_PRD_APPLICABLE_DISCOUNTS.'
	');

	foreach( $product_promotion_ids as $product_id => $item ){
		if( !count($item['promotion_ids']) ){
			continue;
		}

		$item['promotion_ids'] = array_unique( $item['promotion_ids'] );

		fld_object_values_set(
			array($order, $product_id, $item['line_id']),
			_FLD_PRD_APPLICABLE_DISCOUNTS,
			implode(', ', $item['promotion_ids'])
		);
	}

	// mise à jour des sous-totaux (calcul à titre indicatif, sans gestion du code promotion de la commande parent)
	if( $result_final && ord_orders_is_parent( $order ) ){
		if( $rchilds = ord_orders_get_childs( $order ) ){
			while( $child = ria_mysql_fetch_assoc($rchilds) ){
				ord_orders_update_totals( $child['id'] );
			}
		}
	}
	return $result_final;
}
// \endcond

// \cond onlyria
/**	Cette fonction va retourner le total HT des produits contenus dans une commande
 *	Ne tient pas compte des composants de nomenclature variable
 *
 *	@param int $ord Obligatoire, Identifiant de la commande
 *
 *	@return float le total hors taxe des lignes de produits contenues dans la commande
 *
 */
function ord_orders_products_total_ht_get( $ord ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	$sql = '
		select
			sum(prd_price_ht*prd_qte) as total_ht
		from
			riashop.ord_products
		where
			prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
	';

	if( $r_totals = ria_mysql_query( $sql ) ){
		if( $total = ria_mysql_fetch_array( $r_totals ) ){
			return $total['total_ht'];
		}
	}

	return 0;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer un tableau des identifiants produits présents dans une commande
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param $include_port Optionnel, par défaut les frais de port sont exclus, mettre True pour les inclure
 *	@return array Un tableau contenant les identifiants de produits (le tableau sera vide si l'un des paramètres est faux)
 */
function ord_orders_products_get_ids( $ord_id, $include_port=false ){
	$ar_prd_ids = array();

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return $ar_prd_ids;
	}

	global $config;

	$sql = '
		select prd_id as id
		from riashop.ord_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '.$ord_id.'
	';

	if( !$include_port ){
		$sql .= ' and prd_ref not in ("'.implode( '", "', $config['dlv_prd_references'] ).'")';
	}

	$res = ria_mysql_query( $sql );
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_prd_ids[] = $r['id'];
		}
	}

	return $ar_prd_ids;
}
// \endcond

/** Cette fonction permet de savoir si les frais de port sont offerts ou non pour une commande.
 *	@param int $order Obligatoire, identifiant d'une commande
 *	@return bool True si les frais de port sont offerts, False dans le cas contraire
 */
function ord_orders_is_free_shipping( $order ){
	global $config;

	if( !is_numeric($order) || $order<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from pmt_codes_services, (
			select ifnull(ord_pmt_id, 0) as pmt
				from riashop.ord_orders
				where ord_tnt_id='.$config['tnt_id'].'
					and ord_id='.$order.'
				union
			select ifnull(prd_cod_id, 0) as pmt
				from riashop.ord_products
				where prd_tnt_id='.$config['tnt_id'].'
					and prd_ord_id='.$order.'
				union
			select oop_pmt_id as pmt_codes
				from riashop.ord_orders_promotions
				where oop_tnt_id='.$config['tnt_id'].'
					and oop_ord_id='.$order.'
		) as nb
		where cod_tnt_id='.$config['tnt_id'].' and cod_id=pmt
		group by cod_srv_id
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/** Cette fonction retourne les services offerts avec les codes promotions utilisés par la commande.
 *	@param int $order Obligatoire, identifiant d'une commande
 *	@return array Un tableau contenant les identifiants des services offerts
 */
function ord_orders_get_free_shipping( $order ){
	global $config;

	$ar_srv = array();

	if( !is_numeric($order) || $order<=0 ){
		return $ar_srv;
	}

	$res = ria_mysql_query('
		select cod_srv_id as id
		from riashop.pmt_codes_services, (
			select ifnull(ord_pmt_id, 0) as pmt
			from riashop.ord_orders
			where ord_tnt_id='.$config['tnt_id'].'
				and ord_id='.$order.'

				union

			select ifnull(prd_cod_id, 0) as pmt
			from riashop.ord_products
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_ord_id='.$order.'

				union

			select opm_cod_id as pmt
			from riashop.ord_products_promotions
			where opm_tnt_id='.$config['tnt_id'].'
				and opm_ord_id='.$order.'

				union

			select oop_pmt_id as pmt
			from riashop.ord_orders_promotions
			where oop_tnt_id='.$config['tnt_id'].'
				and oop_ord_id='.$order.'
		) as ord_promo
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id=ord_promo.pmt

		union

		select cod_srv_id as id
		from riashop.pmt_codes as c, riashop.pmt_codes_services as s, (
			select ifnull(ord_pmt_id, 0) as pmt
			from riashop.ord_orders
			where ord_tnt_id='.$config['tnt_id'].'
				and ord_id='.$order.'

				union

			select ifnull(prd_cod_id, 0) as pmt
			from riashop.ord_products
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_ord_id='.$order.'

				union

			select opm_cod_id as pmt
			from riashop.ord_products_promotions
			where opm_tnt_id='.$config['tnt_id'].'
				and opm_ord_id='.$order.'

				union

			select oop_pmt_id as pmt
			from riashop.ord_orders_promotions
			where oop_tnt_id='.$config['tnt_id'].'
				and oop_ord_id='.$order.'
		) as ord_promo
		where c.cod_tnt_id='.$config['tnt_id'].'
			and c.cod_tnt_id=s.cod_tnt_id
			and c.cod_parent_id=s.cod_id
			and c.cod_id=ord_promo.pmt
		group by cod_srv_id
	');

	if( $res && ria_mysql_num_rows($res) ){
		while( $srv = ria_mysql_fetch_array($res) ){
			$ar_srv[] = $srv['id'];
		}
	}

	return $ar_srv;
}
// \endcond

// \cond onlydev
/** \ingroup model_promotions_specials
 *	@{
 */
// \endcond

/** Cette fonction permet de vérifier si les promotions appliquées sur une commande sont toujours valables. Si tel n'est plus le cas alors elle sont automatiquement retirées.
 *	Elle appliquera aussi les promotions automatiques.
 *	@param int $order Obligatoire, identifiant d'une commande
 *	@param int|bool $reseller Identifiant du revendeur sur lequel les tarifs sont basés. Valeur par défaut : false
 *	@return bool True si la vérification s'est déroulée sans problème, False dans le cas contraire
 */
function ord_orders_promotions_verified( $order, $reseller=false ){
	global $config;

	if( !is_numeric($order) || $order<=0 ){
		return false;
	}

	$state_id = ord_orders_get_state($order);
	if (!in_array($state_id, ord_states_get_uncompleted())) {
		return false;
	}

	// Contrôle qu'il existe au minimum une promotion spéciale sur la commande
	$having_promo = ria_mysql_query('
		select 1
		from riashop.ord_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$order.'
			and ifnull(prd_cod_id, "")!=""

		union

		select 1
		from riashop.ord_products_promotions
		where opm_tnt_id='.$config['tnt_id'].'
			and opm_ord_id='.$order.'
	');

	$update_total_order = false;
	$ar_ord_childs = array();

	// S'il existe une promotion spéciale sur la commande
	if( $having_promo && ria_mysql_num_rows($having_promo) ){
		// Récupère les produits de la commande (hors frais de port)
		$rord_prd = ria_mysql_query('
			select
				op.prd_id as id, op.prd_ref as ref, op.prd_line_id as line, op.prd_name as name, op.prd_cod_id as cod, op.prd_qte as qte, op.prd_price_ht as price_ht, p.prd_follow_stock as follow_stock,
				(op.prd_price_ht * op.prd_qte) as total_ht, (op.prd_price_ht * op.prd_tva_rate * op.prd_qte) as total_ttc, hp.prd_parent_id as parent, op.prd_tva_rate as tva_rate, p.prd_ecotaxe as ecotaxe,
				if(ifnull(p.prd_weight_net, "")="", p.prd_weight, p.prd_weight_net) as weight_net, ifnull(p.prd_sell_weight, 0) as sell_weight, prd_ord_child_id as ord_child_id
			from riashop.ord_products as op
				join riashop.prd_products as p on (op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id)
				left join riashop.prd_hierarchy as hp on (p.prd_tnt_id=hp.prd_tnt_id and p.prd_id=hp.prd_child_id)
			where op.prd_tnt_id='.$config['tnt_id'].'
				and op.prd_ord_id='.$order.'
				and op.prd_ref not in ("'.implode('", "', array_slashes($config['dlv_prd_references'])).'")
				and p.prd_date_deleted is null
		');

		if( $rord_prd && ria_mysql_num_rows($rord_prd) ){
			$ord = array( 'usr_id'=>0, 'state'=>0 );
			$get_infos = $user = $prc = false;

			// Pour chaque produit, les promotions spéciales sont annulées
			while( $ord_prd = ria_mysql_fetch_array($rord_prd) ){
				$code = pmt_codes_get_code( $ord_prd['cod'] );

				if( trim($code) != '' ){
					continue;
				}

				$type = 0;
				if( $ord_prd['cod'] ){
					$type = pmt_codes_get_type( $ord_prd['cod'] );
				}

				switch( $type ){
					case _PMT_TYPE_PRD :
						$del_product = true;

						if( $config['tnt_id'] == 4 ){
							$r_product = pmt_offer_products_get( $ord_prd['cod'] );
							if( $r_product && ria_mysql_num_rows($r_product) > 1 ){
								if( pmt_codes_is_applicable(null, $order, $ord_prd['cod']) === true ){
									$del_product = false;
								}
							}
						}

						if( $del_product ){
							if( is_numeric($ord_prd['ord_child_id']) && $ord_prd['ord_child_id'] > 0 ){
								$ar_ord_childs[ $ord_prd['id'].'-'.$ord_prd['cod'] ] = $ord_prd['ord_child_id'];
							}

							$can_delete = true;

							// Vérifie qu'il s'agit d'une promotion n'offrant pas le choix entre plusieurs produits
							// ou bien que la promotion est terminée ou ne remplit plus les conditions
							{
								$r_product = pmt_offer_products_get($ord_prd['cod']);
								if ($r_product && ria_mysql_num_rows($r_product) > 1) {
									$can_delete = false;

									if (pmt_codes_is_applicable(null, $order, $ord_prd['cod']) !== true) {
										$can_delete = true;
									}
								}
							}

							if ($can_delete) {
								if (!ord_products_del($order, $ord_prd['id'], $ord_prd['line'], -1, $ord_prd['cod'], false)) {
									return false;
								}
							}
						}

						break;
					case _PMT_TYPE_BUY_X_FREE_Y : {
						if( is_numeric($ord_prd['ord_child_id']) && $ord_prd['ord_child_id'] > 0 ){
							$ar_ord_childs[ $ord_prd['id'].'-'.$ord_prd['cod'] ] = $ord_prd['ord_child_id'];
						}

						if( !ord_products_del( $order, $ord_prd['id'], $ord_prd['line'], -1, $ord_prd['cod'], false ) ){
							return false;
						}

						break;
					}
					default : {

						$r_promo = ord_products_promotions_get( $order, $ord_prd['id'] );
						if( !$r_promo || !ria_mysql_num_rows($r_promo) ){
							if( !$ord_prd['cod'] ){
								break;
							}
						}

						// Récupère les lignes de type Remise Hors stock pour les supprimer et les ajouter de façon normal à la commande
						$r_outstock = ord_products_get( $order, false, 0, '', null, false, 0, 0, -1, array(_FLD_PRD_ORD_REMISE_OUTSTOCK => 'Oui') );
						if( $r_outstock ){
							while( $outstock = ria_mysql_fetch_assoc($r_outstock) ){
								ord_products_del( $order, $outstock['id'], $outstock['line'] );
								$line_id = ord_products_add( $order, $outstock['id'], $outstock['qte'], '', true, null, 0, false, 0, 0, false, false, false, true );

								if( $line_id !== false ){
									if( is_numeric($ord_prd['ord_child_id']) && $ord_prd['ord_child_id'] > 0 ){
										ord_products_set_ord_child_id( $order, $outstock['id'], $line_id, $ord_prd['ord_child_id'] );
									}
								}
							}
						}

						if( !$get_infos ){
							$rord = ria_mysql_query('
								select ord_usr_id as usr_id, ord_state_id as state
								from riashop.ord_orders
								where ord_tnt_id='.$config['tnt_id'].'
									and ord_id='.$order.'
							');

							if( $rord && ria_mysql_num_rows($rord) ){
								$ord = ria_mysql_fetch_assoc( $rord );
							}

							// Récupère la catégorie tarifaire de l'utilisateur
							$prc = isset($_SESSION['usr_prc_id']) && prd_prices_categories_exists($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : $config['default_prc_id'];

							$user = is_numeric($ord['usr_id']) && $ord['usr_id']>0 ? $ord['usr_id'] : 0;
							if( $user ){
								$prc = gu_users_get_prc( $user );
							}

							// Détermine l'arrondi du prix HT
							$round_price = !prd_prices_categories_get_ttc( $prc );

							// Détermine le ratio de vente au poids
							$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;

							$get_infos = true;
						}

						// Détermine si la ligne est négociée ou non
						$negociate = fld_object_values_get( array( $order, $ord_prd['id'], $ord_prd['line'] ), _FLD_PRD_ORD_FREE, '', false, true );
						$negociate = $negociate==='Oui' || $negociate==='oui' || $negociate || $ord['state']==_STATE_MODEL;

						if( $negociate ){
							if( is_numeric($ord_prd['parent']) && $ord_prd['parent']>0 ){
								continue;
							}
						}else{
							if( is_numeric($ord_prd['parent']) && $ord_prd['parent']>0 ){
								if( !is_numeric($ord_prd['price_ht']) || $ord_prd['price_ht']==0 ){
									$negociate = true;
									error_log( __FILE__.':'.__LINE__.' Ligne de commande négociée + identifiant promotion - '.$ord_prd['cod'].' - '.$ord_prd['id'] );
									break;
								}
							}
						}

						// Récupère le colisage
						$colisage = fld_object_values_get( array($order, $ord_prd['id'], $ord_prd['line']), _FLD_PRD_COL_ORD_PRODUCT, '', false, true );
						$colisage = !is_numeric($colisage) || $colisage<=0 ? 0 : $colisage;

						$price_ht = $ord_prd['price_ht'];
						$tva_rate = $ord_prd['tva_rate'];
						if( !$tva_rate ){
							$tva_rate = _TVA_RATE_DEFAULT;
						}

						// détermine le tarif standard
						if( $rprice = prd_products_get_price( $ord_prd['id'], $user, $prc, $order, $ord_prd['qte'], $colisage ) ){
							if( $p = ria_mysql_fetch_array($rprice) ){
								$price_ht = $p['price_ht'];
								$tva_rate = $p['tva_rate'];

								if( !$tva_rate ){
									$tva_rate = _TVA_RATE_DEFAULT;
								}
							}
						}

						$price_ar = null;
						if( ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $p) ){
							$price_ar = $p;
						}

						// Détermine une éventuelle promotion
						$pmt = prc_promotions_get( $ord_prd['id'], $user, $prc, $ord_prd['qte'], $colisage, $price_ar );
						if( is_array($pmt) && sizeof($pmt) && $pmt['price_ht']<$price_ht ){
							$price_ht = $pmt['price_ht'];
						}

						// gestion d'un prix au niveau du revendeur
						if( $config['tnt_id'] == 4 && in_array($config['wst_id'], array(26, 27, 30, 78)) && isset($config['fld_price_public']) ){
							$default = fld_object_values_get( $ord_prd['id'], $config['fld_price_public'] );
							if( is_numeric($default) && $default > 0 ){
								$price_ht = $default / $tva_rate;
							}
						}

						if( is_numeric($reseller) && $reseller>0 ){
							require_once('prd/resellers.inc.php');
							$rseller = prd_resellers_get( $ord_prd['id'], $reseller );
							if( $rseller && ria_mysql_num_rows($rseller) ){
								$seller = ria_mysql_fetch_array($rseller);
								if( is_numeric($seller['price_ht']) && $seller['price_ht']>0 ){
									$price_ht = $seller['price_ht'];
								}
								if( is_numeric($seller['price_promo_ht']) && $seller['price_promo_ht']!=0 ){
									$price_ht = $seller['price_promo_ht'];
								}
							}
						}

						// Détermine, pour les articles vendus au poids sans colisage, le prix pour le poids ( p/r au prix au kilo )
						if( $ord_prd['weight_net']>0 && $ord_prd['weight_net']!=$ratio && $ord_prd['sell_weight'] && !$colisage ){
							$price_ht = $price_ht * ( $ord_prd['weight_net'] / $ratio );
						}

						if( $round_price && !$colisage ){
							$price_ht = round( $price_ht, $config['round_digits_count'] );
						}

						// Rafraichit la ref, le libellé, le tarif, la TVA et l'écotaxe
						$sql = '
							update riashop.ord_products
							set
								prd_ref="'.$ord_prd['ref'].'",
								prd_name=\''.addslashes($ord_prd['name']).'\',
								prd_ecotaxe='.$ord_prd['ecotaxe'].',
								prd_price_ht='.$price_ht.',
								prd_tva_rate='.$tva_rate.'
							where prd_tnt_id='.$config['tnt_id'].'
								and prd_ord_id='.$order.'
								and prd_id='.$ord_prd['id'].'
								and prd_line_id='.$ord_prd['line'].'
						';

						if( !ria_mysql_query($sql) ){
							return false;
						}
					}
				}
			}

			// Retirer toutes les remises automatiques pouvant être appliquée sur la commande
			if( !ord_products_promotions_del($order) ){
				return false;
			}

			$update_total_order = true;
		}
	}

	return ord_orders_refresh_promotions( $order, 0, $update_total_order, $ar_ord_childs );
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/** Cette fonction permet de gérer toutes les promotions spéciales sur une commande.
 *
 *	@param int $order Obligatoire, identifiant d'une commande
 *	@param int $cod Facultatif, identifiant d'une promotion, si aucun identifiant n'est donnée la fonction recherchera des promotions automatique à appliquer
 *	@param bool $update_total_order Facultatif, booléen indiquant si l'opération doit également mettre à jour le montant total des commandes (true) ou non (false, valeur par défaut)
 *	@param array $ar_ord_childs Facultatif, liste des produits des commandes enfants
 *
 *	@return bool True si le traitement des promotions s'est correctement déroulée, False dans le cas contraire
 */
function ord_orders_refresh_promotions( $order, $cod=0, $update_total_order=false, $ar_ord_childs=array() ){
	global $config;

	if( !is_numeric($cod) || $cod<0 ){
		return false;
	}
	if( $cod>0 && !pmt_codes_exists($cod) ){
		return false;
	}
	if( !ord_orders_exists($order) ){
		return false;
	}

	// Récupère les promotions pouvant s'appliquer automatiquement
	if( !$cod ){
		$rcod = pmt_codes_get( null, null, true, [_PMT_TYPE_PRD, _PMT_TYPE_BUY_X_FREE_Y, _PMT_TYPE_REDUC], true );
	} else {
		$rcod = pmt_codes_get( $cod, null, true );
	}

	{	// Récupère les promotions de types REMISES ou SOLDES
		$pmt_field = false;
		// Hack Chadog, seules les promotions avec "Hors seuil max" à Oui sont récupérées
		if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
			$pmt_field = [ 102710=>'Oui' ];
		}

		$remise = pmt_codes_get( null, null, true, [_PMT_TYPE_REMISE, _PMT_TYPE_SOLDES], true, null, null, null, null, $pmt_field );
	}

	$ar_products = array();
	if( ($rcod && ria_mysql_num_rows($rcod)) || ($remise && ria_mysql_num_rows($remise)) ){
		// Récupération du dépôt pour contrôler le stock d'un produit
		$dps = 0;
		if( $config['prd_deposits']=='use-main' ){
			$dps = prd_deposits_get_main();
		}else{
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		}

		// Récupère le compte client rattaché à la commande ou bien celui en session
		$user_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']!='' ? $_SESSION['admin_view_user'] : ( isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0 );

		$ruser = ria_mysql_query('select ord_usr_id from riashop.ord_orders where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$order.' and ifnull(ord_usr_id, 0)!=0');
		if( $ruser && ria_mysql_num_rows($ruser) ){
			$user = ria_mysql_fetch_assoc( $ruser );
			$user_id = $user['ord_usr_id'];
		}

		// Récupère les produits de la commande en cours de vérification
		$roprd = ria_mysql_query('
			select
				op.prd_id as id, op.prd_ref as ref, op.prd_line_id as line, op.prd_name as name, op.prd_cod_id as cod, op.prd_qte as qte, op.prd_price_ht as price_ht, p.prd_follow_stock as follow_stock, op.prd_dps_id as dps_id,
				(op.prd_price_ht * op.prd_qte) as total_ht, (op.prd_price_ht * op.prd_tva_rate * op.prd_qte) as total_ttc
			from riashop.ord_products as op
				join riashop.prd_products as p on (op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id)
			where op.prd_tnt_id='.$config['tnt_id'].'
				and op.prd_ord_id='.$order.'
				and op.prd_ref not in ("'.implode('", "', array_slashes($config['dlv_prd_references'])).'")
				and p.prd_date_deleted is null
		');

		if( $roprd ){
			while( $one_p = ria_mysql_fetch_assoc($roprd) ){
				$ar_products[] = $one_p;
			}
		}
	}

	// Gestion des promotions spéciales de types remises
	if( $remise && ria_mysql_num_rows($remise) ){

		$ar_remise = array();

		$ar_prd_ids = [];
		foreach( $ar_products as $one_prd ){
			array_push( $ar_prd_ids, $one_prd['id'] );
		}

		while( $one_remise = ria_mysql_fetch_assoc($remise) ){
			$roffer = pmt_offers_get( $one_remise['id'] );
			if( $roffer && ria_mysql_num_rows($roffer) ){
				$products = array();

				if( count($ar_prd_ids) > 0 ){
					$rp_in_cod = pmt_codes_products_get( 0, false, $ar_prd_ids, $one_remise, true );
					if( is_array($rp_in_cod) ){
						foreach ($rp_in_cod as $r) {
							$products[ $r['prd_id'] ] = $r;
						}
					}
				}

				$ar_remise[] = array(
					'cod' => $one_remise,
					'offer' => ria_mysql_fetch_assoc( $roffer ),
					'products' => $products
				);
			}
		}

		if( sizeof($ar_products) ){
			$rord = ord_orders_get_with_adresses( $user_id, $order );
			if( !$rord || !ria_mysql_num_rows($rord) ){
				return false;
			}

			$ord = ria_mysql_fetch_assoc( $rord );

			foreach( $ar_products as $one_p ){
				$is_linked = false;
				if( $config['tnt_id'] == 39 ){
					$id_link = fld_object_values_get( array($order, $one_p['id'], $one_p['line']), 3394, '', false, true );
					if( is_numeric($id_link) && $id_link > 0 ){
						$is_linked = true;
					}
				}

				if( !$is_linked ){
					$negociate = fld_object_values_get( array( $order, $one_p['id'], $one_p['line'] ), _FLD_PRD_ORD_FREE, '', false, true );
					$negociate = $negociate==='Oui' || $negociate==='oui' || $negociate;
					if( $negociate ){
						continue;
					}
				}

				$outstock = fld_object_values_get( array($order, $one_p['id'], $one_p['line']), _FLD_PRD_ORD_REMISE_OUTSTOCK, '', false, true );
				$outstock = $outstock==='Oui' || $outstock==='oui' || $outstock;
				if( $outstock ){
					continue;
				}

				$last = $cod_apply = 0;
				$qte = $qte_supp = 0;

				// Contrôle la quantité en stock
				require_once 'Stock/PmtOutstock.php';
				$PmtOutstock = new \Stock\PmtOutstock($one_p['id'], $dps);
				$stock = $PmtOutstock->getStock();

				$calc_promotions_type = 0;
				if( isset($config['calc_promotions_type']) && is_numeric($config['calc_promotions_type']) ){
					$calc_promotions_type = $config['calc_promotions_type'];
				}

				foreach( $ar_remise as $one_remise ){

					if (!array_key_exists($one_p['id'], $one_remise['products'])) {
						continue;
					}

					//Récupère le colisage utilisé lors de la commande
					$colisage = fld_object_values_get( array($order, $one_p['id'], $one_p['line']), _FLD_PRD_COL_ORD_PRODUCT );
					if( !is_numeric($colisage) || $colisage <= 0 ){
						$colisage = 0; // a l'unité
					}

					// Contrôle que le colisage n'est pas exclut de la promotion
					if( isset($one_remise['products'][ $one_p['id'] ]['col_excluded']) && in_array($colisage, $one_remise['products'][ $one_p['id'] ]['col_excluded'])){
						continue;
					}

					// Contrôle que le colisage fait partie de la liste d'inclusion définies (si une liste a été définie, sinon tous les colisages sont inclus)
					if( isset($one_remise['products'][ $one_p['id'] ]['col_included']) && is_array($one_remise['products'][ $one_p['id'] ]['col_included']) && count($one_remise['products'][ $one_p['id'] ]['col_included'])  ){
						if( !in_array($colisage, $one_remise['products'][ $one_p['id']]['col_included']) ){
							continue;
						}
					}

					if( is_numeric($one_p['dps_id']) && $one_p['dps_id'] > 0 ){
						// Contrôle que le dépôt n'est pas exclut de la promotion
						if( isset($one_remise['products'][ $one_p['id'] ]['col_excluded_dps']) && in_array($one_p['dps_id'], $one_remise['products'][ $one_p['id'] ]['col_excluded_dps'])){
							continue;
						}

						// Contrôle que le dépôt fait partie de la liste d'inclusion définies (si une liste a été définie, sinon tous les colisages sont inclus)
						if( isset($one_remise['products'][ $one_p['id'] ]['col_included_dps']) && is_array($one_remise['products'][ $one_p['id'] ]['col_included_dps']) && count($one_remise['products'][ $one_p['id'] ]['col_included_dps'])  ){
							if( !in_array($one_p['dps_id'], $one_remise['products'][ $one_p['id']]['col_included_dps']) ){
								continue;
							}
						}
					}

					$limit_in_stock = false;
					$ctrl_stock = $stock;
					if( $PmtOutstock->isActive() ){
						if( $one_p['follow_stock'] && $one_remise['cod']['available_stocks'] ){
							$limit_in_stock = true;

							if ($PmtOutstock->checkRestocking($one_remise['cod']['date_start_en'])) {
								$ctrl_stock = 0;
							}

							if( $ctrl_stock < $one_p['qte'] ){
								$qte = $ctrl_stock;
								$qte_supp = $one_p['qte'] - $ctrl_stock;
							}
						}
					}

					$one_r = $one_remise['offer'];
					$prd_remise = $one_remise['products'][ $one_p['id'] ];

					if( $prd_remise['discount'] !== null && $prd_remise['discount_type'] !== null ){
						$one_r['discount'] = $prd_remise['discount'];
						$one_r['discount_type'] = $prd_remise['discount_type'];
					}

					$user_is_include = true;
					if (!$one_r['all_customers']) {
						if (!is_numeric($user_id) || $user_id <= 0) {
							$user_is_include = false;
						}
					}
					if( $user_is_include && (!$limit_in_stock || $ctrl_stock > 0) && pmt_codes_is_applicable( null, $order, 0, false, $one_p['id'], $one_remise['cod'], $ord )===true ){

						// récupère le tarif de base suivant le type de calcul
						$price_ht = null;
						$rprice = false;

						// Calcul spécial des promotions pour Proloisirs / Océo
						if( $config['tnt_id'] == 4 && in_array($config['wst_id'], array(27, 30)) ){
							require_once($config['site_dir'].'/include/view.product.inc.php');
							$rprice = proloisirs_get_price( $one_p['id'] );

							if( isset($rprice['price_promo_ht'], $rprice['price_ht']) ){
								$price_ht = is_numeric($rprice['price_promo_ht']) && $rprice['price_promo_ht'] > 0 ? $rprice['price_promo_ht'] : $rprice['price_ht'];
							}
						}else{
							if( $calc_promotions_type==PMT_BASE_PRICE ){
								$p = prd_products_get_price_array( $one_p['id'], 0, 0, 0, 1, 0 );
							}elseif( $calc_promotions_type==PMT_BASE_PRICE_QTE ){
								$p = prd_products_get_price_array( $one_p['id'], 0, 0, 0, $one_p['qte'], 0 );
							}elseif( $calc_promotions_type==PMT_USR_PRICE ){
								$p = prd_products_get_price_array( $one_p['id'], $user_id, $config['default_prc_id'], 0, 1, 0 );
							}elseif( $calc_promotions_type==PMT_USR_PRICE_QTE ){
								$p = prd_products_get_price_array( $one_p['id'], $user_id, $config['default_prc_id'], 0, $one_p['qte'], 0 );
							}
							if( ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $p) ){
								$price_ht = $p['price_ht'];

								/** Problème survenu chez Pierre Oteiza pour les produits avec un tarif au kg
								 * @see https://riastudio.atlassian.net/browse/PIERREOTMT-52
								 */
								if( $config['tnt_id'] == 13 && prd_products_get_is_selling_weight($one_p['id']) ){
									$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;
									$rcol = prd_colisage_classify_get( 0, $one_p['id']);
									if( ria_mysql_num_rows($rcol)==0 ){
										$wn = prd_products_get_weight_net($one_p['id']);
										$price_ht = $price_ht * (($wn>0 ? $wn : $ratio )/$ratio);
										$p['price_ht'] = $price_ht;
										$p['price_ttc'] = $price_ht * $p['tva_rate'] ;
									}
								}
							}
						}
						if( $price_ht===null ){
							return false;
						}

						if( $one_r['discount_type'] == 0 ){
							$total = $price_ht - $one_r['discount'];
						}elseif( $one_r['discount_type'] == 1 ){
							if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
								$ecotaxe = prd_products_get_ecotaxe( $one_p['id'] );
								$total = ( ($price_ht - $ecotaxe) * ( 1 - ($one_r['discount'] / 100) ) ) + $ecotaxe;
							}else{
								$total = $price_ht * ( 1 - ($one_r['discount'] / 100) );
							}
						}elseif( $one_r['discount_type'] == 2 ){
							$total = $price_ht;
							if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
								$ecotaxe = prd_products_get_ecotaxe( $one_p['id'] );
								if (($price_ht-$ecotaxe) > $one_r['discount']) {
									$total = $one_r['discount'] + $ecotaxe;
								}
							}elseif ($price_ht > $one_r['discount']) {
								$total = $one_r['discount'];
							}
						}

						if( $total<0 ){
							$total = 0;
						}

						if( $last==0 || $last>$total ){
							$cod_apply = $one_r['cod_id'];
							$last = $total;
						}
					}
				}

				if( $cod_apply && (!$limit_in_stock || $stock > 0) ){
					if (
						isset($config['calc_promotions_base_against_user'])
						&& $config['calc_promotions_base_against_user']
						&& in_array($calc_promotions_type, array(PMT_BASE_PRICE, PMT_BASE_PRICE_QTE))
					) {
						$r_user_price = prd_products_get_price( $one_p['id'], $user_id, $config['default_prc_id'], 0, 1, 0 );
						if( $r_user_price && ria_mysql_num_rows($r_user_price) ){
							$user_price = ria_mysql_fetch_assoc($r_user_price);
							if ($last > $user_price['price_ht']) {
								$last = $user_price['price_ht'];
							}
						}
					}

					// hack chadog, l'arrondi du prix remise est sur 2 chiffres après la virgule
					if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
						$last = round( $last, 2 );
					}

					// gère le cas où $last est anormalement vide
					if (is_null($last) || $last === '') {
						continue; 
					}

					if($config['tnt_id'] == 1024 && !empty($p['tva_rate']))
					{

						$tva_rate_hermitage = $p['tva_rate'];
						$ttc_hermitage = $last * $tva_rate_hermitage;

						$res = ria_mysql_query('
							update riashop.ord_products
							set prd_price_ttc='.$ttc_hermitage.'
							where prd_tnt_id='.$config['tnt_id'].'
								and prd_ord_id='.$order.'
								and prd_id='.$one_p['id'].'
								and prd_line_id='.$one_p['line'].'
						');
						
					}
					// Mise à jour du montant HT de la ligne de commande
						$res = ria_mysql_query('
							update riashop.ord_products
							set prd_price_ht='.$last.'
							'.( $qte > 0 ? ', prd_qte = '.$qte : '' ).'
							where prd_tnt_id='.$config['tnt_id'].'
								and prd_ord_id='.$order.'
								and prd_id='.$one_p['id'].'
								and prd_line_id='.$one_p['line'].'
						');

					// Limite de stock atteinte pour la remise
					if( $qte > 0 && $qte_supp > 0 ){
						$line_outstock = ord_products_add( $order, $one_p['id'], $qte_supp, '', true, null, 0, false, 0, 0, false, false, false, true, false, true, $one_p['dps_id'] );
						fld_object_values_set( array($order, $one_p['id'], $line_outstock), _FLD_PRD_ORD_REMISE_OUTSTOCK, 'Oui' );
					}

					// Insère la remise sur la ligne de commande
					if( !ord_products_promotions_add($order, $one_p['id'], $one_p['line'], $cod_apply ) ){
						continue;
					}

					$update_total_order = true;
				}
			}
		}
	}

	// Gestion des promotions spéciales hors (Bons d'achat, Chèques cadeaux, Remises)
	if( $rcod && ria_mysql_num_rows($rcod) ){
		// Récupère pour la commande en cours les promotions exclues
		$ar_exclude = ord_promotions_excluded_get_byord( $order );

		while( $cod = ria_mysql_fetch_assoc($rcod) ){
			// Exclut certains type de promotion du système
			if( in_array($cod['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_REMISE, _PMT_TYPE_SOLDES)) ){
				continue;
			}

			if( in_array($cod['id'], $ar_exclude) ){
				continue;
			}

			// Si la promotion spéciale n'est pas applicable sur la commande, on ne va pas plus loin
			if( pmt_codes_is_applicable(null, $order, $cod['id'])!==true ){
				continue;
			}

			// Récupère les offres sur la promotion
			$roffer = pmt_offers_get( $cod['id'] );
			if( !$roffer || !ria_mysql_num_rows($roffer) ){
				continue;
			}

			// Des lignes de commandes doivent obligatoires exister pour certains types promotions
			if( in_array($cod['type'], array(_PMT_TYPE_REDUC, _PMT_TYPE_BUY_X_FREE_Y)) ){
				if( !sizeof($ar_products) ){
					continue;
				}
			}

			switch( $cod['type'] ){
				case _PMT_TYPE_PRD: { // Gestion des promotions de type : PRODUIT(s) OFFERT(s)
					$offer = ria_mysql_fetch_assoc( $roffer );

					$rpop = pmt_offer_products_get( $cod['id'], $offer['id'], ($offer['prd_in_cart'] ? $order : 0) );

					if( $rpop && ria_mysql_num_rows($rpop)==1 ){
						$pop = ria_mysql_fetch_assoc( $rpop );

						// Récupère le multiple de produit offert
						$multiple = 1;

						if( !$offer['one_by_cart'] ){
							$multiple = (int) pmt_code_conditions_apply( $cod['id'], $order, false, true );

							if( !is_numeric($multiple) || $multiple<=0 ){
								continue;
							}
						}

						$is_follow_stock = prd_products_is_follow_stock( '', $pop['prd_id'] );
						if( $is_follow_stock && $cod['available_stocks'] ){
							$stock = 0;
							$rsto = prd_dps_stocks_get( $pop['prd_id'], $dps );
							if( $rsto && ria_mysql_num_rows($rsto) ){
								$sto = ria_mysql_fetch_assoc( $rsto );
								if( $config['tnt_id'] != 4 ){
									$stock = $sto['qte'] - $sto['prepa'];
								}else{
									$stock = $sto['qte'] - $sto['prepa'] - $sto['res'] + $sto['com'];
								}
							}

							$ordered = ord_products_get_qte( $order, $pop['prd_id'] );

							if( $stock<($ordered + ($pop['qty']*$multiple)) ){
								continue;
							}
						}

						$tva_rate = _TVA_RATE_DEFAULT;
						$r_tva_prd = prc_tvas_get( 0, false, $pop['prd_id'] );
						if ($r_tva_prd && ria_mysql_num_rows($r_tva_prd)) {
							$tva_prd = ria_mysql_fetch_assoc( $r_tva_prd );
							$tva_rate = $tva_prd['rate'];
						}

						$control_restriction = !isset($config['pmt_control_restriction']) || $config['pmt_control_restriction'];
						$line_id = ord_products_add_offer( $order, $pop['prd_id'], $cod['id'], ($pop['qty']*$multiple), true, $control_restriction, true, $tva_rate );
						if( $line_id === false ){
							return false;
						}

						if( array_key_exists($pop['prd_id'].'-'.$cod['id'], $ar_ord_childs) ){
							ord_products_set_ord_child_id( $order, $pop['prd_id'], $line_id, $ar_ord_childs[ $pop['prd_id'].'-'.$cod['id'] ] );
						}
					}
					break;
				}
				case _PMT_TYPE_REDUC: { // Gestion des promotions de type : REDUCTIONS DEGRASIVES
					foreach( $ar_products as $key=>$oprd ){
						if( is_numeric($oprd['cod']) && $oprd['cod'] > 0 && pmt_codes_get_type($oprd['cod']) != _PMT_TYPE_REDUC ){
							continue;
						}

						$upd_tarif = false;

						if( pmt_products_is_included($cod, $oprd['id']) ){
							// récupère le tarif du produit
							$rprice = prd_products_get_price( $oprd['id'], $user_id );
							if( !$rprice || !ria_mysql_num_rows($rprice) ){
								continue;
							}

							$price = ria_mysql_fetch_array( $rprice );
							if( $price['price_ht']<=0 ){
								continue;
							}

							$pmt = prc_promotions_get( $oprd['id'], $user_id );
							if( isset($pmt['price_ht']) && $price['price_ht']>$pmt['price_ht'] ){
								$price['price_ht'] = $pmt['price_ht'];
							}

							if( prd_products_get_is_selling_weight($oprd['id']) ){
								$weight = prd_products_get_weight_net( $oprd['id'] );
								if( !$weight ){
									$weight = prd_products_get_weight_brut( $oprd['id'] );
								}

								if( !$weight ){
									continue;
								}

								$price['price_ht'] = $price['price_ht'] * $weight / 1000;
							}

							$one = false;
							$total = 0;

							if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
								$ecotaxe = prd_products_get_ecotaxe( $oprd['id'] );
								$price['price_ht'] -= $ecotaxe;
							}

							for( $i=1 ; $i<=$oprd['qte'] ; $i++ ){
								ria_mysql_data_seek( $roffer, 0 );

								$sub_total = 0; $one_pos = false;
								while( $offer = ria_mysql_fetch_array($roffer) ){
									if( $offer['prd_pos']==$i || $i%$offer['prd_pos']==0 ){
										$one = $one_pos = true;

										$sub_total = $price['price_ht'] - ($price['price_ht'] * ($offer['discount'] / 100));
									}
								}

								$total += $one_pos || $sub_total>0 ? $sub_total : $price['price_ht'];
							}

							if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
								$total += $ecotaxe * $oprd['qte'];
							}

							if( $one ){
								$upd_tarif = $total / $oprd['qte'];
							}
						}

						if( $upd_tarif && $upd_tarif!=$oprd['price_ht'] ){
							$res = ria_mysql_query('
								update riashop.ord_products
								set prd_price_ht='.$upd_tarif.',
									prd_cod_id='.$cod['id'].'
								where prd_tnt_id='.$config['tnt_id'].'
									and prd_id='.$oprd['id'].'
									and prd_line_id='.$oprd['line'].'
									and prd_ord_id='.$order.'
							');

							if( !$res ){
								continue;
							}

							$ar_products[ $key ]['price_ht'] = $upd_tarif;
						}
					}

					break;
				}
				case _PMT_TYPE_BUY_X_FREE_Y: { // Gestion des promotions de type : X ACHETE(s) = Y OFFERT(s)
					$offer = ria_mysql_fetch_assoc( $roffer );

					foreach( $ar_products as $key=>$oprd ){
						if( !pmt_products_is_included($cod, $oprd['id']) || $oprd['qte']<$offer['buy_x'] ){
							continue;
						}

						if( $oprd['cod']==$cod['id'] || ($cod['parent'] && $oprd['cod']==$cod['parent']) ){
							continue;
						}

						if( is_numeric($oprd['cod']) && $oprd['cod'] > 0 && pmt_codes_get_type($oprd['cod']) != _PMT_TYPE_REDUC ){
							continue;
						}

						$colisage = fld_object_values_get( array($order, $oprd['id'], $oprd['line']), _FLD_PRD_COL_ORD_PRODUCT );
						if ($colisage === false) {
							$colisage = 0;
						}

						$qte_free = floor( $oprd['qte'] / $offer['buy_x'] );
						$qte_free = ( $qte_free < 1 ? 1 : $qte_free ) * $offer['free_y'];

						$qte_offer = $qte_free;
						if( $oprd['follow_stock'] && $cod['available_stocks'] ){
							$stock = 0;
							$rsto = prd_dps_stocks_get( $oprd['id'], $dps );
							if( $rsto && ria_mysql_num_rows($rsto) ){
								$sto = ria_mysql_fetch_assoc( $rsto );
								if( $config['tnt_id'] != 4 ){
									$stock = $sto['qte'] - $sto['prepa'];
								}else{
									$stock = $sto['qte'] - $sto['prepa'] - $sto['res'] + $sto['com'];
								}
							}

							if( ($oprd['qte'] + $qte_free) > $stock ){
								$old_qte = $oprd['qte'];
								$pay = $free = $tpay = $tfree = 0;
								while( true ){
									$oprd['qte'] = $oprd['qte'] - $offer['buy_x'];
									$tpay += $offer['buy_x'];
									$tfree += $offer['free_y'];

									if( $tpay>$old_qte || ($tpay+$tfree)>$stock ){
										break;
									}

									$pay = $tpay;
									$free = $tfree;
								}

								$diff = $old_qte - ($pay + $free);

								ord_products_update( $order, $oprd['id'], ($pay+$diff), -1, $colisage, 0, false, null, false, 0, false );
								$qte_offer = $free;

								$update_total_order = true;
							}
						}

						$r_del = ria_mysql_query('
							select prd_ord_id as ord_id, prd_id, prd_line_id as line_id
							from riashop.ord_products
							where prd_tnt_id = '.$config['tnt_id'].'
								and prd_ord_id = '.$order.'
								and prd_id = '.$oprd['id'].'
								and prd_cod_id = '.$cod['id'].'
						');

						if ($r_del) {
							while ($del = ria_mysql_fetch_array($r_del)) {
								if( !ord_products_del($del['ord_id'], $del['prd_id'], $del['line_id']) ){
									return false;
								}
							}
						}

						$line_id = ord_products_add_free( $order, $oprd['ref'], $oprd['name'], 0, $qte_offer, null, '', _TVA_RATE_DEFAULT, $cod['id'], 0, false, $colisage, 0, false, false, true );
						if( $line_id === false ){
							return false;
						}

						if( array_key_exists($oprd['id'].'-'.$cod['id'], $ar_ord_childs) ){
							ord_products_set_ord_child_id( $order, $oprd['id'], $line_id, $ar_ord_childs[ $oprd['id'].'-'.$cod['id'] ] );
						}
					}

					break;
				}
			}
		}
	}

	// Gestion du frais de port offerts avec les promotions spéciales
	$code_services = ord_orders_get_free_shipping( $order );
	if( is_array($code_services) && sizeof($code_services) ){
		$rord = ria_mysql_query('
			select ord_srv_id as srv_id, ord_str_id as str_id
			from riashop.ord_orders
			where ord_tnt_id='.$config['tnt_id'].'
				and ord_id='.$order.'
		');

		if( $rord && ria_mysql_num_rows($rord) ){
			$ord = ria_mysql_fetch_array( $rord );

			if( ( !$ord['srv_id'] && !$ord['str_id'] ) || ( $ord['str_id'] && in_array(-1, $code_services) ) || in_array($ord['srv_id'], $code_services) ){
				$ar_port = prd_products_get_id( $config['dlv_prd_references'], $config['cat_root'] );
				if( is_array($ar_port) && sizeof($ar_port) && !ord_products_del( $order, array_values($ar_port), false, -1, 0, false ) ){
					return false;
				}
			}
		}
	}

	$reward = false;

	// Application des remises provenant des points de fidélité
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']) {
		$r_reward = rwd_rewards_get( 0, $_SESSION['usr_prf_id'] );
		if ($r_reward && ria_mysql_num_rows($r_reward)) {
			$reward = ria_mysql_fetch_assoc( $r_reward );

			$pts_used = fld_object_values_get( $order, _FLD_ORD_PTS, '', false, true );
			if ($reward['system'] == _RWD_SYSTEM_REMISE && $pts_used >= $reward['nb_pts']) {
				$update_total_order = true;
			}
		}
	}
	// Si une modification sur la commande a été faite, alors on met à jour ses totaux
	if( $update_total_order ){
		$res = ord_orders_update_totals( $order, false, $reward );
	}else{
		ord_orders_set_date_modified( $order );
	}


	return true;
}
// \endcond

// \cond onlydev
/** \ingroup model_promotions_specials
 *	@{
 */
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de calculer les frais de port appliqués à une commande.
 *	@param int $ord_id Obligatoire, identifiant de la commande.
 *	@param bool $ttc Optionnel, détermine si le résultat est retourné TTC (par défaut, il est HT).
 *	@param int $ord_child_id Optionnel, identifiant d'une commande enfant de $ord_id dont on souhaite connaitre le sous-total de port (Null est autorisée pour récupérer les frais de port non rattachés à une commande enfant).
 *
 *	@return float Le montant total HT des frais de port (TTC le cas échéant), ou False en cas d'échec
 */
function ord_orders_port_get( $ord_id, $ttc = false, $ord_child_id = 0 ){

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	global $config;

	// les frais de port doivent être configurés
	if( !is_array($config['dlv_prd_references']) || !sizeof($config['dlv_prd_references']) ){
		return false;
	}

	$sql = '
		select
			sum( '.( $ttc ? 'ifnull(prd_price_ttc, prd_tva_rate * prd_price_ht)' : 'prd_price_ht' ).' * prd_qte ) as total
		from
			riashop.ord_products
		where
			prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '.$ord_id.'
			and prd_parent_id is null
			and prd_ref in ("'.implode('", "', array_slashes($config['dlv_prd_references'])).'")
	';

	if( is_numeric($ord_child_id) && $ord_child_id > 0 ){
		$sql .= ' and prd_ord_child_id = '.$ord_child_id;
	}elseif( $ord_child_id === null ){
		$sql .= ' and prd_ord_child_id is null';
	}

	$rprd = ria_mysql_query($sql);

	if( !$rprd || !ria_mysql_num_rows($rprd) ){
		return false;
	}

	return ria_mysql_result($rprd, 0, 'total');

}
// \endcond

// \cond onlyria
/**	Cette fonction recupère la ref du frais de port si present dans la commande
 *
 *	@param int $order_id Obligatoire, Identifiant de la commande
 *	@param bool $have_srv Optionnel, mettre à true pour vérifier si un service de livraison est active sur la commande (dans le cas d'une promotion avec frais de port offert)
 *
 *	@return Si $have_srv est à true, retourne true si la commande à des frais de port sinon false
 *	@return Par défaut : false en cas d'erreur sinon un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- ref : la référence du frais de port de la commande ,false si aucun frais de port
 */
function ord_orders_port_has( $order_id, $have_srv=false ){
	if( !ord_orders_exists($order_id) ){
		return false;
	}
	global $config;

	$res = ria_mysql_query('
		select prd_ref as ref
		from riashop.ord_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$order_id.'
			and prd_parent_id is null
			and prd_ref in (\''.implode('\',\'',$config['dlv_prd_references']).'\')
	');

	if( !$have_srv ){
		return $res;
	}

	if( $res && ria_mysql_num_rows($res) ){
		return true;
	}

	$result = false;

	$order_promotions = ord_orders_promotions_get($order_id);
	if (is_array($order_promotions)) {
		foreach($order_promotions as $ord_promotion){
			$rpmt = pmt_codes_get( $ord_promotion['pmt_id'] );

			if (!$rpmt || !ria_mysql_num_rows($rpmt)) {
				continue;
			}

			$pmt = ria_mysql_fetch_assoc( $rpmt );

			if( $pmt['free_shipping'] && pmt_codes_is_applicable($pmt['code'], $ord_promotion['ord_id']) ){
				$result = true;
				break;
			}
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime d'une commande tous les articles frais de port
 *	@param int $ord Obligatoire, Identifiant de la commande
 *	@param int $catroot Optionnel, Identifiant de la catégorie racine de l'arborescence où les frais de port sont classés
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_port_del( $ord, $catroot = 0 ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	$ids = prd_products_get_id( $config['dlv_prd_references'], $catroot );
	if( $ids===false || !sizeof($ids) ){
		return false;
	}

	return ord_products_del( $ord, array_values($ids) );
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime physiquement une commande de la base (au lieu, traditionnellement, de lui affecter le statut 10 et de la masquer).
 *	Toute erreur est fatale (sortie à False). La suppression de la commande est la dernière action effectuée.
 *	Il manque encore des actions possibles : suppression des utilisateurs liés s'il s'agit d'un modèle, suppression des acomptes et échéances, etc...
 *	@param int $ord Obligatoire, identifiant de la commande.
 *	@param bool $del_rows Optionnel, détermine si les lignes doivent être supprimées en cascade.
 *	@param bool $set_null Optionnel, détermine si l'identifiant lié (prd_ord_id) dans les lignes de facture, BL et PL, doit être mis à NULL.
 *	@param bool $del_subscriptions Optionnel, détermine si les abonnements liés doivent être supprimées.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_orders_del_sage( $ord, $del_rows = false, $set_null = false, $del_subscriptions = false ){
	if( !is_numeric($ord) || $ord <= 0 ){
		return false;
	}

	global $config;

	/* mise à NULL des lignes de factures, BL et PL liés à la commande */
	if( $set_null ){

		// récupère la liste des PLs impactés
		$ar_pl = array();

		$sql_get = '
			select distinct prd_pl_id as id
			from riashop.ord_pl_products
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord.'
		';

		if( $rpl = ria_mysql_query($sql_get) ){
			while( $pl = ria_mysql_fetch_assoc($rpl) ){
				$ar_pl[] = $pl['id'];
			}
		}

		$r = ria_mysql_query('
			update riashop.ord_pl_products
			set prd_ord_id = NULL
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord.'
		');

		if( !$r ){
			return false;
		}else{
			foreach( $ar_pl as $pl_id ){
				ord_pl_set_date_modified( $pl_id );
			}
		}

		// récupère la liste des BLs impactés
		$ar_bl = array();

		$sql_get = '
			select distinct prd_bl_id as id
			from riashop.ord_bl_products
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord.'
		';

		if( $rbl = ria_mysql_query($sql_get) ){
			while( $bl = ria_mysql_fetch_assoc($rbl) ){
				$ar_bl[] = $bl['id'];
			}
		}

		$r = ria_mysql_query('
			update riashop.ord_bl_products
			set prd_ord_id = NULL
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord.'
		');

		if( !$r ){
			return false;
		}else{
			foreach( $ar_bl as $bl_id ){
				ord_bl_set_date_modified( $bl_id );
			}
		}

		// récupère la liste des factures impactées
		$ar_inv = array();

		$sql_get = '
			select distinct prd_inv_id as id
			from riashop.ord_inv_products
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_ord_id = '.$ord.'
		';
		if( $rinv = ria_mysql_query($sql_get) ){
			while( $inv = ria_mysql_fetch_assoc($rinv) ){
				$ar_inv[] = $inv['id'];
			}
		}

		$r = ria_mysql_query('
			update riashop.ord_inv_products
			set prd_ord_id = NULL
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord.'
		');

		if( !$r ){
			return false;
		}else{
			foreach( $ar_inv as $inv_id ){
				ord_invoices_set_date_modified( $inv_id );
			}
		}

	}

	// Suppression des abonnements relatifs à la commmande
	if( $del_subscriptions ){

		$r = ria_mysql_query('
			delete from riashop.ord_subscriptions
			where ops_tnt_id = '.$config['tnt_id'].' and ops_ord_id = '.$ord.'
		');

		if( !$r ){
			return false;
		}

	}

	// Suppression des lignes
	if( $del_rows ){

		$r = ria_mysql_query('
			delete from riashop.ord_products
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord.'
		');

		if( !$r ){
			return false;
		}

	}

	// Suppression de la commande
	return ria_mysql_query('
		delete from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$ord.'
	');

}
// \endcond

// \cond onlydev
/** \defgroup models_orders_delivery Livraison
 * 	\ingroup models_orders
 *	@{
 */
// \endcond

/**	Cette fonction permet le renseignement de l'information 'Service de livraison' pour une commande donnée.
 *
 *	@param int $ord Identifiant interne de la commande
 *	@param $srv Identifiant interne du service de livraison. Si cette valeur ne correspond à aucun service de livraison enregistré, le champ est mis à null
 *	@param $recalculate Détermine si le montant de la commande doit être recalculé (Vrai par défaut, Faux dans le cas d'un changement de service post-finalisation)
 *	@param $check_service Optionnel, permet de rendre obtionnel la vérification du service de livraison par rapport au contenu de la commande
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function ord_orders_set_dlv_service( $ord, $srv=false, $recalculate=true, $check_service=true ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}
	if( !is_numeric($srv) || $srv<=0 ){
		$srv = 'null';
	}

	// check pour chaque produit si le service de livraison est disponible si non retourne false
	if($check_service && is_numeric($srv) && $srv >= 0 ){
		$rproducts = ord_products_get( $ord );
		if( $rproducts ){
			while( $p = ria_mysql_fetch_array($rproducts) ){
				if( prd_products_is_port($p['ref']) ){
					continue;
				}

				if( !dlv_products_is_available( $p['id'], $srv ) ){
					return false;
				}
			}
		}
	}

	$res = ria_mysql_query('update riashop.ord_orders set ord_srv_id='.$srv.' where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord);
	if( $res && $recalculate ){
		ord_orders_update_totals($ord); // Met à jour les totaux avec le montant de l'option cadeau
	}
	return $res;
}

/** Cette fonction permet le renseignement de l'information 'Devise utilisée' pour une commande donnée
 *
 * 	@param int $ord Identifiant interne de la commande
 * 	@param string $currency Code de la monaie utilisé respectant la norme ISO 4217
 *
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function ord_orders_set_currency( $ord, $currency ){
	global $config;

	if( !is_numeric($ord) || $ord <= 0 ){
		return false;
	}

	if( !is_string($currency) || trim($currency) == "" ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_currency = "'.addslashes($currency).'"
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$ord.'
	');

}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer l'identifiant du vendeur associé a une commande donnée.
 *	@param int $ord Obligatoire, identifiant de la commande
 *	@return int l'identifiant du vendeur si associé à la commande
 *	@return null si aucun vendeur n'est associé
 *	@return bool false en cas d'erreur
 */
 function ord_orders_get_seller_id( $ord ){
	if( !is_numeric($ord) || $ord <= 0 ){
		return false;
	}

	global $config;

	$result = ria_mysql_query('
		select ord_seller_id
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
		and ord_id='.$ord.'
	');

	if( !$result || !ria_mysql_num_rows($result) ){
		return false;
	}

	return ria_mysql_result($result, 0, 'ord_seller_id');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de forcer la mise à jour du status de la commande.
 * 	@param int $ord_id Obligatoire, identifiant d'une commande
 * 	@param int $state Obligatoire, nouveau statut à appliquer (attention aucun contrôle)
 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function ord_orders_set_statut( $ord_id, $state ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	if( !is_numeric($state) || $state <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update ord_orders
		set ord_state_id = '.$state.'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( !$res ){
		return false;
	}

	// Ajout du nouveau statut dans l'historique (seulement si le statut à changé)
	if( ria_mysql_affected_rows() > 0 ){
		$res = ord_orders_states_add($ord_id, $state);
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'association d'un identifiant de vendeur a une commande donnée.
 *	@param int $ord Obligatoire, Identifiant de la commande à modifier
 *	@param int $seller Obligatoire, Identifiant de vendeur à attribuer à la commande (ou NULL pour détacher le représentant)
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function ord_orders_set_seller_id( $ord, $seller ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	if( $seller!==null && ( !is_numeric($seller) || $seller<=0 ) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_seller_id='.( $seller!==null ? $seller : 'NULL' ).'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'association d'un identifiant de vendeur comme commissionnaire de la commande
 *	@param integer $ord_id Identifiant de la commande à modifier
 *	@param integer $seller_id Identifiant de vendeur a attribuer à la commande (ou NULL pour détacher le représentant)
 *	@return bool True en cas de succès et false en cas d'erreur
 */
function ord_orders_set_seller_com( $ord_id, $seller_id ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}
	if( $seller_id!==null && ( !is_numeric($seller_id) || $seller_id<=0 ) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
			set ord_seller_com='.( $seller_id!==null ? $seller_id : 'NULL' ).'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord_id
	);

}
// \endcond


// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/**	Cette fonction permet le renseignement de l'information 'Magasin de livraison' pour une commande donnée.
 *	Appellée sans le second argument, cette fonction efface le champ 'Magasin de livraison'
 *	@param int $ord Obligatoire, Identifiant de la commande
 *	@param int $str Optionnel, Identifiant du magasin dans lequel la commande sera livrée ou false pour détacher
 *			la commande de tout magasin de livraison
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_set_dlv_store( $ord, $str=false ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}
	if( !is_numeric($str) || $str<=0 ){
		$str = 'null';
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_str_id='.$str.'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}

/** Cette fonction permet d'enregistrer la troisième adresse sur la commande
 * 	@param int  $ord_id Obligatoire, identifiant de la commande
 * 	@param int $adr_id Optionnel, identifiant de l'adresse
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_set_adr_final( $ord_id, $adr_id=false ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	if( !is_numeric($adr_id) || $adr_id <= 0 ){
		$adr_id = 'null';
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_adr_final='.$adr_id.'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord_id
	);
}

// \cond onlydev
/// @}
// \endcond


// \cond onlyria
/**	Cette fonction permet de récupérer l'identifiant du magasin utilisé pour sa livraison
 *	@param int $ord_id Identifiant de la commande
 *	@return int|bool L'identifiant du magasin (pouvant être null), False si le paramètre obligatoire est omis ou faux
 */
function ord_orders_get_dlv_store( $ord_id ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_str_id
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$ord_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['ord_str_id'];
}
// \endcond

/** Cette fonction permet de vérifier si un service de livraison est utilisable pour une commande.
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@param int $srv Obligatoire, identifiant d'un service de livraison
 *	@return bool True si le service est utilisable, False dans le cas contraire
 */
function ord_prd_services_is_available( $ord, $srv ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	if( !is_numeric($srv) || $srv<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from riashop.prd_services_unavailable
			join riashop.ord_products on (psu_tnt_id=prd_tnt_id and psu_prd_id=prd_id)
		where psu_tnt_id='.$config['tnt_id'].'
			and psu_srv_id='.$srv.'
			and prd_ord_id='.$ord.'
	');

	if( $res && ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/**	Cette fonction rafraîchit le contenu d'un panier (tarifs, références, libellés).
 *
 *	Ce rafraichissement ne peut avoir lieu que sur les paniers et paniers enregistrés.
 *	Les paniers établis par des vendeurs ne peuvent pas non plus être rafraichits, en
 *	raison des tarifs négociés.
 *
 *	@param int $order Obligatoire, identifiant de la commande à rafraichir
 *	@param int $reseller Optionnel, identifiant du revendeur sur lequel les tarifs sont basés ou un tableau contenant : 'id' => identifiant du revendeur et 'wst_id' => identifiant d'un site
 *	@param bool $update_order Optionnel, par défaut le contenu de la commande est mis à jour, mettre False pour ne le mettre que si sa dernière modification remonte à plus de 30 minutes
 *	@param bool $refresh_promo Optionnel, par défaut les promotions de la commande sont mises à jour, mettre False pour ne pas faire la modification
 *	@param bool $return_deleted Optionnel, par défaut à false, mettre true pour retourner un tableau des références supprimées
 *
 *	@return bool true en cas de succès ou bien un tableau des références produits supprimées qui n'existent plus (pouvant être vide) si $return_deleted est à true
 *	@return bool false en cas d'erreur
 *
 *	@todo Revoir le traitement du prix par défaut
 *
 */
function ord_orders_refresh( $order, $reseller=false, $update_order=true, $refresh_promo=true, $return_deleted=false ){
	global $config;

	if( !is_numeric($order) ){
		return false;
	}

	// Transforme le paramètre $reseller en tableau s'il s'agit d'un numérique supérieur à zéro
	// Ce tableau est toujours constitué de 'id' => identifiant du revendeur et 'wst_id' => identifiant d'un site (0 par défaut)
	if( is_numeric($reseller) && $reseller > 0 ){
		$reseller = [
			'id' => $reseller,
			'wst_id' => 0,
		];
	}

	// Charge les informations nécessaires sur la commande
	$rord = ria_mysql_query('
		select
			ord_state_id as state, ord_usr_id as usr_id, ord_seller_id as seller, ord_date_archived as archive,
			ord_pmt_id as pmt_id, ord_date_modified as date_modified
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$order.'
	');
	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}
	$ord = ria_mysql_fetch_array($rord);

	// Vérifie qu'il s'agit bien d'un panier
	$inc_devis = isset($config['ord_products_devis']) && $config['ord_products_devis'];
	if( !in_array($ord['state'], ord_states_get_uncompleted($inc_devis)) && $ord['state'] != _STATE_MODEL ){
		return false;
	}

	if( $ord['archive'] ){ // Les commandes archivées ne sont pas actualisables
		return false;
	}

	// Récupère la référence de l'option cadeau
	$options = dlv_options_get();
	$gift = $options['gift-ref'];

	// Récupère la catégorie tarifaire
	$prc = isset($_SESSION['usr_prc_id']) && is_numeric($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : $config['default_prc_id'];
	$user = is_numeric($ord['usr_id']) && $ord['usr_id']>0 ? $ord['usr_id'] : 0;
	if( $user ){
		$prc = gu_users_get_prc( $user );
	}

	// Détermine l'arrondi du prix HT
	$round_price = !prd_prices_categories_get_ttc( $prc );

	// Détermine le ratio de vente au poids
	$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;

	// Contrôles sur les points de fidélité
	$user_balance = gu_users_get_rewards_balance($user);
	$enable_reward_products = $arr_changed_prd = $prd_to_delete = array();

	$res_prd_has_change = ria_mysql_query('
		select prd_id as id, rwc_sell_points as new_points
		from riashop.ord_products
		left join rwd_catalogs on prd_id = rwc_prd_id
		where prd_tnt_id='.$config["tnt_id"].'
			and prd_ord_id ='.$order.'
			and prd_sell_points > 0
			and prd_price_ht <= 0
			and prd_sell_points <> rwc_sell_points
	');

	if( $res_prd_has_change && ria_mysql_num_rows($res_prd_has_change) ){
		$arr_changed_prd = array();
		while($changed_prd = ria_mysql_fetch_assoc($res_prd_has_change)){
			$arr_changed_prd[$changed_prd["id"]] = $changed_prd;
		}
	};

	$res_prd = ria_mysql_query('
		select prd_id as id, prd_sell_points as points, prd_qte as qte, prd_line_id as line_id
		from riashop.ord_products
		left join rwd_catalogs on prd_id = rwc_prd_id
		where prd_tnt_id='.$config["tnt_id"].'
			and prd_ord_id ='.$order.'
			and prd_sell_points > 0
			and prd_price_ht <= 0
			');

	if( $res_prd && ria_mysql_num_rows($res_prd) ){
		while($prd = ria_mysql_fetch_assoc($res_prd)){
			if(array_key_exists($prd["id"],$arr_changed_prd)){
				$prd['points'] = $arr_changed_prd[$prd["id"]]["new_points"];
				$prd['changed_points'] = true;
			}else{
				$prd['changed_points'] = false;
			}
			$total = $prd['points'] * $prd['qte'];
			if($user_balance > 0 && $user_balance >= $total){
				$prd['changed'] = false;
				$user_balance = $user_balance - $total;
				$enable_reward_products[$prd["id"]] = $prd;
			}else if($user_balance > 0 ){
				$qte = $prd['qte'];
				for($i = 0 ; $i <= $qte; $i++){
					if($user_balance >= $prd['points']){
						$user_balance = $user_balance - $prd['points'];
					}else{
						$prd['qte']--;
					}
				}
				if($prd['qte'] > 0){
					$prd['changed'] = true;
					$enable_reward_products[$prd["id"]] = $prd;
				}else{
					$prd_to_delete[$prd["id"]] = $prd;
				}
			}else{
				$prd_to_delete[$prd["id"]] = $prd;
			}
		}
	}

	// Procède au rafraichissement de chaque ligne de produit (y compris les lignes enfants)
	$ord_products = ord_products_get( $order, false, 0, '', null, false, 1 );

	$list_ref_deleted = [];
	while( $ord_prd = ria_mysql_fetch_assoc($ord_products) ){
		if( !$update_order && strtotime($ord_prd['date_modified']) > strtotime('-30 minutes') ){
			if( strtotime($ord_prd['prd_date_modified']) < strtotime('-30 minutes') ){
				continue;
			}
		}
		if ( $ord_prd['id'] == 0 ){ // Si le produit est un interligne, alors on passe.
			continue;
		}

		// Fidélité : vérifier qu'il est dans la liste enabled
		if(array_key_exists($ord_prd["id"],$enable_reward_products)){
			if($enable_reward_products[$ord_prd["id"]]["line_id"] == $ord_prd["line"]){
				if($enable_reward_products[$ord_prd["id"]]["changed_points"]){
					ord_products_update_free( $order, $ord_prd["ref"], $ord_prd["name"], 0, $enable_reward_products[$ord_prd["id"]]["qte"], false, false, false, 0, 0, 0, false, false, 0, $ord_prd["line"], $enable_reward_products[$ord_prd["id"]]["points"]);
				}
				else if($enable_reward_products[$ord_prd["id"]]["changed"]){
					ord_products_update_free( $order, $ord_prd["ref"], $ord_prd["name"], 0, $enable_reward_products[$ord_prd["id"]]["qte"], false, false, false, 0, 0, 0, false, false, 0, $ord_prd["line"]);
				}
			}
		}else if(array_key_exists($ord_prd["id"],$prd_to_delete)){
			if($prd_to_delete[$ord_prd["id"]]["line_id"] == $ord_prd["line"]){
				ord_products_del( $order, $ord_prd['id'], $ord_prd['line'] );
			}

		}

		// Vérifie que le produit existe encore et qu'il est publié
		if( !( $src_prd = ria_mysql_fetch_array(prd_products_get( $ord_prd['id'] )) ) ){
			// Supprime le produit s'il ne s'agit pas de frais de port ou d'une option cadeau
			if( !prd_products_is_port( $ord_prd['ref'] ) && $ord_prd['ref'] != $gift ){
				ord_products_del( $order, $ord_prd['id'], $ord_prd['line'] );
				$list_ref_deleted[] = $ord_prd['ref'];
			}
		}else{

			$no_check = false; // Pourrait être remplacé par "continue;"
			if( isset($config['order_refresh_check_publication']) && $config['order_refresh_check_publication'] ){
				// pas un produit frais de port
				// pas une référence cadeau
				// pas de cod_id
				// pas un composant de nomenclature
				if( !prd_products_is_port( $ord_prd['ref'] ) && $ord_prd['ref'] != $gift && !$ord_prd['cod'] && !$ord_prd['parent'] ){

					// !bug : sur certaines boutiques (ex : a&co), le produit enfant "childonly" n'est pas publié, mais le parent l'est
					$rp_pub = prd_products_get_simple( $ord_prd['id'], '', true, 0, false, false, false, false, array('childs' => true) );

					if( !$rp_pub || !ria_mysql_num_rows($rp_pub) ){
						ord_products_del( $order, $ord_prd['id'], $ord_prd['line'] );
						$no_check = true;
					}
				}
			}

			// Aucune mise à jour pour le site vendeur de BigShip, s'il s'agit d'un produit hors-catalogue
			$is_bs_vendeur_hors_cat = $config[ 'wst_id' ] == 4 && $ord_prd['ref'] == 'DIVERS';

			if( !$no_check && !$is_bs_vendeur_hors_cat ){
				// détermine s'il s'agit d'une ligne dont le prix a été arbitrairement fixé ou négocié (ou s'il s'agit d'un composant de nomenclature variable non tarifé)
				$negociate = fld_object_values_get( array( $order, $ord_prd['id'], $ord_prd['line'] ), _FLD_PRD_ORD_FREE, '', false, true );
				$negociate = $negociate==='Oui' || $negociate==='oui' || $negociate || $ord['state']==_STATE_MODEL;

				if( !$negociate ){
					if( is_numeric($ord_prd['parent']) && $ord_prd['parent']>0 ){
						if( !is_numeric($ord_prd['price_ht']) || $ord_prd['price_ht']==0 ){
							$negociate = true;
						}
					}
				}

				// le nom du produit n'est pas mis à jour pour les fabrications Sodip
				$up_name = true;
				if( in_array($config['tnt_id'], array(11,53)) && in_array($config['wst_id'], array(54,55,91,92)) && substr($ord_prd['ref'], strlen($ord_prd['ref']) - 2) == '-1' ){
					$up_name = false;
				}

				if (isset($GLOBALS['product_add_admin']) && $GLOBALS['product_add_admin']){
					$up_name = false;
				}

				// S'il s'agit d'une ligne négociée, le nom du produit est potentiellement réécrit
				// Dans ce cas, il ne faut pas modifier le nom du produit sur la ligne
				if( $negociate ){
					$up_name = false;
				}

				if( $negociate || (is_numeric($ord_prd['cod']) && $ord_prd['cod']>0) ){
					$sql = '
						update riashop.ord_products
						set prd_ref = "'.addslashes( $src_prd['ref'] ).'"
					';

					// pour les produits frais de port, le nom n'est pas mis à jour car il peut s'agir d'un nom spécifié précisément pour le service de livraison (Animal & Co)
					if( !prd_products_is_port($src_prd['ref']) && $up_name ){
						$sql .= '
							, prd_name = "'.addslashes($src_prd['name']).'"
						';
					}

					$sql .= '
						where prd_tnt_id = '.$config['tnt_id'].'
							and prd_ord_id = '.$order.'
							and prd_id = '.$ord_prd['id'].'
							and prd_line_id = '.$ord_prd['line'].'
					';

					ria_mysql_query($sql);
				}else{
					if( in_array($config['tnt_id'], [977, 1043, 998]) ){
						$dps = ria_mysql_result( ria_mysql_query('
							select prd_dps_id as dps
							from ord_products
							where prd_tnt_id = '.$config['tnt_id'].'
								and prd_ord_id = '.$order.'
								and prd_id = '.$ord_prd['id'].'
								and prd_line_id = '.$ord_prd['line'].'
						'), 0, 'dps');

						// Le dépôt est obligatoire
						if( !is_numeric($dps) || $dps <= 0 ){
							return false;
						}

						// Détermine le code du client
						$cnd_usr_code = 0;
						if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] > 0 ){
							$cnd_usr_code = gu_users_get_parent_ref( $_SESSION['admin_view_user'], true );
						}elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ){
							$cnd_usr_code = gu_users_get_parent_ref( $_SESSION['usr_id'], true );
						}

						$r_price = ria_mysql_query('
							select prc_value, prc_currency
							from prc_prices
								join prc_price_conditions on (ppc_tnt_id = '.$config['tnt_id'].' and ppc_prc_id = prc_id)
							where prc_tnt_id = '.$config['tnt_id'].'
								and prc_prd_id = '.$ord_prd['id'].'
									and prc_dps_id = '.$dps.'
								and prc_is_deleted = 0
									and ppc_fld_id = '._FLD_USR_REF.'
									and ppc_value = "'.htmlspecialchars( $cnd_usr_code ).'"
						');

						if( !$r_price || !ria_mysql_num_rows($r_price) ){
							return false;
						}

						$price = ria_mysql_fetch_assoc( $r_price );

						$tva_rate = 1.000;
						$price_ht = $price['prc_value'];

					}elseif($config['tnt_id'] == 1024){
						// détermine le client
						$user = $ord['usr_id'] > 0 ? $ord['usr_id'] : 0;
						$cnt_code = gu_users_get_cnt_code($user);
						$cnt_code = str_replace( '"', '', $cnt_code );

						$sql = '
							select
								prc_id as id,
								prc_value as price_ht,
								ptv_tva_rate as tva_rate,
								ptv_cnt_code as cnt_code,
								prc_name as name,
								prc_prd_id as prd,
								prc_cat_id as cat,
								prc_is_promotion as "is-promotion",
								prc_grp_id as grp_id
							from
								riashop.prc_prices
							inner join
								riashop.prc_tvas
							on
									ptv_date_deleted is null
								and ptv_tnt_id='.$config['tnt_id'].'
								and ptv_prd_id='.$ord_prd['id'].'
								and ptv_cnt_code = "'.htmlspecialchars($cnt_code).'"

							where
									prc_is_deleted=0
								and prc_tnt_id='.$config['tnt_id'].'
								and ( prc_date_start<=now() ) and ( prc_date_end>now() )
								and prc_prd_id='.$ord_prd['id'].'
								and exists (
									select 1 from riashop.prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id='.$config['tnt_id'].'
								)
								and prc_qte_min=1
								and prc_is_promotion=0
							order by prc_type_id asc, prc_value asc
						';

						$r_price = ria_mysql_query($sql);

						if( !ria_mysql_num_rows($r_price) ){
							continue;
						}
						// récupère le conditionnement dans lequel l'article a été commandé
						$val = fld_object_values_get( array( $order, $ord_prd['id'], $ord_prd['line'] ), _FLD_PRD_COL_ORD_PRODUCT );
						$colisage = !is_numeric($val) || $val<=0 ? 0 : $val;

						$price = ria_mysql_fetch_assoc( $r_price );

						$price_ht = $price['price_ht'];

						// Détermine l'existence d'une promotion
						$pmt = prc_promotions_get( $ord_prd['id'], $user, 0, $ord_prd['qte'], $colisage );

						if( is_array($pmt) && sizeof($pmt) && $pmt['price_ht']<$price_ht ){
							$price_ht = $pmt['price_ht'];
						}
						$tva_rate = $price['tva_rate'];

					}else{
						// récupère le conditionnement dans lequel l'article a été commandé
						$val = fld_object_values_get( array( $order, $ord_prd['id'], $ord_prd['line'] ), _FLD_PRD_COL_ORD_PRODUCT );
						$colisage = !is_numeric($val) || $val<=0 ? 0 : $val;

						$price_ht = $ord_prd['price_ht'];
						$tva_rate = $ord_prd['tva_rate'];
						if( !$tva_rate ){
							$tva_rate = _TVA_RATE_DEFAULT;
						}

						// détermine le tarif standard
						if( $rprice = prd_products_get_price( $ord_prd['id'], $user, $prc, $order, $ord_prd['qte'], $colisage ) ){
							if( $p = ria_mysql_fetch_array($rprice) ){
								$price_ht = $p['price_ht'];
								$tva_rate = $p['tva_rate'];
								if( !$tva_rate ){
									$tva_rate = _TVA_RATE_DEFAULT;
								}
							}
						}

						// Détermine une éventuelle promotion
						$pmt = prc_promotions_get( $ord_prd['id'], $user, $prc, $ord_prd['qte'], $colisage );
						if( is_array($pmt) && sizeof($pmt) && $pmt['price_ht']<$price_ht ){
							$price_ht = $pmt['price_ht'];
						}

						// gestion d'un prix au niveau du revendeur
						if( $config['tnt_id'] == 4 && in_array($config['wst_id'], array(26, 27, 30, 78)) && isset($config['fld_price_public']) ){
							$default = fld_object_values_get( $ord_prd['id'], $config['fld_price_public'] );
							if( is_numeric($default) && $default > 0 ){
								$price_ht = $default / $tva_rate;
							}
						}

						if( is_array($reseller) && ria_array_key_exists(['id', 'wst_id'], $reseller) ){
							require_once('prd/resellers.inc.php');
							$rseller = prd_resellers_get( $ord_prd['id'], $reseller['id'], false, $reseller['wst_id'] );
							if( $rseller && ria_mysql_num_rows($rseller) ){
								$seller = ria_mysql_fetch_array($rseller);
								if( is_numeric($seller['price_ht']) && $seller['price_ht']>0 ){
									$price_ht = $seller['price_ht'];
								}
								if( is_numeric($seller['price_promo_ht']) && $seller['price_promo_ht']!=0 ){
									$price_ht = $seller['price_promo_ht'];
								}
							}
						}

						// Spécifique Extranet Proloisirs, dans le cas où le produit est considéré comme "Produit rouge"
						// Si le produit dispose de ce champ avancé, alors aucune remise n'est possible, sauf s'il est en destockage
						if( $config['tnt_id'] == 4 && $config['wst_id'] == 8 && isset($config['fld_prd_red_price'], $config['fld_array_yes'], $config['fld_prd_destockage']) ){
							$is_red_price = in_array( fld_object_values_get( $ord_prd['id'], $config['fld_prd_red_price'] ), $config['fld_array_yes'] );
							$is_destockage = in_array( fld_object_values_get($ord_prd['id'], $config['fld_prd_destockage']), $config['fld_array_yes'] );
							if( $is_red_price && !$is_destockage ){
								$price_ht = $p['price_ht'];
							}
						}

						// Détermine, pour les articles vendus au poids sans colisage, le prix pour le poids ( p/r au prix au kilo )
						if( $ord_prd['weight_net']>0 && $ord_prd['weight_net']!=$ratio && $ord_prd['sell_weight'] && !$colisage ){
							$price_ht = $price_ht * ( $ord_prd['weight_net'] / $ratio );
						}
						if( $round_price && !$colisage ){
							$price_ht = round( $price_ht, $config['round_digits_count'] );
						}
					}

					// Rafraichit la ref, le libellé, le tarif, la TVA et l'écotaxe
					ria_mysql_query('
						update riashop.ord_products
							set prd_ref = "'.addslashes( $src_prd['ref'] ).'",
								'.( $up_name ? 'prd_name = "'.addslashes( $src_prd['name'] ).'",' : '' ).'
								prd_ecotaxe = '.$src_prd['ecotaxe'].',
								prd_price_ht = '.($price_ht ?  $price_ht : 0).',
								prd_tva_rate = '.$tva_rate.'
						where prd_tnt_id = '.$config['tnt_id'].'
							and prd_ord_id = '.$order.'
							and prd_id = '.$ord_prd['id'].'
							and prd_line_id = '.$ord_prd['line'].'
					');
				}
			}
		}
	}

	// Met à jour la date de dernière modification de la commande
	ria_mysql_query('update riashop.ord_orders set ord_date_modified=now() where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$order);

	ord_subscriptions_refresh( $order );

	if($refresh_promo){
		ord_orders_refresh_promotions( $order );
		$order_promotions = ord_orders_promotions_get($order);
		if( !empty($order_promotions) ){
			foreach($order_promotions as $order_promotion) {
				ord_orders_refresh_promotions( $order, $order_promotion['pmt_id'] );
			}
		}
	}
	ord_orders_update_totals( $order );

	// Récupère les commandes enfants pour les mettre à jour
	$r_child = ria_mysql_query('select ord_id from riashop.ord_orders where ord_tnt_id = '.$config['tnt_id'].' and ord_parent_id = '.$order);
	if( $r_child ){
		while( $child = ria_mysql_fetch_assoc($r_child) ){
			if( !ord_orders_refresh($child['ord_id'], $reseller, $update_order) ){
				return false;
			}
		}
	}

	return ($return_deleted ? $list_ref_deleted : true);
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les préparations de livraison contenant des lignes relatives
 *	à une commande donnée.
 *	@param int $ord Obligatoire, Identifiant de la commande
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du pl
 *			- piece : numéro de pièce sage
 *			- ref : référence sage
 *			- date : date/heure de création du pl dans sage
 */
function ord_orders_pl_get( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	return ria_mysql_query('
		select pl_id as id, pl_piece as piece, pl_ref as ref, date_format(pl_date,"%d/%m/%Y à %H:%i") as date
		from riashop.ord_pl
		where pl_tnt_id='.$config['tnt_id'].' and pl_masked = 0 and exists (
			select prd_pl_id from riashop.ord_pl_products where prd_tnt_id='.$config['tnt_id'].' and prd_pl_id=pl_id and prd_ord_id='.$ord.'
		)
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les bons de livraison contenant des lignes relatives
 *	à une commande donnée.
 *	@param int $ord Identifiant de la commande
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du bl
 *			- piece : numéro de pièce sage
 *			- ref : référence sage
 *			- date : date/heure de création du bl dans sage
 *			- date_en : date/heure de création du bl dans sage au format d'origine (EN)
 *			- srv_id : identifiant du service de livraison
 */
function ord_orders_bl_get( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	return ria_mysql_query('
		select bl_id as id, bl_piece as piece, bl_ref as ref, date_format(bl_date,"%d/%m/%Y à %H:%i") as date, bl_date as date_en, bl_srv_id as srv_id
		from riashop.ord_bl
		where bl_tnt_id='.$config['tnt_id'].' and bl_masked = 0 and exists (
			select prd_bl_id from riashop.ord_bl_products where prd_tnt_id='.$config['tnt_id'].' and prd_bl_id=bl_id and prd_ord_id='.$ord.'
		)
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les factures contenant des lignes relatives
 *	à une commande donnée.
 *	@param int $ord Obligatoire, Identifiant de la commande
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la facture
 *			- piece : numéro de pièce sage
 *			- ref : référence sage
 *			- date : date/heure de création de la facture dans sage
 */
function ord_orders_invoices_get( $ord ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	return ria_mysql_query('
		select inv_id as id, inv_piece as piece, inv_ref as ref, date_format(inv_date,"%d/%m/%Y à %H:%i") as date
		from riashop.ord_invoices
		where inv_tnt_id='.$config['tnt_id'].' and inv_masked = 0 and exists (
			select prd_inv_id from riashop.ord_inv_products where prd_tnt_id='.$config['tnt_id'].' and prd_inv_id=inv_id and prd_ord_id='.$ord.'
		)
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction crée une nouvelle commande associée à l'utilisateur passé en paramètre.
 *	Elle est utilisée par le processus de synchronisation, et ne devrait pas être utilisée dans le reste de la boutique.
 *
 *	@param int $usr Identifiant de l'utilisateur propriétaire de la commande
 *	@param string $date Date d'enregistrement dans la gestion commerciale
 *	@param int $state Etat dans lequel placer la commande lors de sa création
 *	@param string $state_sage Chaîne de caractère décrivant l'état de la commande dans la gestion commerciale
 *	@param string $piece référence de la pièce sage
 *	@param string $ref référence de la commande sage
 *	@param bool $masked Indique si la commande doit être masquée ou non
 *	@param int $wst_id Facultatif, identifiant du site rattaché à la commande
 *	@param string $date_created Facultatif, date de création de la commande dans l'ERP
 *
 *	@return bool false en cas d'échec
 *	@return int l'identifiant de la commande en cas de succès
 */
function ord_orders_add_sage( $usr, $date, $state, $state_sage, $piece, $ref, $masked, $wst_id=false, $date_created=null ){
	global $config;

	// Vérifie que le compte client existe
	if( !gu_users_exists($usr) ){
		error_log( 'ord_orders_add_sage '.$config['tnt_id'].' : l\'utilisateur '.$usr.' n\'existe pas.' );
		return false;
	}

	// Vérifie que l'état de commande existe
	if( !ord_states_exists($state) ){
		error_log( 'ord_orders_add_sage '.$config['tnt_id'].' : le statut de commande '.$state.' n\'existe pas.' );
		return false;
	}

	// on ne peut pas créer de commande archivée (il faudrait alors spécifier l'état d'origine dans un autre paramètre)
	if( $state==_STATE_ARCHIVE ){
		error_log( 'ord_orders_add_sage '.$config['tnt_id'].' : on ne peut pas créer de commande archivée' );
		return false;
	}

	// Par défaut la devise appliquée sur la commande est EUR (la même que celle définie sur la structure de la table en BDD)
	$currency = 'EUR';

	// On récupère la devise utilisée par le client en fonction de sa catégorie tarifaire
	$usr_currency = gu_users_get_currency( $usr );
	if( trim($usr_currency) != '' ){
		$currency = $usr_currency;
	}

	if( $wst_id===false || $wst_id===0 ){
		$wst_id = $config['wst_id'];
	}else{
		if( !is_numeric($wst_id) || $wst_id<0 ){
			error_log( 'ord_orders_add_sage '.$config['tnt_id'].' : l\'identifiant du site web '.$wst_id.' est invalide' );
			return false;
		}
		$rweb = wst_websites_get( $wst_id );
		if( !$rweb || !ria_mysql_num_rows($rweb) ){
			error_log( 'ord_orders_add_sage '.$config['tnt_id'].' : l\'identifiant du site web '.$wst_id.' est invalide' );
			return false;
		}
		$web = ria_mysql_fetch_array( $rweb );
		if( $web['tnt_id']!=$config['tnt_id'] ){
			error_log( 'ord_orders_add_sage '.$config['tnt_id'].' : l\'identifiant du tenant de la commande '.$web['tnt_id'].' est différent de celui de l\'instance '.$config['tnt_id'] );
			return false;
		}
	}

	$user = ria_mysql_fetch_array(gu_users_get($usr));
	$date = dateheureparse( $date );

	if ($date_created != null) {
		$date_created = dateheureparse( $date_created );
	}

	if( (!isset($config['ord_piece_allow_duplicate']) || $config['ord_piece_allow_duplicate']==false) &&
		$config['sync_global_gescom_type']!=10 && $config['sync_global_gescom_type']!=18 ){
		if( trim($piece)!='' ){

			$rord = ria_mysql_query('
				select ord_id
				from riashop.ord_orders
				where ord_tnt_id='.$config['tnt_id'].'
					and ord_piece="'.addslashes( $piece ).'"
			');

			if( $rord && ria_mysql_num_rows($rord) ){
				$ord = ria_mysql_fetch_array( $rord );
				ord_orders_states_add( $ord['ord_id'], _STATE_CANCEL_MERCHAND );
			}
		}
	}

	$masked = $masked ? '1' : '0';

	$fields = array( 'ord_tnt_id, ord_usr_id, ord_currency, ord_state_id, ord_date, ord_adr_invoices, ord_adr_delivery, ord_state_sage, ord_piece, ord_ref, ord_masked, ord_wst_id' );
	$values = array( $config['tnt_id'], $usr, '\''.addslashes($currency).'\'', $state, '\''.$date.'\'', $user['adr_invoices'], $user['adr_delivery'], '\''.addslashes($state_sage).'\'', '\''.addslashes($piece).'\'', '\''.addslashes($ref).'\'', $masked, $wst_id);

	if( ria_mysql_query( 'insert into riashop.ord_orders('.implode(', ', $fields).') values ('.implode(', ', $values).')' ) ){
		$ord_id = ria_mysql_insert_id();

		// Rafraichit le nombre de commandes passées par l'utilisateur.
		gu_users_update_orders($usr);

		try{
			// Index la commande dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_ORDER,
				'obj_id_0' => $ord_id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		return $ord_id;
	}else{
		error_log( 'ord_orders_add_sage '.$config['tnt_id'].' : la création de la commande a échoué' );
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le renseignement du champ piece après importation d'une commande dans SAGE.
 *	@param int $ord Obligatoire, identifiant de la commande
 *	@param string $piece Obligatoire, numéro de pièce sage
 *	@param bool $no_action Optionnel, si True aucune autre action n'est effectuée (mail, indexation, etc...)
 *	@param bool $no_mail_owner Optionnel, si True et $no_action = False, l'email n'est pas envoyé mais les autres actions sont réalisées
 *	@param bool $update_total Optionnel, si True et $no_action = False, le montant total de la commande est mis à jour
 *	@param bool $apply_promo Optionnel, par défaut les promotions sont prises en compte dans le calcul des totaux, mettre faux pour les ignorer (commande déjà synchronisée avec remise appliquée sur chaque ligne de produit).
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_piece_set( $ord, $piece, $no_action=false, $no_mail_owner=false, $update_total = true, $apply_promo=true ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	if( !isset($config['ord_piece_allow_duplicate']) || $config['ord_piece_allow_duplicate']==false ){
		if( trim($piece)!='' ){
			// empêche les doublons sur le numéro de pièce
			$sql_todel = '
				select ord_id as id
				from riashop.ord_orders
				where ord_tnt_id = '.$config['tnt_id'].' and ord_id != '.$ord.' and ord_piece = "'.addslashes($piece).'"
			';
			if( $rord = ria_mysql_query($sql_todel) ){
				while( $o = ria_mysql_fetch_array($rord) ){
					$sql_up = '
						update riashop.ord_orders
						set ord_state_id = 10, ord_masked = 1
						where ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$o['id'].'
					';
					if( ria_mysql_query($sql_up) ){
						ord_orders_states_add( $o['id'], _STATE_CANCEL_MERCHAND );
					}
				}
			}
		}
	}

	$res = ria_mysql_query('update riashop.ord_orders set ord_piece=\''.addslashes($piece).'\', ord_need_sync=0 where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord);

	if( $res && !$no_action ){
		if($update_total){
			ord_orders_update_totals($ord, false, false, $apply_promo);
		}

		try{
			// Index la commande dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_ORDER,
				'obj_id_0' => $ord,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		if( !$no_mail_owner ){
			ord_alert_shop_owner($ord);
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification d'une commande enregistrée depuis la gestion commerciale.
 *
 *	@param int $id Identifiant interne de la commande
 *	@param $state Etat dans lequel placer la commande
 *	@param $state_sage description de l'état de la commande dans la gestion commerciale
 *	@param $dlv Identifiant interne du service de livraison. Si l'identifiant passé ne correspond à aucun service enregistré, le champ est mis à null (aucun service de livraison)
 *	@param string $ref Référence externe sage
 *	@param $piece Numéro de pièce sage
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_orders_upd_sage( $id, $state, $state_sage, $dlv, $ref, $piece ){
	if( !ord_orders_exists($id) || !ord_states_exists($state) ){
		return false;
	}

	global $config;

	if( !dlv_services_exists($dlv) ){
		$dlv = 'null';
	}

	$res = ria_mysql_query('
		update riashop.ord_orders
		set ord_state_id='.($state==_STATE_ARCHIVE ? 'ord_state_id' : $state).',
			ord_date_archived='.($state==_STATE_ARCHIVE ? 'now()' : 'ord_date_archived').'
			ord_state_sage=\''.addslashes($state_sage).'\',
			ord_srv_id='.$dlv.', ord_ref=\''.addslashes($ref).'\',
			ord_piece=\''.addslashes($piece).'\'
		where ord_tnt_id='.$config['tnt_id'].'
		and ord_id='.$id
	);

	if( $res ){
		try{
			// Index la commande dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_ORDER,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $ref;
}
// \endcond

/** Cette fonction permet la duplication d'une commande existante
 * 	@param integer $ord_id Obligatoire, identifiant de la commande à dupliquer
 * 	@param string $ref Optionnel, référence de la commande à assigner
 * 	@param integer $usr_id Optionnel, identifiant du client à assigner
 * 	@param boolean $admin Optionnel, identifie si l'action a été effecuée depuis l'interface admin
 *
 * 	@return integer|false l'identifiant de la nouvelle commande en cas de succès, false en cas d'échec
 */
function ord_orders_copy($ord_id, $ref=false, $usr_id=false, $admin=false){
	if (!is_numeric($ord_id) || $ord_id <= 0){
		return false;
	}

	if ($usr_id !== false && (!is_numeric($usr_id) || $usr_id < 0)){
		return false;
	}

	global $config;

	// Charger les informations suivant si les paramètres sont fournis ou non, et en fonction de l'état de la commande
	{
		$state = _STATE_BASKET;
		if( ord_orders_exists($ord_id, 0, array(_STATE_BASKET, _STATE_WAIT_PAY, _STATE_BASKET_SAVE, _STATE_DEVIS)) ){
			$state = 'ord_state_id';
		}

		$usr_invoice = 'ord_adr_invoices';
		$usr_delivery = 'ord_adr_delivery';
		if ($usr_id !== false){
			if ($usr_id){
				$r_usr = gu_users_get($usr_id);
				if (!$r_usr || !ria_mysql_num_rows($r_usr)){
					return false;
				}


				$user = ria_mysql_fetch_assoc($r_usr);

				$usr_invoice = $user['adr_invoices'];
				$usr_delivery = $user['adr_delivery'] !== null ? $user['adr_delivery'] : $usr_invoice;
			} else {
				$usr_id = $usr_invoice = $usr_delivery = 'NULL';
			}
		}
	}

	// Copier l'entête + les mises à jour de statut
	{
		$res = ria_mysql_query('
		insert into riashop.ord_orders
		(ord_tnt_id, ord_date, ord_products, ord_total_ht, ord_total_ttc, ord_total_ht_delivered, ord_adr_invoices, ord_adr_delivery, ord_state_id, ord_state_sage, ord_piece, ord_ref, ord_need_sync, ord_date_livr, ord_usr_id, ord_srv_id, ord_str_id, ord_dlv_notes, ord_opt_gift, ord_opt_gift_message, ord_pay_id, ord_card_id, ord_masked, ord_pmt_id, ord_seller_id, ord_comments, ord_cnt_id, ord_date_modified, ord_date_archived, ord_dps_id, ord_relanced, ord_alert_livr, ord_rly_id, ord_wst_id, ord_user_ip, ord_parent_id)
			select
				ord_tnt_id, now(), ord_products, ord_total_ht, ord_total_ttc, ord_total_ht_delivered, '.$usr_invoice.', '.$usr_delivery.', '.$state.', ord_state_sage, NULL, '.($ref !== false ? '"'.$ref.'"' : "ord_ref").', 0, ord_date_livr, '.($usr_id !== false ? $usr_id : "ord_usr_id").', ord_srv_id, ord_str_id, ord_dlv_notes, ord_opt_gift, ord_opt_gift_message, ord_pay_id, ord_card_id, ord_masked, ord_pmt_id, ord_seller_id, ord_comments, ord_cnt_id, ord_date_modified, ord_date_archived, ord_dps_id, ord_relanced, ord_alert_livr, ord_rly_id, ord_wst_id, ord_user_ip, ord_parent_id
			from riashop.ord_orders
			where ord_tnt_id = '.$config['tnt_id'].'
				and ord_id = '.$ord_id.'
		');


		$new_id = ria_mysql_insert_id();
		if (!is_numeric($new_id) || $new_id <= 0) {
			return false;
		}

		if ($state == "ord_state_id"){
			$state = "oos_state_id";
		}

		$res = ria_mysql_query('
		insert into riashop.ord_orders_states
			(oos_tnt_id, oos_ord_id, oos_state_id, oos_datetime, oos_usr_id)
		(select '.$config['tnt_id'].', '.$new_id.', '.$state.', now(), '.$_SESSION['usr_id'].'
		from riashop.ord_orders_states
		where oos_tnt_id = '.$config['tnt_id'].'
			and oos_ord_id = '.$ord_id.'
			limit 1)
		');

		if (!$res) {
			ord_orders_del_sage( $new_id, false );
			return false;
		}
	}

	// Copie les lignes de commandes
	{
		if( $config['tnt_id'] == 56 ){ // Gestion d'un spé pour Cerdys
			$res = ria_mysql_query('
				insert into riashop.ord_products
					(prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_is_free, prd_is_delayed, prd_pos)
				select
					prd_tnt_id, '.$new_id.', prd_id, prd_line_id, if(ifnull(col_qte, 0) > 0, (prd_qte / col_qte), prd_qte), prd_ref, prd_name, prd_price_ht, prd_tva_rate, NULL, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_is_free, prd_is_delayed, prd_pos
				from riashop.ord_products
					left join fld_object_values on (pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = prd_ord_id and pv_obj_id_1 = prd_id and pv_obj_id_2 = prd_line_id and pv_fld_id = 490)
					left join prd_colisage_types on (col_tnt_id = '.$config['tnt_id'].' and col_id = ifnull(pv_value, 0))
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_ord_id = '.$ord_id.'
			');
		}else{
			$res = ria_mysql_query('
				insert into riashop.ord_products
					(prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_is_free, prd_is_delayed, prd_pos)
				select
					prd_tnt_id, '.$new_id.', prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, NULL, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_is_free, prd_is_delayed, prd_pos
				from riashop.ord_products
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_ord_id = '.$ord_id.'
			');
		}

		// Ajoute un "_FLD_PRD_ORD_FREE" flag à "Oui" pour tous les produits contenus dans la commande à dupliquer.
		if( $admin ){
			ria_mysql_query('
				insert into riashop.fld_object_values
					(pv_tnt_id, pv_obj_id_0, pv_obj_id_1, pv_obj_id_2, pv_fld_id, pv_value)
				select prd_tnt_id, prd_ord_id, prd_id, prd_line_id, '._FLD_PRD_ORD_FREE.', "Oui"
				from riashop.ord_products
				where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
			');
		}

		if (!$res) {
			ord_orders_del_sage($new_id, true);
			return false;
		}
	}

	// Copier les informations annexes (promotion)
	{
		$res = ria_mysql_query('
			insert into riashop.ord_promotions_excluded
				(ope_tnt_id, ope_ord_id, ope_cod_id, ope_date_created, ope_date_deleted)
			select
				ope_tnt_id, '.$new_id.', ope_cod_id, ope_date_created, ope_date_deleted
			from riashop.ord_promotions_excluded
			where ope_tnt_id = '.$config['tnt_id'].'
				and ope_ord_id = '.$ord_id.'
		');

		if (!$res) {
			ord_orders_del_sage( $new_id, true );
			return false;
		}

		$res = ria_mysql_query('
			insert into riashop.ord_products_promotions
				(opm_tnt_id, opm_ord_id, opm_prd_id, opm_line_id, opm_cod_id)
			select
				opm_tnt_id, '.$new_id.', opm_prd_id, opm_line_id, opm_cod_id
			from riashop.ord_products_promotions
			where opm_tnt_id = '.$config['tnt_id'].'
				and opm_ord_id = '.$ord_id.'
		');

		if (!$res) {
			ord_orders_del_sage( $new_id, true );
			return false;
		}
	}

	// Copier les champs avancés (entête + ligne)
	// Certains champs avancés ne sont pas dupliqué
	{
		$res = ria_mysql_query('
			insert into riashop.fld_object_values
				(pv_tnt_id, pv_lng_code, pv_obj_id_0, pv_obj_id_1, pv_obj_id_2, pv_fld_id, pv_value)
			select
				pv_tnt_id, pv_lng_code, '.$new_id.', pv_obj_id_1, pv_obj_id_2, pv_fld_id, pv_value
			from riashop.fld_object_values
				join riashop.fld_fields on ((0 = fld_tnt_id or pv_tnt_id = fld_tnt_id) and pv_fld_id = fld_id)
			where pv_tnt_id = '.$config['tnt_id'].'
				and pv_obj_id_0 = '.$ord_id.'
				and fld_cls_id in ('.CLS_ORDER.', '.CLS_ORD_PRODUCT.')
				and fld_id not in ('._FLD_ORD_LINE_DISCOUNT.')
		');

		/*
		 * Applique les valeurs par défaut pour les champs "_FLD_ORD_SIGN_RATE" et "_FLD_ORD_PIPE_SIGN_DATE".
		 *
		 * Valeurs par défaut:
		 * 	   - _FLD_ORD_SIGN_RATE       --> 3 (30% de chance de signature)
		 *     - _FLD_ORD_PIPE_SIGN_DATE  --> 1 mois
		 */
		fld_object_values_set($new_id, _FLD_ORD_SIGN_RATE, 3);
		fld_object_values_set($new_id, _FLD_ORD_PIPE_SIGN_DATE, date('Y-m-d 00:00:00', strtotime('+1 month')));

		if( !$res ){
			ord_orders_del_sage($new_id, true);
			return false;
		}
	}

	// Copier les promotions
	{
		$order_promotions = ord_orders_promotions_get($ord_id);
		foreach($order_promotions as $order_promo){
			ord_orders_promotions_add($new_id, $order_promo['pmt_id'], $order_promo['pmt_type']);
		}
	}

	return $new_id;
}

// \cond onlyria
/** Retourne le status d'une commande
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $old_version Optionnel, convertit l'archivage en state_id 12
 *	@return Retourne l'identifant du status
 */
function ord_orders_get_status( $ord_id, $old_version=false ){
	return ord_orders_get_state( $ord_id, $old_version );
}
// \endcond

// \cond onlyria
/**	Alias pour la fonction ord_orders_state_update()
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param $status Obligatoire, identifiant du statut
 *	@param $status_sage Facultatif, descriptif du statuts Sage (inutile, paramètre utilisé uniquement pour compatibilité ascendante)
 *	@param $notify Optionnel, par défaut, si le changement de statut est valide, la commande est notifiée par email. Mettre False pour ne rien envoyer
 *	@param int $usr_id Optionnel, identifiant du compte qui à lancer la mise à jour du status, utilisé dans dans le cas de validation de commande
 *	@param $collect Optionnel, indique si la commande doit ou non passer en click&collect
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_update_status( $ord_id, $status, $status_sage='', $notify=true, $usr_id=0, $collect=false ){
	return ord_orders_state_update($ord_id, $status, $status_sage, $notify, $usr_id, $collect);
}
// \endcond

/**	Cette fonction met à jour le statut d'une commande.
 *	Dans certains cas, elle envoie également une notification.
 *	Des actions spécifiques sont déclenchées (mise à jour de la date de la commande, décompte des commandes du client) lorsque la commande passe d'un état panier à un état validé.
 *	Il est impossible de rétrograder en panier une commande validée.
 *
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param int $status Obligatoire, identifiant du statut
 *	@param string $status_sage Facultatif, descriptif du statuts Sage (inutile, paramètre utilisé uniquement pour compatibilité ascendante)
 *	@param bool $notify Optionnel, par défaut, si le changement de statut est valide, la commande est notifiée par email. Mettre False pour ne rien envoyer
 *	@param int $usr_id Optionnel, identifiant du compte qui à lancer la mise à jour du status, utilisé dans dans le cas de validation de commande
 *	@param bool $collect Optionnel, indique si la commande doit ou non passer en click&collect
 *
 *	@return bool True en cas de succès, False en cas d'échec. Si le statut est 9 ou 10 (annulation) et que la commande n'est pas trouvée, la fonction retournera True. Si le statut n'est pas modifié, la fonction retournera True.
 */
function ord_orders_state_update( $ord_id, $status, $status_sage='', $notify=true, $usr_id=0, $collect=false ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	if( !is_numeric($usr_id) ){
		return false;
	}

	if( $status == _STATE_MODEL || !ord_states_exists( $status ) ){
		return false;
	}

	// Récupère la commande même si elle est masquée.
	$r_order = ord_orders_get_masked($ord_id);
	if( !$r_order || !ria_mysql_num_rows($r_order) ){
		// Les status d'annulation sont autorisés sur les commandes déjà physiquement supprimées ou masquées.
		return in_array($status, ord_states_get_canceled(true));
	}

	$order = ria_mysql_fetch_assoc($r_order);

	// Charge le statut actuel de la commande.
	$old = $order['state_id'];

	// Nous retournons "true" et n'effectuons aucun changement
	// si le statut actuel est le même que le nouveau.
	if( $old == $status ){
		return true;
	}

	// Vérifie si nous pouvons changer le statut d'une commande vers "Devis".
	if( (!isset($config['ord_products_devis']) || !$config['ord_products_devis']) && $status == _STATE_DEVIS ){
		return false;
	}

	$inc_devis = $config['tnt_id'] == 4;

	$was_cart = in_array($old, ord_states_get_uncompleted($inc_devis));
	$is_cart = in_array($status, ord_states_get_uncompleted($inc_devis));

	// Pas de rétrogradation d'une commande validée vers un panier
	if( $is_cart && !$was_cart ){
		return false;
	}

	// Si la commande passe à l'état disponible en magasin et qu'elle n'a pas de magasin on refuse le changement d'état.
	if( $config['ord_check_has_store_enable'] && $status == _STATE_BL_STORE && $order['str_id'] <= 0 ){
		return false;
	}

	// Click'n'Collect si var de config, commande validée, et livraison en magasin.
	if( (isset($config['click_collect_enabled']) && $config['click_collect_enabled']) && $status ==_STATE_PAY_CONFIRM && (isset($order['str_id']) && trim($order['str_id'])!="") && $collect ){
		$status = _STATE_CLICK_N_COLLECT;
	}

	$res = ria_mysql_query('
		update riashop.ord_orders
		set
			ord_state_sage = "'.addslashes($status_sage).'",
			ord_state_id = ' . $status
			. ($status == _STATE_ARCHIVE ? ', ord_date_archived = now()' : '') . '
		where
				ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id . '
	');

	ord_orders_update_totals( $ord_id );
	ord_orders_update_header( $ord_id );

	// Mis à jour de la commande.
	if( !$res ){
		return false;
	}

	// Envoie d'un event pour des actions spécifique par les sites clients
	EventService\EventProvider::dispatch(
		new EventService\Order\Events\StateUpdated(
			$ord_id,
			$old,
			$status,
			$notify,
			$usr_id
		)
	);

	// Log le changement de statut pour avoir un historique.
	ord_orders_states_add($ord_id, $status, $usr_id);

	// Mise à jour des CGV.
	if( $was_cart && in_array($status, array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_PAY_WAIT_CONFIRM, _STATE_CLICK_N_COLLECT)) ){
		require_once('cgv.inc.php');

		if( $cgv = cgv_versions_get_current() ){
			fld_object_values_set($ord_id, _FLD_ORD_CGV, $cgv);
		}
	}

	// Sauvegarde le prix HT avant remise dans un champ avancé.
	if( isset($config['store_price_before_discounts']) && $config['store_price_before_discounts'] && $status == _STATE_WAIT_PAY ){
		$r_products = ord_products_get($ord_id);

		if( $r_products && ria_mysql_num_rows($r_products) > 0 ){
			while( $product = ria_mysql_fetch_assoc($r_products) ){
				fld_object_values_set(array($ord_id, $product['id'], $product['line']), _FLD_PRD_PRICE_BEFORE_DISCOUNTS, $product['total_ht']);
			}
		}
	}

	// Fournit la date de changement de status vers "Paiement en attente de confirmation".
	// Doit être remplacé par l'utilisation de ord_orders_states.
	if( $status == _STATE_PAY_WAIT_CONFIRM ){
		fld_object_values_set($ord_id, _FLD_ORD_UPD_STATUT, date('Y-m-d'));
	}

	// si un panier est validé, il faut enregistrer l'éventuel utilisation de points de fidélité
		// Application des points de fidélité seulement dans certains statuts
		if( in_array($status, array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_CLICK_N_COLLECT)) && $was_cart ){
			$sum_points = ord_orders_sum_points( $order['id'] );
			if (is_numeric($sum_points) && $sum_points > 0) {
				fld_object_values_set( $ord_id, _FLD_ORD_PTS, $sum_points );
			}

			$pts = fld_object_values_get( $ord_id, _FLD_ORD_PTS );

			if( is_numeric($pts) && $pts>0 && !stats_rewards_objs_exists( CLS_ORDER, $ord_id, false, true ) ){
				$prf = gu_users_get_prf( $order['user'] );
				if( $prf ){
					stats_rewards_add( $order['user'], $prf, 0, true, 'Utilisation de points de fidélité', CLS_ORDER, $ord_id, $pts );
				}
			}
		}

		if( $was_cart || $old == _STATE_DEVIS ){
			gu_users_update_orders( $order['user'] );
			gu_users_update_orders_web( $order['user'] );

			// Mise à jour du nombre d'utilisation des promotions utilisées sur la commande
			$ar_pmt = array();
			$order_promotions = ord_orders_promotions_get($order['id']);
			if( !empty($order_promotions) ){
				foreach($order_promotions as $order_promotion) {
					$ar_pmt[] = $order_promotion['pmt_id'];
				}
			}

			$rp_cod = ria_mysql_query('
				select prd_cod_id as cod_id
				from riashop.ord_products
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_ord_id = '.$ord_id.'
			');

			if( $rp_cod ){
				while( $p_cod = ria_mysql_fetch_assoc($rp_cod) ){
					$ar_pmt[] = $p_cod['cod_id'];
				}
			}

			$rp_cod = ria_mysql_query('
				select opm_cod_id as cod_id
				from riashop.ord_products_promotions
				where opm_tnt_id = '.$config['tnt_id'].'
					and opm_ord_id = '.$ord_id.'
			');

			if( $rp_cod ){
				while( $p_cod = ria_mysql_fetch_assoc($rp_cod) ){
					$ar_pmt[] = $p_cod['cod_id'];
				}
			}

			if( sizeof($ar_pmt) ){
				$ar_pmt = array_unique( $ar_pmt );

				foreach( $ar_pmt as $one_pmt ){
					pmt_codes_used_refresh( $one_pmt );
				}
			}
		}

		// fixe la date des paniers convertis en commande (ou en attente de validation)
		if( $was_cart && in_array($status, array(_STATE_WAIT_VALIDATION, _STATE_PRJ_WAIT_VALIDATION, _STATE_WAIT_PAY)) ){
			$date_cmd = date('Y-m-d H:i:s');

			ria_mysql_query('update riashop.ord_orders set ord_date="'.$date_cmd.'" where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$order['id']);

			// met à jour le statut et la date des commandes enfants
			ria_mysql_query('update riashop.ord_orders set ord_date="'.$date_cmd.'", ord_state_id='.$status.' where ord_tnt_id='.$config['tnt_id'].' and ord_parent_id='.$order['id']);
		}

		// dans le cas d'un devis annulé, on ne notifie pas.
		if( $old == _STATE_DEVIS && in_array($status, array(_STATE_CANCEL_USER,_STATE_CANCEL_MERCHAND)) ){
			$notify = false;
		}

		// pour notifier, les conditions suivantes doivent êtr respectées :
		// 1) Notification explicititement autorisée ( $notify = true )
		// ET
		// 2) Mise à jour du statut des bons de commande depuis la boutique ( $config['allow_orders_update_state'] = true ) OU pas de passage à un document de vente de niveau supérieur (PL, BL ou FA)
		// ET
		// 3) La commande n'est pas un panier en attente de validation des locataires Animal & Co ou TPLC
		if(
			$notify &&
			( $config['allow_orders_update_state'] || !in_array($status, array(_STATE_PREPARATION, _STATE_BL_READY, _STATE_BL_EXP, _STATE_BL_PARTIEL_EXP, _STATE_INVOICE)) ) &&
			( !in_array($status, array(_STATE_PRJ_WAIT_VALIDATION,_STATE_WAIT_VALIDATION)) || !in_array($config['tnt_id'], array(22)) )
		){
			ord_orders_notify( $order['id'] );
		}

		// gestion des stocks sauf sur la boutique goodies Navicom ( aucune certitude que les stocks de Sage seront impactés ) et Pierre Oteiza (?)
		$tnt_resa = ria_array_get($config, 'ord_managed_reserved', true);

		switch( $status ){
			case _STATE_WAIT_PAY : {
				try{
					// Index la commande dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
						'cls_id' => CLS_ORDER,
						'obj_id_0' => $order['id'],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}

				stats_add_order_completed($order['total_ht']);
				if (!$tnt_resa) {
					break;
				}
				// Gestion du déstockage lors de la conversion du panier
				// Vérification si la commande était un panier avant ou un devis et si le tenant a l'option de gestion des stocks
				//vérification si on doit faire la gestion des stock pour ce tenant
				$manage_stock = ($was_cart || $old == _STATE_DEVIS ) && $tnt_resa;
				if (!$manage_stock) {
					break;
				}

				//Récupération de l'utilisateur pour vérifier le profile
				$rusr = gu_users_get( $order['user'] );
				if (!$rusr || !ria_mysql_num_rows($rusr)) {
					break;
				}
				$usr = ria_mysql_fetch_array($rusr);
				if (!$usr) {
					break;
				}
				//Vérification du profile
				// si le client n'est ni un admin, un représentant ou un fournisseur on gère les stocks
				// si le client est synchronisé ou n'a pas de référence on gère les stocks
				$user_manage_stock = !in_array($usr['prf_id'], array(PRF_ADMIN, PRF_SELLER, PRF_SUPPLIER)) && ( $usr['is_sync']==1 || $usr['ref']=='' );
				if (!$user_manage_stock) {
					break;
				}
				// Récupération du dépôt impacté
				$dps = prd_deposits_get_main();
				if( isset($config[ 'prd_deposits' ]) && $config[ 'prd_deposits' ]=='use-customer' && prd_deposits_exists( $usr['dps_id'] ) ){
					$dps = $usr['dps_id'];
				}
				//surcharge du dépôt par celui sur la commande
				if( isset($config['stock_used_dps_order']) && $config['stock_used_dps_order'] ){
					$dps_ord = ord_orders_get_deposit( $order['id'] );
					if( is_numeric($dps_ord) && $dps_ord ){
						$dps = $dps_ord;
					}
				}
				// récupération des produits pour vérifier les stocks
				$products = ord_products_get($order['id']);
				if (!$products || !ria_mysql_num_rows($products)) {
					break;
				}
				// pour chaque produit on fait la gestion du stock si nescéssaire.
				while( $p = ria_mysql_fetch_array($products) ){
					// gestion du stock que si le produit est suivie en stock
					if( prd_products_is_follow_stock( $p['ref'] ) ){
						$altern = false;

						$line_dps = $dps;
						if( isset($config['stock_used_dps_order']) && $config['stock_used_dps_order'] && $p['line_dps_id'] > 0 ){
							$line_dps = $p['line_dps_id'];
						}
						if( $altern ){
							$rstk = prd_dps_stocks_get( $p['id'], 57 );
							if( !$rstk || !ria_mysql_num_rows($rstk) ){
								continue;
							}
							$stk = ria_mysql_fetch_array($rstk);
							prd_dps_stocks_update( $p['id'], 57, $stk['qte'], $stk['res'] + $p['qte'], $stk['com'], $stk['prepa'], $stk['mini'], $stk['maxi'] );
						}else{
							if( prd_products_is_nomenclature($p['id']) && !prd_products_get_is_sync($p['id']) ){
								$rch = ord_products_get( $order['id'], false, 0, '', null, false, -1, $p['id'] );
								if (!$rch) {
									continue;
								}
								while( $ch = ria_mysql_fetch_array($rch) ){
									prd_stocks_set_reserved( $ch['id'], $line_dps, $ch['qte'], $order['wst_id'] );
								}
							}else{
								prd_stocks_set_reserved( $p['id'], $line_dps, $p['qte'], $order['wst_id'] );
							}
						}
					}
				}
				break;
			}
			case _STATE_CANCEL_USER :
			case _STATE_CANCEL_MERCHAND :{

				if( $was_cart || $old == _STATE_DEVIS ){
					break;
				}

				stats_add_order_canceled( $order['total_ht']);
				gu_users_update_orders_canceled( $order['user'] );
				gu_users_update_orders_canceled_web( $order['user'] );

				// L'annulation d'une commande, entraîne la désactivation des cartes cadeaux achetées
				$rcod = pmt_codes_get( null, null, false, _PMT_TYPE_GIFTS, false, null, null, null, null, array(_FLD_PMT_ORD=>$order['id']) );
				if( $rcod && ria_mysql_num_rows($rcod) ){
					while( $cod = ria_mysql_fetch_array($rcod) ){
						error_log( 'Annulation de la carte cadeau '.$cod['id'].' - '.$cod['code'].'.' );
						if( $cod['used']>0 ){
							error_log( 'La carte cadeau "'.$cod['id'].' - '.$cod['code'].'" vient d\'être annulée, toutefois elle a déjà été utilisée' );
						}

						if( !pmt_codes_del($cod['id']) ){
							error_log( __FILE__.':'.__LINE__.' - Impossible de désactiver la carte cadeau : '.$cod['id'].' (commande : '.$order['id'].')' );
						}
					}
				}

				// annulation des réservations pour les commandes non synchronisées et pas encore préparée (3, 4, 5, 25)
				// le compte ne doit pas être un admin, un fournisseur ou un représentant (et cohérent sur ses marqueurs de synchro)

				//vérification si on doit faire la gestion des stock pour ce tenant
				if (!$tnt_resa) {
					break;
				}
				$is_sync = $order['piece'] != '';
				$manage_stock = ((isset($config['cancel_ord_set_reserved']) && $config['cancel_ord_set_reserved']) || !$is_sync) &&
				in_array($old, array(_STATE_PAY_WAIT_CONFIRM, _STATE_PAY_CONFIRM, _STATE_WAIT_PAY, _STATE_IN_PROCESS, _STATE_CLICK_N_COLLECT)) &&
				$tnt_resa;

				if (!$manage_stock) {
					break;
				}
				//Récupération de l'utilisateur pour vérifier le profile
				$rusr = gu_users_get( $order['user'] );
				if (!$rusr || !ria_mysql_num_rows($rusr)) {
					break;
				}
				$usr = ria_mysql_fetch_array($rusr);
				if (!$usr) {
					break;
				}
				//Vérification du profile
				// si le client n'est ni un admin, un représentant ou un fournisseur on gère les stocks
				// si le client est synchronisé ou n'a pas de référence on gère les stocks
				$user_manage_stock = !in_array($usr['prf_id'], array(PRF_ADMIN, PRF_SELLER, PRF_SUPPLIER)) && ( $usr['is_sync']==1 || $usr['ref']=='' );
				if (!$user_manage_stock) {
					break;
				}
				// Récupération du dépôt impacté
				$dps = prd_deposits_get_main();
				if( isset($config[ 'prd_deposits' ]) && $config[ 'prd_deposits' ]=='use-customer' ){
					if( prd_deposits_exists( $usr['dps_id'] ) ){
						$dps = $usr['dps_id'];
					}
				}
				//surcharge du dépôt par celui sur la commande
				if( isset($config['stock_used_dps_order']) && $config['stock_used_dps_order'] ){
					$dps_ord = ord_orders_get_deposit( $order['id'] );
					if( is_numeric($dps_ord) && $dps_ord ){
						$dps = $dps_ord;
					}
				}
				// récupération des produits pour vérifier les stocks
				$products = ord_products_get($order['id']);
				if (!$products || !ria_mysql_num_rows($products)) {
					break;
				}
				// pour chaque produit on fait la gestion du stock si nescéssaire.
				while( $p = ria_mysql_fetch_array($products) ){
					// la gestion du stock n'a lieu que si le produit est suivi en stock
					if( prd_products_is_follow_stock( $p['ref'] ) ){
						$altern = false;

						$line_dps = $dps;
						if( isset($config['stock_used_dps_order']) && $config['stock_used_dps_order'] && $p['line_dps_id'] > 0 ){
							$line_dps = $p['line_dps_id'];
						}
						if( $altern ){
							$rstk = prd_dps_stocks_get( $p['id'], 57 );
							if( !$rstk || !ria_mysql_num_rows($rstk) ){
								continue;
							}
							$stk = ria_mysql_fetch_array($rstk);
							prd_dps_stocks_update( $p['id'], 57, $stk['qte'], $stk['res'] - $p['qte'], $stk['com'], $stk['prepa'], $stk['mini'], $stk['maxi'] );
						}else{
							if( prd_products_is_nomenclature($p['id']) && !prd_products_get_is_sync($p['id']) ){
								$rch = ord_products_get( $order['id'], false, 0, '', null, false, -1, $p['id'] );
								if (!$rch) {
									continue;
								}
								while( $ch = ria_mysql_fetch_array($rch) ){
									prd_stocks_set_reserved( $ch['id'], $line_dps, $ch['qte'] * -1 );
								}
							}else{
								prd_stocks_set_reserved( $p['id'], $line_dps, $p['qte'] * -1 );
							}
						}
					}
				}
				break;
			}
		}

		// Si la commande passe à un statut validé et qu'elle était à un statut en attente de règlement ou règlement en attente de validation
		// Alors on génère les cartes cadeaux qu'elle peut contenir
		if( in_array($old, array(_STATE_WAIT_PAY, _STATE_PAY_WAIT_CONFIRM)) && in_array($status, ord_states_get_ord_valid( false, true )) ){
			// Appel de la fonction de génération de carte cadeau, celle-ci peut aussi être appelé sur les factures
			pmt_promotions_get_gift_card(CLS_ORDER,$order['id'] ,$order['user'], $order['wst_id']);
		}

	// enregistrement des points de fidélité seulement si le calcul se fait sur la commande
	// les status acceptés pour attribuer des points soit : 3 - en attente de règlement et 4 - en attente de traitement
	// les points de fidélité ne sont attribué qu'une seule fois (contrôler dans rwd_actions_apply)
	if( $config['rwd_reward_actived'] ){
		if( in_array($status, array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM)) ){
			$prf = gu_users_get_prf( $order['user'] );
			if( $prf ){
				$typcalc = rwd_rewards_get_type_calc( $prf );
				if( $typcalc=='order' ){
					rwd_actions_apply( 'RWA_ORDER', CLS_ORDER, $ord_id );
				}

				$rwd_sp = rwd_rewards_get_sponsor( 0, $config['wst_id'], $prf );
				if (is_array($rwd_sp) && count($rwd_sp)) {
					if (is_numeric($rwd_sp['points']) && $rwd_sp['points']) {
						rwd_actions_apply( 'RWA_SPONSOR', CLS_ORDER, $ord_id, array('prf'=>$prf, 'godson'=>$order['user']) );
					}else{
						$order_promotions = ord_orders_promotions_get($order['id']);
						if(!empty($order_promotions)) {
							foreach($order_promotions as $order_promotion){
								rwd_actions_apply( 'RWA_SPONSOR', CLS_ORDER, $ord_id, array('prf'=>$prf, 'cod'=>$order_promotion['pmt_id'], 'godson'=>$order['user']) );
							}

						}
					}
				}

			}
		}else{
			// annulation de commande
			if( in_array($status, ord_states_get_canceled( true )) ){
				rwd_actions_apply( 'RWC_CANCEL_ORDER', CLS_ORDER, $ord_id );
			}
		}
		// Débite les points de fidélités si un produit de la commande appartient au catalogue du programme de fidélité.
		rwd_actions_apply('RWA_MANAGE_GIFT', CLS_ORDER, $ord_id);
	}

	// S'il on est sur une commande de l'extranet Proloisirs, on met à jour les commandes sites rattachées à celle-ci
	if( $config['tnt_id'] == 4 && $order['wst_id'] == 8 ){
		$rord_website = ord_orders_get( 0, 0, 0, 0, null, false, false, false, false, false, false, '', false, array(872=>$order['id']), false, array(26, 27, 30, 58, 78) );
		if( $rord_website ){
			while( $ord_website = ria_mysql_fetch_assoc($rord_website) ){
				ord_orders_state_update( $ord_website['id'], $status, '', false );
			}
		}
	}

	// dans le cas de la sodip la synchronisation étant coupé on lance les notifications directements ici
	if( $res && ((isset($config['sync_global_gescom_type']) && $config['sync_global_gescom_type']==GESCOM_TYPE_SINERES)) && ($was_cart || $old==_STATE_DEVIS) && in_array($status,array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM))){

		if( !isset($config['web10_wsdl']) || $config['web10_wsdl'] == '' ){
			ord_alert_shop_owner($order['id']);
		}
	}

	if( in_array($status,array(_STATE_WAIT_PAY)) ){
		$RDV_data = fld_object_values_get($ord_id, _FLD_DLV_CHRONORDV, '', false, true);
		if( $RDV_data ){
			$order = ria_mysql_fetch_assoc( ord_orders_get_with_adresses(0, $ord_id) );
			$RDV = json_decode($RDV_data, true);
			require_once('chronopost/ChronopostRDV.inc.php');
			try{
				$ChronopostRDV = new ChronopostRDV();
				$ChronopostRDV->setOrderId($ord_id);

				$service = new CreneauWS();
				$request = new searchDeliverySlot(true);
				$dateBegin = new DateTime($order['date_livr_en']);
				$dateEnd = new DateTime($order['date_livr_en']);
				$dateEnd->modify('+22 hours');
				$request->setCallerTool('RDVWS')
					->setProductType($RDV['productType'])
					->setShipperZipCode($config['chronordv_shipper_zipcode'])
					->setRecipientAdress1($order['dlv_address1'])
					->setRecipientCity($order['dlv_city'])
					->setRecipientZipCode($order['dlv_postal_code'])
					->setShipperCountry('FR')
					->setRecipientCountry('FR')
					->setWeight(ord_orders_weight_get( $ord_id, 'kg'))
					->setDateBegin($dateBegin)
					->setDateEnd($dateEnd);

				$deliverySlotResponse =  $service->searchDeliverySlot($request)->getReturn();
				$ChronopostRDV->setDeliverySlotResponse($deliverySlotResponse);
				if( !$ChronopostRDV->checkIfSlotInSessionIsAvailable() ){
					error_log('Chronopost - ['.$config['tnt_id'].'] commande ['.$ord_id.'] Créneaux en session expiré');
				}

				$confirmDeliverySlot = $ChronopostRDV->confirmDeliverySlot($dateBegin, $RDV['productType']);

				$serviceResponse = $service->confirmDeliverySlot($confirmDeliverySlot)->getReturn();
				if( $serviceResponse->getCode() != 0 ){
					error_log('Chronopost - ['.$config['tnt_id'].'] commande ['.$ord_id.'] Erreur confirmation de créneaux');
				}
				$productService = $serviceResponse->getProductService();
				$RDV['ProductCode'] = '';
				$RDV['ServiceCode'] = '';
				if( is_object($productService) ){
					$RDV['ProductCode'] = $productService->getProductCode();
					$RDV['ServiceCode'] = $productService->getServiceCode();
				}
				$ChronopostRDV->saveSlotInSession( $RDV['slot'], $RDV['dlv_date'], $RDV['productType'], $RDV['ProductCode'], $RDV['ServiceCode']);
			}catch(Exception $e){
				error_log('Chronopost - ['.$config['tnt_id'].'] commande ['.$ord_id.'] '. $e->getMessage());
			}
		}
	}

	return $res;
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les factures rattachées à une commande donnée.
 *	@param int $ord_id Identifiant de la commande
 *	@param int $usr_id Optionnel, identifiant d'un compte client
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la facture
 *			- piece : numéro de pièce de la facture dans la gestion commerciale
 *			- date : date/heure de création de la facture dans la gestion commerciale
 */
function ord_ord_invoices_get( $ord_id, $usr_id=0 ){
	if (!is_numeric($ord_id) || $ord_id <= 0) {
		return false;
	}

	if (!is_numeric($usr_id) || $usr_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select distinct inv_id as id, inv_piece as piece, date_format(inv_date,"%d/%m/%Y à %H:%i") as date
		from riashop.ord_invoices
			join riashop.ord_inv_products on (prd_tnt_id = inv_tnt_id and prd_inv_id = inv_id)
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_masked = 0
			and prd_ord_id = '.$ord_id.'
	';

	if ($usr_id) {
		$sql .= ' and inv_usr_id = '.$usr_id;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction est chargé d'envoyer un mail de relance une commande pour laquelle le paiement par chèque n'a pas encore été reçu.
 *	@param $order Obligatoire, identifiant d'une commande ou résultat ria_mysql_fetch_assoc( ord_orders_get_with_adresses() )
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 */
function ord_orders_pay_cheque_notify( $order ){
	if( is_array($order) ){
		if( !ria_array_key_exists(array('id', 'user'), $order) ){
			return false;
		}
	}else{
		$rorder = ord_orders_get_with_adresses( 0, $order );
		if( !$rorder || !ria_mysql_num_rows($rorder) ){
			return false;
		}

		$order = ria_mysql_fetch_assoc( $rorder );
	}

	global $config;

	$rcfg = cfg_emails_get( 'ord-confirm', $config['wst_id'] );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_assoc( $rcfg );

	$owner = site_owner_get( $order['wst_id'] );
	if( !is_array($owner) || !sizeof($owner) ){
		return false;
	}

	// Récupère les informations sur le compte client
	$ruser = gu_users_get( $order['user'] );
	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		error_log("Aucune configuration propriétaire de renseigner \"ord_orders_pay_cheque_notify\" : ".$config['tnt_id']);
		return false;
	}

	$user = ria_mysql_fetch_assoc( $ruser );

	// Civilité du visiteur
	$title = '';
	if($user['title_id']) {
		$titles = gu_titles_get($user['title_id']);
		if($titles && ria_mysql_num_rows($titles)) {
			$title = ria_mysql_fetch_array($titles);
			$title = ' '.$title['name'];
		}
	}

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	$email->addTo( $user['email'] );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );

	$compl_url = '?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=cheque_pay';

	$can_send = true;
	switch( $config['tnt_id'] ){
		case 16: {
			$address  = htmlspecialchars( $owner['name'] );
			$address .= '<br />'.htmlspecialchars( $owner['address1'] );

			if( trim($owner['address2']) != '' ){
				$address .= '<br />'.htmlspecialchars( $owner['address2'] );
			}

			$address .= htmlspecialchars( $owner['zipcode'].' '.$owner['city'] );

			$email->setSubject('Animal & Co : En attente de règlement');

			$email->addHtml( $config['email_html_header'] );
			$email->addParagraph('Bonjour '.trim( $title.' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');
			$email->addParagraph('Vous avez récemment choisi de régler par chèque votre commande '.$order['id'].'. Nous sommes toutefois en attente du règlement d\'un montant de '.number_format( $order['total_ttc'], 2, ',', ' ' ).' € pour pouvoir commencer son traitement.');
			$email->addHtml('
				<p>Veuillez, s’il vous plait, adresser votre chèque à l\'ordre de "SARL animaleco.com", à l\'adresse suivante : <br />Animaleco.com<br />103 Avenue de Paris<br />79000 NIORT</p>

				<p>N\'oubliez pas de faire figurer au dos de votre chèque :</p>
				<ul>
					<li>Vos noms/prénoms/société</li>
					<li>Votre numéro de commande Internet : '.$order['id'].'</li>
				</ul>

				<p>Pour information, votre commande est encore valide pendant 7 jours avant d’être annulée.</p>
			');
			$email->addParagraph('Bien entendu, toute l\'équipe d\'Animaleco.com se tient à votre entière disposition pour toute question.');
			$email->addParagraph('À très bientôt sur <a href="http://www.animaleco.com/'.$compl_url.'">www.animaleco.com</a> !');
			$email->addParagraph('Cordialement,<br />L\'équipe Animaleco.com.');
			$email->addHtml( $config['email_html_footer'] );
			break;
		}
		default: {
			$can_send = false;
			error_log("Aucune configuration de renseigner \"ord_orders_pay_cheque_notify\" : ".$config['tnt_id']);
			break;
		}
	}

	if( $can_send ){
		return $email->send();
	}

	return true;
}
// \endcond

/**	Retourne la dénomination de la commande, à partir des éléments disponibles (référence, numéro de pièce sage, identifiant internet).
 *	@param string $ref Référence client de la commande
 *	@param string $piece Numéro de pièce sage
 *	@param int $id Identifiant internet
 *	@return string le nom de la commande
 */
function ord_orders_name( $ref, $piece, $id ){
	$ord_name = '';
	if( trim($ref) ){
		$ord_name = ucfirst(trim($ref));
		if( trim($piece) ){
			$ord_name .= ' ('.$piece.')';
		}
	}elseif( trim($piece) ){
		$ord_name = $piece;
	}else{
		$ord_name = str_pad($id,8,'0',STR_PAD_LEFT);
	}
	return $ord_name;
}

// \cond onlyria
/**	Retourne l'identifiant des commandes contenant des reliquats pour un client donné.
 *	@param int $usr Identifiant de l'utilisateur pour lequel on souhaite obtenir les produits retardés
 *	@return la liste des identifiants de commmandes contenant des reliquats
 */
function ord_orders_delayed_list( $usr ){

	$ar_orders = array( 0 );
	$r_orders = ord_products_get_delayed( $usr );
	while( $ord = ria_mysql_fetch_array($r_orders) ){
		$ar_orders[] = $ord['ord_id'];
	}

	return implode(',',$ar_orders);
}
// \endcond

// \cond onlyria
/**	Retourne l'identifiant des commandes contenant des reliquats pour un client donné.
 *	@param int $usr Identifiant de l'utilisateur pour lequel on souhaite obtenir les produits retardés
 *	@return la liste des identifiants de commmandes contenant des reliquats
 */
function ord_orders_have_delayed( $usr ){
	global $memcached;

	// Contrôle le paramètre $usr
	if( !is_numeric($usr) ){
		return false;
	}

	$have_delayed = $memcached->get( 'orders:delayed:list:'.$usr );
	if( $have_delayed!==false ){
		return trim($have_delayed)!='';
	}

	$orders = ord_orders_delayed_list( $usr );
	$memcached->set( 'orders:delayed:list:'.$usr, $orders );

	return trim($orders)!='';
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne une description textuelle du statut d'une ligne de commande.
 *	Elle vérifie la présence de préparations de livraison et de bons de livraison pour une ligne de commande.
 *	@param int $ord Identifiant de la commande
 *	@param int $prd Identifiant du produit
 *	@param $qte Quantité commandée
 *	@param $countOrd Facultatif, si différent de 0, permet de déterminer le nombre de lignes du BC pour la clé ord/prd/qte
 *	@param $line_id Facultatif, numéro de ligne dans la commande
 *	@param int $usr_id Facultatif, identifiant d'un compte client
 *	@param $return_array Optionnel, permet de retourner un ensemble d'information sur le statut d'une ligne de commande
 *	@return string Si $return_array est à false (par défaut), retourne juste le statut final de la ligne
 *	@return array Si $return_array est à true, retourne le détail :
 *							- pl_piece : numéro de pièce du bon de préparation
 *							- pl_date : date du bon de préparation
 *							- bl_piece : numéro de pièce du bon de livraison
 *							- bl_date : date du bon de livraison
 *							- bl_colis_url : url de suvi du colis
 *							- bl_colis : numéro de colis
 *							- inv_piece : numéro de pièce de la facture
 *							- inv_date : date de la facture
 */
function ord_product_state_desc( $ord, $prd, $qte, $countOrd=0, $line_id=false, $usr_id=0, $return_array=false ){
	global $config;

	// Vérifie les paramètres d'entrée
	if( !ord_orders_exists($ord) ){ return false; }
	if( !prd_products_exists($prd) ){ return false; }
	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !is_numeric($qte) ){ return false; }
	if( !is_numeric($countOrd) || $countOrd<0 ){ return false; }
	if( $line_id!==false && (!is_numeric($line_id) || $line_id<0) ){ return false; }
	if( !is_numeric($usr_id) || $usr_id<0 ){ return false; }

	// site web de la commande
	$wst = ord_orders_get_website( $ord );
	if( !$wst ){
		$wst = $config['wst_id'];
	}

	$ar_result = array();

	$state = '';

	// Vérifie la présence de pl(s)
	$pl = ria_mysql_query('
		select pl_piece as piece, date_format(pl_date,"%d/%m/%Y") as date, pl_date as date_en
		from riashop.ord_pl, riashop.ord_pl_products
		where pl_tnt_id='.$config['tnt_id'].' and prd_tnt_id=pl_tnt_id
			and pl_id=prd_pl_id
			and prd_ord_id='.$ord.'
			and prd_id='.$prd.'
			and prd_qte='.$qte.'
			and pl_masked=0
			'.( $line_id!==false ? ' and prd_line_id='.$line_id : '' ).'
	');
	if( ( ria_mysql_num_rows($pl) && $countOrd==0 ) || ( $countOrd>0 && ria_mysql_num_rows($pl)>=$countOrd ) ){
		$p = ria_mysql_fetch_array($pl);
		$state = 'En cours de préparation le '.$p['date'].' ('.$p['piece'].')';

		$ar_result = array_merge($ar_result, array(
			'pl_piece' => $p['piece'],
			'pl_date' => $p['date_en']
		));
	}


	// Vérifie la présence de bl(s)
	$bl = ria_mysql_query('
		select bl_piece as piece, date_format(bl_date,"%d/%m/%Y") as date, ifnull(stn_name, state_name) as "state_name",
			prd_colis as colis, bl_srv_id as srv_id, bl_date as date_en
		from riashop.ord_bl
		join riashop.ord_bl_products on bl_id=prd_bl_id and bl_tnt_id=prd_tnt_id
		join riashop.ord_states on bl_state_id=state_id
		left join riashop.ord_states_name on bl_tnt_id=stn_tnt_id and bl_state_id=stn_stt_id and stn_wst_id='.$wst.'
		where bl_tnt_id='.$config['tnt_id'].'
			and bl_masked = 0
			and prd_ord_id='.$ord.'
			and prd_id='.$prd.'
			and prd_qte='.$qte.'
			'.( $line_id!==false ? ' and prd_line_id='.$line_id : '' ).'
	');
	if( ( ria_mysql_num_rows($bl) && $countOrd==0 ) || ( $countOrd>0 && ria_mysql_num_rows($bl)>=$countOrd ) ){
		$b = ria_mysql_fetch_array($bl);
		if( $state!='' ){ $state .= ', '; }
		$state .= str_replace('ée','é', ($b['state_name']=='Facturée'?'Livré':$b['state_name']) ).' le '.$b['date'].' ('.$b['piece'].')';

		$ar_result = array_merge($ar_result, array(
			'bl_piece' => $b['piece'],
			'bl_date' => $b['date_en']
		));

		if( $b['colis'] ){
			// Charge le transporteur
			$url_colis = '';
			if( $b['srv_id'] ){
				$rsrv = dlv_services_get($b['srv_id']);
				if( ria_mysql_num_rows($rsrv) ){
					$srv = ria_mysql_fetch_array($rsrv);
					$url_colis = $srv['url-colis'];
				}
			}
			if( $url_colis ){
				$state .= ', <a href="'.$url_colis.$b['colis'].'" target="_blank">colis '.$b['colis'].'</a>';
			}else{
				$state .= ', colis '.htmlspecialchars($b['colis']);
			}
			$ar_result['bl_colis_url'] = trim($url_colis) != '' ? $url_colis.$b['colis'] : '';
			$ar_result['bl_colis'] = $b['colis'];
		}
	}

	// Vérifie la présence d'une facture
	$inv = ria_mysql_query('
		select inv_piece as piece, date_format(inv_date,"%d/%m/%Y") as date, inv_date as date_en
		from riashop.ord_invoices, riashop.ord_inv_products
		where inv_tnt_id='.$config['tnt_id'].' and prd_tnt_id=inv_tnt_id
			'.($usr_id ? ' and inv_usr_id = '.$usr_id : '').'
			and inv_id=prd_inv_id
			and inv_masked = 0
			and prd_ord_id='.$ord.'
			and prd_id='.$prd.'
			and prd_qte='.$qte.'
			'.( $line_id!==false ? ' and prd_line_id='.$line_id : '' ).'
	');
	if( ( ria_mysql_num_rows($inv) && $countOrd==0 ) || ( $countOrd>0 && ria_mysql_num_rows($inv)>=$countOrd ) ){
		$i = ria_mysql_fetch_array($inv);
		if( $state!='' ){ $state .= ', '; }
		$state .= 'Facturé le '.$i['date'].' ('.$i['piece'].')';

		$ar_result['inv_piece'] = $i['piece'];
		$ar_result['inv_date'] = $i['date_en'];
	}

	if( $state=='' ){
		//on recupere le profils du propriétaire de la commande, et si c'est un fournisseur on change de message
		$user = ria_mysql_fetch_array(ria_mysql_query('select usr_prf_id from riashop.ord_orders, riashop.gu_users where ord_tnt_id='.$config['tnt_id'].' and (usr_tnt_id=0 or usr_tnt_id=ord_tnt_id) and usr_id = ord_usr_id and ord_id='.$ord));
		if($user['usr_prf_id']!=PRF_SUPPLIER){
			$state = 'En attente de traitement';
		}
		if( !prd_products_is_available($prd) ){
			$date = prd_products_date_available($prd);
			if( $date!='' ){
				$state .= ', disponible au '.$date;
			}
		}
	}

	return $return_array ? $ar_result : $state;
}
// \endcond

/**	Calcul et retourne le poids total brut d'une commande (en Kg)
 *
 *	@param int $id Obligatoire, Identifiant de la commande pour laquelle on souhaite calculer son poids
 * 	@param string $unit Optionnel, Unité de mesure dans lequel le résultat sera retourné, par défaut retourné en kilogramme, les valeurs acceptées sont kg (kilogramme) et g (gramme)
 *	@param bool $net Optionnel, par défaut on retour le total du poids brut, mettre true pour retourné le total du poids net des produits de la commande
 *	@param array $ar_prd_ids Optionnel, permet de limiter le résultat selon certains produits
 *	@param int $zone Facultatif, Identifiant d'une zone
 *	@param bool $round Facultatif, Arrondir la valeur du poids ?
 *	@param array $ar_cat_ids Optionnel, permet de limiter le résultat selon certaines catégories de produits
 *	@return int le poids total brut de la commande (par défaut en Kg)
 *
 */
function ord_orders_weight_get( $id, $unit='kg', $net=false, $ar_prd_ids=array(), $zone='', $round = true, $ar_cat_ids=array() ){
	if( !is_numeric($id) || $id<=0 || !ord_orders_exists($id) ){
		return false;
	}

	global $config;

	$total_weight = 0;

	$products = ord_products_get($id);
	while( $prd = ria_mysql_fetch_array($products) ){
		if( is_array($ar_prd_ids) && count($ar_prd_ids) > 0 && !in_array($prd['id'], $ar_prd_ids) ){
			continue;
		}

		if( is_array($ar_cat_ids) && count($ar_cat_ids) > 0 ){
			$to_continue=true;
			$r_cly = prd_classify_get(false,$prd['id']);
			if ($r_cly && ria_mysql_num_rows($r_cly)){
				while($cly = ria_mysql_fetch_assoc($r_cly)){
					if (in_array($cly['cat'], $ar_cat_ids)){
						$to_continue=false;
						break;
					}
				}
			}
			if ($to_continue){
				continue;
			}
		}


		$prd['weight'] 		= is_numeric( $prd['weight'] ) && $prd['weight'] > 0 ? $prd['weight'] : 0;
		$prd['weight_net'] 	= is_numeric( $prd['weight_net'] ) && $prd['weight_net'] > 0 ? $prd['weight_net'] : 0;

		$total_weight += ( $net ? $prd['weight_net'] : $prd['weight'] ) * $prd['qte'];
	}

	// magoration du poids de la commande
	if( isset($config['cmd_pourcent_packing']) && is_numeric($config['cmd_pourcent_packing']) && $config['cmd_pourcent_packing'] ){
		$total_weight = $total_weight * (1 + $config['cmd_pourcent_packing']/100);
	}

	$excluded_products = 0;
	if (is_numeric($zone) && $zone > 0){
		$excluded_products = ord_orders_excluded_products_amount_get($net ? 'weight_net' : 'weight', $id, $zone, $total_weight);
	}

	$total_weight -= $excluded_products;

	switch($unit) {
		case 'kg' :
			return $round ? round($total_weight/1000) : $total_weight/1000;
			break;
		case 'g' :
			return $round ? round($total_weight) : $total_weight;
			break;
		default:
			return $round ? round($total_weight/1000) : $total_weight/1000;
			break;
	}
}

// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/** Détermine le montant de frais de port d'une commande
 *	Cette fonction est a généraliser, et non pas suivant les locataires
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param int $usr_id Facultatif, identifiant du client de la commande ( s'il n'est pas précisé et que la commande n'est pas rattaché à un client, l'exécution échouera )
 *	@param int $srv_id Facultatif, identifiant du service de livraison de la commande ( suivant les locataires, celui-ci est obligatoire ou non )
 *	@param bool $do_all_work Facultatif, si true (et suivant le locataire), la gestion des frais de port est traitée intégralement
 *	@param bool $simulate_zipcode Optionnel permet de substituer le code postal (FR - 5 chiffres) de l'adresse de livraison
 *	@param array $cat Facultatif, Tableau d'identifiant de catégorie permettant de limité le calcul du frais de port aux produits de certaine catégorie
 *	@param array $prd Facultatif, Tableau d'identifiant de produit permettant de limité le calcul du frais de port à certain produit
 *	@param $ref_free Facultatif, référence a utilisé dans le cas où les frais de port sont offert (seulement si $do_all_word et $config['dlv_active_port'] sont a true)
 *	@param $simulate_zone Optionnel permet de substituer la zone de livraison par l'identifiant donné
 *
 *	@return Le montant des frais de port en cas de succès ou False en cas d'échec
 */
function ord_orders_get_port_amount( $ord_id, $usr_id=0, $srv_id=0, $do_all_work=false, $simulate_zipcode=false, $cat=array(), $prd=array(), $ref_free='', $simulate_zone = null){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	if( $usr_id != 0 && !gu_users_exists($usr_id) ){
		return false;
	}

	if( $srv_id != 0 && !dlv_services_exists($srv_id) ){
		return false;
	}

	// Récupération des informations de la commande (avec l'adresse si le calcul des frais de port est laissé au moteur RiaShop)
	if (isset($config['dlv_active_port']) && $config['dlv_active_port']) {
		$rord = ord_orders_get_with_adresses( 0, $ord_id );
	}else{
		$rord = ord_orders_get( 0, $ord_id );
	}

	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}

	$ord = ria_mysql_fetch_array( $rord );

	$pmts = ord_orders_promotions_get($ord_id);
	if (is_array($pmts) && !empty($pmts)) {
		foreach($pmts as $pmt){
			$r_code = pmt_codes_get($pmt['pmt_id']);
			if (!$r_code || !ria_mysql_num_rows($r_code)) {
				continue;
			}
			$code = ria_mysql_fetch_assoc($r_code);
			if( $code['free_shipping'] ){
				$code_services = pmt_codes_get_services( $code['id'] );
				if( ( !$ord['srv_id'] && !$ord['str_id'] ) || ( $ord['str_id'] && in_array(-1, $code_services) ) || in_array($ord['srv_id'], $code_services) ){
					return 0;
				}
			}
		}
	}

	$r_prd_promo = ord_products_promotions_get($ord_id);
	if( $r_prd_promo && ria_mysql_num_rows($r_prd_promo) ){
		while( $prd_promo = ria_mysql_fetch_assoc($r_prd_promo) ){
			$r_code = pmt_codes_get($prd_promo['cod_id']);
			if (!$r_code || !ria_mysql_num_rows($r_code)) {
				continue;
			}
			$code = ria_mysql_fetch_assoc($r_code);
			if( $code['free_shipping'] ){
				$code_services = pmt_codes_get_services( $code['id'] );
				if( is_numeric($srv_id) && $srv_id != $ord['srv_id'] ){
					if( in_array($srv_id, $code_services) ){
						return 0;
					}
				}elseif( ( !$ord['srv_id'] && !$ord['str_id'] ) || ( $ord['str_id'] && in_array(-1, $code_services) ) || in_array($ord['srv_id'], $code_services) ){
					return 0;
				}
			}
		}
	}

	if (isset($config['dlv_active_port']) && $config['dlv_active_port']) {

		// substitue le paramètre service de livraison
		if( !$srv_id ){
			$srv_id = $ord['srv_id'];
		}
		//initialisation de la variable port_id
		$port_id = 0;

		// Récupération de l'identifiant RiaShop du produit frais de port par défaut
		$port_id = isset($config['default_port_id']) ? $config['default_port_id'] : 0;

		// Si un produit frais de port est renseigné dans le service de livraison, on récupère son identifiant RiaShop
		if( is_numeric($srv_id) && $srv_id > 0 ){
			$r_service = dlv_services_get($srv_id);
			if( $r_service && ria_mysql_num_rows($r_service) ){
				$service = ria_mysql_fetch_assoc($r_service);

				if( trim($service['prd_ref']) ){
					// Récupère l'identifiant du produit frais de port associé au service de livraison
					$service_port_id = prd_products_get_id($service['prd_ref']);

					// Si l'identifiant a bien été récupéré, c'est sur ce produit que l'on va affecter les frais de livraison
					$port_id = is_numeric($service_port_id) && $service_port_id > 0 ? $service_port_id : $port_id;
				}
			}
		}
		// Si il y a aucune configuration du port_id on return false
		if (!$port_id) {
			return false;
		}

		//chargement de l'adresse de livraison
		$zone_id = $simulate_zone;
		$zone_ids = array();
		if (!is_null($simulate_zone)) {
			$zone_ids[] = $simulate_zone;
		}

		if( is_null($zone_id) ){
			if( is_numeric($simulate_zipcode) && $simulate_zipcode > 0 ){

				$_simulate_zipcode = trim($simulate_zipcode);
				$simulate_zipcode = substr($_simulate_zipcode, 0, 2);

				if( in_array($simulate_zipcode, [97, 98]) ){
					$simulate_zipcode = substr($_simulate_zipcode, 0, 3);
				}
				$r_zone = sys_zones_get(0, $simulate_zipcode, '', false, 0, '', _ZONE_DPT_FRANCE);

				if( $r_zone && ria_mysql_num_rows($r_zone) ){
					// departement trouvé
					while( $zone = ria_mysql_fetch_assoc($r_zone) ){
						$zone_id = $zone['id'];
						$zone_ids[] = $zone_id;
					}
				}
			}else{
				$zip = 0;
				$zip_city = 0;
				$city = '';
				$country = '';

				if( is_numeric($ord['str_id']) && $ord['str_id'] ){ //charge l'adresse du magasin de livraison si renseigné
					$r_store = dlv_stores_get($ord['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null);

					if( $r_store && ria_mysql_num_rows($r_store) ){
						$store = ria_mysql_fetch_assoc( $r_store );
						$zip = mb_substr(str_pad($store['zipcode'], 5, '0', STR_PAD_LEFT), 0, 2, 'UTF-8');
						$zip_city = $store['zipcode'];
						$city = $store['city'];
						$country = $store['country'];
					}
				}elseif( $ord['rly_id'] ){ //charge l'adresse relais de livraison si renseigné
					$r_rly = dlv_relays_get( $ord['rly_id']);

					if( $r_rly && ria_mysql_num_rows($r_rly) ){
						$rly = ria_mysql_fetch_assoc( $r_rly );
						$zip = mb_substr(str_pad($rly['zipcode'], 5, '0', STR_PAD_LEFT), 0, 2, 'UTF-8');
						$zip_city = $rly['zipcode'];
						$city = $rly['city'];
						$country = $rly['country'];
					}
				}else{
					if( trim($ord['dlv_postal_code']) != '' ){
							$zip = mb_substr(str_pad($ord['dlv_postal_code'], 5, '0', STR_PAD_LEFT), 0, 2, 'UTF-8');
						$zip_city = $ord['dlv_postal_code'];
						$city = $ord['dlv_city'];
						$country = $ord['dlv_country'];
					}else{
							$zip = mb_substr(str_pad($ord['inv_postal_code'], 5, '0', STR_PAD_LEFT), 0, 2, 'UTF-8');

						$zip_city = $ord['inv_postal_code'];
						$city = $ord['inv_city'];
						$country = $ord['inv_country'];
					}

				}

				if( $zip == 0 ){
					return false;
				}

				$zone_id = 0;

				// Récupération du pays si récupéré via l'adresse du magasin, relais, de facturation du client ou de livraison du client
				$r_zone = sys_zones_get(0, '', $country, false, 0, '', _ZONE_PAYS, array(), -1, -1, false);
				if( $r_zone && ria_mysql_num_rows($r_zone) ){
					$zone = ria_mysql_fetch_assoc($r_zone);
					$zone_id = $zone['id'];
				}

				// Traitement différent si pays récupéré ou non
				if( $zone_id ){
					$r_region = sys_zones_get(0, '', '', false, $zone_id);
					if( $r_region && ria_mysql_num_rows($r_region) ){
						while( $region = ria_mysql_fetch_assoc($r_region) ){
							$r_zone = sys_zones_get(0, $zip, '', false, $region['id'], '', _ZONE_DPT_FRANCE);
							if( $r_zone && ria_mysql_num_rows($r_zone) ){
								// departement trouvé
								$zone = ria_mysql_fetch_assoc($r_zone);
								$zone_id = $zone['id'];
								$r_zone_zip = sys_zones_get(0, $zip_city, '', false, $zone_id, '', _ZONE_ZIPCODES);
								if( $r_zone_zip && ria_mysql_num_rows($r_zone_zip) ){
									// code postal trouvé
									$zone_zip = ria_mysql_fetch_assoc($r_zone_zip);
									$zone_id = $zone_zip['id'];
									$r_zone_city = sys_zones_get(0, '', $city, true, $zone_zip['id'], '', _ZONE_INSEE, array(), -1, -1, false);
									if( $r_zone_city && ria_mysql_num_rows($r_zone_city) ){
										// ville INSEE trouvée
										$zone_city = ria_mysql_fetch_assoc($r_zone_city);
										$zone_id = $zone_city['id'];
									}
								}
								$zone_ids[] = $zone_id;
							}
						}
					}

					// On renseigne l'identifiant de la zone du pays si aucune zone (département - code postal - ville) trouvée en amont
					if( !count($zone_ids) ){
						$zone_ids[] = $zone_id;
					}
				}else{
					$r_zone = sys_zones_get(0, $zip, '', false, $zone_id, '', _ZONE_DPT_FRANCE);
					if( $r_zone && ria_mysql_num_rows($r_zone) ){
						// departement trouvé
						while( $zone = ria_mysql_fetch_assoc($r_zone) ){
							$zone_id = $zone['id'];
							$r_zone_zip = sys_zones_get(0, $zip_city, '', false, $zone_id, '', _ZONE_ZIPCODES);
							if( $r_zone_zip && ria_mysql_num_rows($r_zone_zip) ){
								// code postal trouvé
								$zone_zip = ria_mysql_fetch_assoc($r_zone_zip);
								$zone_id = $zone_zip['id'];
								$r_zone_city = sys_zones_get(0, '', $city, true, $zone_zip['id'], '', _ZONE_INSEE, array(), -1, -1, false);
								if( $r_zone_city && ria_mysql_num_rows($r_zone_city) ){
									// ville INSEE trouvée
									$zone_city = ria_mysql_fetch_assoc($r_zone_city);
									$zone_id = $zone_city['id'];
								}
							}
							$zone_ids[] = $zone_id;
						}
					}
				}
			}
		}

		if( !$zone_id || !sizeof($zone_ids) ){
			return false;
		}

		// Récupère toutes les zones parent de la zone de livraison sélectionnée, de la plus précise à la moins précise
		$final_port_val = false;
		$port_val = false;
		$ar_zone_port_vals = array();
		$round = !isset($config['round_port_ref_value']) || $config['round_port_ref_value'];
		$find=false;
		foreach ($zone_ids as $key => $zone_id) {
			$zone = array();
			$r_zone = sys_zones_parents_get($zone_id);
			if ($r_zone && ria_mysql_num_rows($r_zone)) {
				while($zo = ria_mysql_fetch_assoc($r_zone)){
					$zone[]=$zo['id'];
				}
			}
			$zone[] = $zone_id;
			$zone = array_reverse($zone);
			$r_pack = dlv_package_prices_get( null, null, null, null, null, null, null, array('value_min'=>'desc'));
			if( $r_pack && ria_mysql_num_rows($r_pack) ){
				foreach($zone as $k => $v){
					while($pack = ria_mysql_fetch_assoc($r_pack)){//pour chaque tarification répondant au critère
						$r_z = dlv_package_price_zones_get( $pack['id'], $v);

						if (!$r_z || !ria_mysql_num_rows($r_z)) {
							continue;
						}

						while ($z_data = ria_mysql_fetch_assoc($r_z)) {
							$z = $z_data['zone_id'];

							$r_zone = dlv_zones_get($z, true);
							$zo = ria_mysql_fetch_assoc($r_zone);

							if($srv_id){
								if(ria_mysql_num_rows(dlv_services_get( $srv_id, false, $zo['id'] )) < 1){//vérifie si le service de la commande existe pour la zone
									continue;
								}
							}

							switch($zo['type']){
								case 'qte':
									$value = ord_products_get_all_qte( $ord_id, false, $cat, $prd, $zo['id'] );
									break;
								case 'weight':
									$value = ord_orders_weight_get( $ord_id , 'kg', false, $prd, $zo['id'], $round, $cat);
									if( $value == 0 ){
										$value = 0.001;
									}
									break;
								case 'weight_net':
									$value = ord_orders_weight_get( $ord_id ,'kg', true, $prd, $zo['id'], $round, $cat);
									if( $value == 0 ){
										$value = 0.001;
									}
									break;
								case 'HT':
									$value = ord_orders_get_total_without_port( $ord_id, false, false, $zo['id'], $prd, $cat );
									break;
								case 'TTC':
									$value = ord_orders_get_total_without_port( $ord_id, true, false, $zo['id'], $prd, $cat );
									break;
								default:
									$value = -1;
									break;
							}

							if($pack['value_min'] > $value || $value <= 0){
								continue;
							}

							$port_val = $pack['price_ht'];

							if( $pack['is_prorata'] && $pack['slice'] && $pack['sl_price']){ //calcul de répartition par tranches
								if(($value - $pack['slice']) > 0 ){
									if(!$pack['is_cumuled']){
										$port_val = 0;
									}else{
										$value -= $pack['value_min'];
									}
									while( ($value - $pack['slice']) > 0 ){
										$value -= $pack['slice'];
										$port_val += $pack['sl_price'];
									}
								}
							}

							if($pack['is_prorata'] && !$pack['slice'] ){ //calcul au prorata proportionnel
								$port_val = ( $pack['price_ht'] * $value ) / $pack['value_min'];
							}

							if (!array_key_exists($zo['id'], $ar_zone_port_vals)){
								$ar_zone_port_vals[$zo['id']] = array(
									'port_val' => parseFloat($port_val),
									'qte' => $value
								);
							}

							$find=true;
						}
					}

					ria_mysql_data_seek($r_pack, 0);
				}
			}
		}

		if (sizeof($ar_zone_port_vals)){
			$rule_multi = isset($config['multiple_dlv_rule']) && in_array($config['multiple_dlv_rule'], array('min', 'max', 'sum')) ? $config['multiple_dlv_rule'] : 'max';

			switch($rule_multi){
			case 'min' :
				foreach($ar_zone_port_vals as $zone_id => $array_info){
					if($final_port_val !== false) {
						if ($final_port_val > $array_info['port_val']){
							$final_port_val = $array_info['port_val'];
						}
					} else {
						$final_port_val = $array_info['port_val'];
					}
				}
				break;
			case 'max' :
				foreach($ar_zone_port_vals as $zone_id => $array_info){
					if($final_port_val !== false) {
						if ($final_port_val < $array_info['port_val']){
							$final_port_val = $array_info['port_val'];
						}
					} else {
						$final_port_val = $array_info['port_val'];
					}
				}
				break;
			case 'sum' :
				$final_port_val = 0;
				foreach($ar_zone_port_vals as $zone_id => $array_info){
					$final_port_val += $array_info['port_val'];
				}
				break;
			}
		}

		if(!$find){
			return false;
		}

		if ($do_all_work) {
			if( !$final_port_val ){ $final_port_val = 0; }

			$port_ref = '';

			if ($final_port_val == 0 && trim($ref_free) != '') {
				ord_products_del($ord_id, $port_id);

				$port_ref = $ref_free;
				$port_id = 0;
			}

			$rprd_info = prd_products_get_simple( $port_id, $port_ref  );
			if( !$rprd_info || !ria_mysql_num_rows($rprd_info) ){
				return false;
			}
			$prd_info = ria_mysql_fetch_assoc( $rprd_info );

			ord_products_del( $ord_id, $prd_info['id'] );
			if( !ord_products_add_free( $ord_id, $prd_info['ref'], $prd_info['name'], $final_port_val ) ){
				return false;
			}
		}

		return $final_port_val;
	}


	if( $config['tnt_id']==22 && $config['wst_id'] == 34 ){ // Tout pour les Chefs
		if( !$srv_id ){
			return false;
		}

		// récupère le poids de la commande
		$weight = ord_orders_weight_get( $ord_id, 'g' );
		$weight = $weight / 1000;
		$port_val = 0;
		$rpackage = dlv_package_prices_get( null, $srv_id );
		if( $rpackage && ria_mysql_num_rows($rpackage) ){
			while( $package = ria_mysql_fetch_array($rpackage) ){
				if( $weight >= $package['value_min'] ){
					$port_val = $package['price_ht'];
					if ($srv_id != 214) {
						$port_val = $port_val + 2;
					}
				}
			}
		}

		return $port_val;
	}

	if( $config['tnt_id'] != 27 && $ord['srv_id'] > 0 && $srv_id > 0 && $srv_id != $ord['srv_id'] ){
		error_log(__FILE__.':'.__LINE__.' - Le paramètre srv_id est spécifié alors que la commande a déjà un service de livraison.');
	}

	// substitue le paramètre service de livraison
	if( !$srv_id && $ord['srv_id'] ){
		$srv_id = $ord['srv_id'];
	}

	if( $simulate_zipcode!==false ){
		$simulate_zipcode = trim($simulate_zipcode);
		$simulate_zipcode = substr( $simulate_zipcode, 0, 2 );
		if( !is_numeric($simulate_zipcode) ){
			$simulate_zipcode = false;
		}
	}

	$port_val = false;

	switch( $config['tnt_id'] ){
		case 4 : { // Proloisirs

			if( $ord['user']==0 && $usr_id==0 ){
				return false;
			}
			if( $ord['user']!=0 && $usr_id!=0 && $usr_id!=$ord['user'] ){
				return false;
			}

			// substitue le paramètre client
			if( $ord['user']!=0 ){
				$usr_id = $ord['user'];
			}

			// Détermine une livraison directe chez le particulier
			$is_ldd = $config['tnt_id']==4 && fld_object_values_get( $ord_id,496 )=='Oui';

			// Détermine un dépassement de franco de port
			$portf = ord_orders_portf_get();
			if( $ord['total_ht'] >= $portf ){

				$port_val = 0;

			}elseif( $is_ldd ){
				// cumule les prix en supplément pour la LDD
				if( $rprd = ord_products_get( $ord_id ) ){
					while( $prd = ria_mysql_fetch_array($rprd) ){
						if( $rprice = prc_prices_get( 0, NEW_PRICE, false, false, false, $prd['id'], false, false, false, null, null, 1, 1, false, null, array( 'fld'=>_FLD_USR_PRC, 'symbol'=>'=', 'value'=>496 ) ) ){

							$real_price = null;
							while( $price = ria_mysql_fetch_array($rprice) ){

								$rcnd = prc_price_conditions_get( $price['id'] );
								if( $rcnd!==false && ria_mysql_num_rows($rcnd)==1 ){
									$real_price = $price;
								}

							}

							if( $real_price!==null ){
								if( $real_price['price_ht']>$prd['price_ht'] ){
									$port_val += ( $real_price['price_ht'] * $prd['qte'] ) - ( $prd['total_ht'] );
								}else{
									$port_val += ( $real_price['price_ht'] * $prd['qte'] );
								}
							}
						}
					}
				}

			}else{

				$zipcode = null;
				$raddress = null;

				// récupère l'adresse où le client souhaite être livré
				if( $ord['adr_delivery'] ){
					$raddress = gu_adresses_get( $usr_id, $ord['adr_delivery'] );
				}else{
					if( $ruser = gu_users_get( $usr_id ) ){
						if( $user = ria_mysql_fetch_array($ruser) ){
							if( $user['adr_invoices'] ){
								$raddress = gu_adresses_get( $usr_id, $user['adr_invoices'] );
							}
						}
					}
				}

				// détermine le code postal de cette adresse
				if( $raddress!==null && $raddress!==false ){
					if( $address = ria_mysql_fetch_array($raddress) ){
						if( trim($address['zipcode']) ){
							$zipcode = $address['zipcode'];
						}
					}
				}

				if( $zipcode!==null ){

					// détermine le poids total de la commande
					if( $weight = ord_orders_weight_get( $ord_id ) ){

						// par la parenté de zone, on connait la tarification rattachée au code postal, via le département
						$sql = '
							select
								dpp_price_ht as price, dpp_is_prorata as prorata, dpp_prorata_slice as slice, dpp_prorata_price as "sl_price", dpp_value_min as "value_min"
							from
								riashop.dlv_package_prices join
								riashop.dlv_package_price_zones on ( ppz_tnt_id=dpp_tnt_id and ppz_dpp_id=dpp_id ) join
								riashop.sys_zones as z on ( ppz_dzn_id=z.dzn_id ) join
								riashop.sys_zones as c on ( z.dzn_id=c.dzn_parent_id )
							where
								dpp_tnt_id='.$config['tnt_id'].' and
								dpp_value_min<='.$weight.' and
								z.dzn_type_id='._ZONE_DPT_FRANCE.' and
								c.dzn_type_id='._ZONE_ZIPCODES.' and
								c.dzn_code=\''.addslashes( $zipcode ).'\'
							order by
								dpp_value_min asc
							limit
								0, 1
						';

						// récupère la ligne de tarification
						if( $result = ria_mysql_query( $sql ) ){
							if( $p = ria_mysql_fetch_array($result) ){
								$port_val = $p['price']; // tarif minimal
								if( $p['prorata'] ){
									if( $p['slice'] && $p['sl_price'] ){
										// tarif au prorata par palier
										$weight -= $p['value_min'];
										while( $weight >= $p['slice'] ){
											$port_val += $p['sl_price'];
											$weight -= $p['slice'];
										}
									}else{ // tarif au prorata proportionnel
										$port_val = ( $p['price'] * $weight ) / $p['value_min'];
									}
								}
							}
						}

					}

				}

			}
			break;
		}
		case 45 : {// fch
			/** Modification des FDP + ajout des frais de gestion
			 * @see https://riastudio.atlassian.net/browse/FCH-319
			 *
			 * Cas 1 : Frais de port pour commande HT entre 0€ et 150€ -> 45€
			 * Cas 2 : Franco pour commande HT >= 150€
			 * Tous les cas : Frais de gestion -> 3€
			 *
			 * /!\ La Corse peut bénéficier du franco
			 */
			$port_val = 45;

			$amount = ord_orders_get_total_without_port( $ord_id );
			if( $amount >= 150 ){
				$port_val = 0;
			}

			$exo = fld_object_values_get($ord['user'], $config['fld_usr_exo_port']);
			if( $exo == 'Oui' ){
				$port_val = 0;
			}

			// Applique les FDP et Frais de gestion
			if( $do_all_work ){
				ord_products_del( $ord_id, $config['default_port_id'] );
				ord_products_del( $ord_id, $config['prd_management_costs'] );

				// Ajoute les FDP si le port est supérieur à 0€
				if( $port_val > 0 ){
					$rprd_s_costs = prd_products_get_simple( $config['default_port_id'] );
					if( ria_mysql_num_rows($rprd_s_costs) ){
						$prd_s_costs = ria_mysql_fetch_assoc( $rprd_s_costs );

						if( !ord_products_add_free( $ord_id, $prd_s_costs['ref'], $prd_s_costs['name'], $port_val ) ){
							return false;
						}
					}
				}

				// Ajout les frais de gestion
				$rprd_m_costs = prd_products_get_simple( $config['prd_management_costs'] );
				if( ria_mysql_num_rows($rprd_m_costs) ){
					$prd_m_costs = ria_mysql_fetch_assoc( $rprd_m_costs );

					if( !ord_products_add_free( $ord_id, $prd_m_costs['ref'], $prd_m_costs['name'], 3 ) ){
						return false;
					}
				}
			}
			break;
		}
		case 21 :{ // Sodipec

			$ord_weight_sum = ord_orders_weight_get( $ord_id );

			if( $ord_weight_sum===false ){
				return false;
			}

			require_once('sys.zones.inc.php');
			require_once('delivery.price.inc.php');

			$zone_id = null;

			// chargement de l'adresse de livraison
			$ord_address = ria_mysql_fetch_array(ord_orders_get_with_adresses( 0, $ord_id ));

			$rzone = false;
			$zip = mb_substr(str_pad( $ord_address['dlv_postal_code'], 5, '0', STR_PAD_LEFT ), 0, 2, 'UTF-8');
			if( $simulate_zipcode!==false ){
				$zip = $simulate_zipcode;
			}
			$rzone = sys_zones_get( 0, $zip, '', false, 0, '', _ZONE_DPT_FRANCE );
			if( $rzone && ria_mysql_num_rows($rzone) ){
				// Code postal trouvé
				$zone = ria_mysql_fetch_array($rzone);
				$zone_id = $zone['id'];
			}

			$ex_zone_id = $zone_id;

			if( $simulate_zipcode===false ){
				$rzone_zip = sys_zones_get( 0, $ord_address['dlv_postal_code'], '', false, $zone_id, '', _ZONE_ZIPCODES );
				if( $rzone_zip && ria_mysql_num_rows($rzone_zip) ){
					// code postal trouvé
					$zone_zip = ria_mysql_fetch_array($rzone_zip);
					$rzone_city = sys_zones_get( 0, '', $ord_address['dlv_city'], true, $zone_zip['id'], '', _ZONE_INSEE, array(), -1, -1, false );
					if( $rzone_city && ria_mysql_num_rows($rzone_city) ){
						// ville INSEE trouvée
						$zone_city = ria_mysql_fetch_array($rzone_city);
						$zone_id = $zone_city['id'];
					}
				}
			}

			if( !$zone_id ){
				return false;
			}

			$pass = false;

			// Chargement des colis
			if( $rpack = dlv_package_prices_get( null, $srv_id, null, null, $ord_weight_sum ) ){
				while( $pack = ria_mysql_fetch_array($rpack) ){
					if( dlv_package_price_zones_exists( $pack['id'], $zone_id ) ){ // Zone du colis

						$port_val = $pack['price_ht'];
						if( $pack['is_prorata'] ){ // calcul de répartition par tranches
							$ord_weight_sum -= $pack['slice'];
							// On déduit deux fois $pack['slice'], car pour un poids de 105kg et une tranche à 100kg, le prix est celui de 100kg, pas de 200kg
							while( ($ord_weight_sum - $pack['slice']) > 0 ){
								$ord_weight_sum -= $pack['slice'];
								$port_val += $pack['sl_price'];
							}
						}

						// le premier est le bon, car dlv_package_prices_get() trie par quantité décroissante, donc on est sur la plus proche du poids réeel
						$pass = true;
						break;
					}
				}
			}

			if( !$pass && $ex_zone_id ){
				// Chargement des colis
				if( $rpack = dlv_package_prices_get( null, $srv_id, null, null, $ord_weight_sum ) ){
					while( $pack = ria_mysql_fetch_array($rpack) ){
						if( dlv_package_price_zones_exists( $pack['id'], $ex_zone_id ) ){ // Zone du colis

							$port_val = $pack['price_ht'];
							if( $pack['is_prorata'] ){ // calcul de répartition par tranches
								$ord_weight_sum -= $pack['slice'];
								// On déduit deux fois $pack['slice'], car pour un poids de 105kg et une tranche à 100kg, le prix est celui de 100kg, pas de 200kg
								while( ($ord_weight_sum - $pack['slice']) > 0 ){
									$ord_weight_sum -= $pack['slice'];
									$port_val += $pack['sl_price'];
								}
							}

							// le premier est le bon, car dlv_package_prices_get() trie par quantité décroissante, donc on est sur la plus proche du poids réeel
							$pass = true;
							break;
						}
					}
				}
			}

			// test si la zone est exclu pour le franco
			$rcurrent_zone = sys_zones_get( $zone_id );
			if( $rcurrent_zone && ria_mysql_num_rows($rcurrent_zone) ){
				$current_zone = ria_mysql_fetch_array($rcurrent_zone);
				if( !in_array( mb_substr( $current_zone['code'], 0, 2, 'UTF-8'), $config['franco_excluded']) ){

					// récupère le franco
					$franco = 0;
					$dlv = dlv_services_get( $config['default_srv_id'] );
					if( $dlv && ria_mysql_num_rows( $dlv ) ){
						$dlv = ria_mysql_fetch_array( $dlv );
						$franco = $dlv['dealer-free-ht'];
					}

					if( $port_val && ord_orders_get_total_without_port( $ord_id ) > $franco  ){
						$port_val = 0;
					}

				}
			}

			// si spécifié, crée la ligne de frais de port adéquat
			if( $do_all_work ){
				$rprd_info = prd_products_get_simple( $config['default_port_id']  );
				if( !$rprd_info || !ria_mysql_num_rows($rprd_info) ){
					return false;
				}
				$prd_info = ria_mysql_fetch_array( $rprd_info );

				if( !$port_val ){
					$port_val = 0;
				}

				ord_products_del( $ord_id, $prd_info['id'] );
				if( !ord_products_add_free( $ord_id, $prd_info['ref'], $prd_info['name'], $port_val ) ){
					return false;
				}
			}
			break;
		}
		case 23 :{ //abripiscine

			// chargement de l'adresse de livraison
			$ord_address = ria_mysql_fetch_array(ord_orders_get_with_adresses( 0, $ord_id ));

			// on parcours le panier et on récupère pour chaque produit le prix de la colonne à appliquer
			$rprd = ord_products_get( $ord_id );
			if( !$rprd || !ria_mysql_num_rows($rprd) ){
				return false;
			}

			$port_val = 0;
			while( $prd = ria_mysql_fetch_array($rprd) ){
				$col = fld_object_values_get( array($_SESSION['ord_id'], $prd['id']), $config['fld_prd_port_col'] );
				if( trim($col)!='' ){
					if( !isset($config['code_delivery'][strtoupper($col)]) ){
						error_log('Frais de port indisponible - KitAbripiscine');
					}else{
						$port_val += $config['code_delivery'][strtoupper($col)];

					}
				}
			}

			$rservice = dlv_services_get($srv_id);
			if( !$rservice || !ria_mysql_num_rows( $rservice ) ){
				return false;
			}
			$service = ria_mysql_fetch_array($rservice);

			if( $port_val == 0 ){
				$port_val = $service['price-ht'];
			}

			// test si la commande à un code promo et s'il est pas en free shipping
			$order_promotions = ord_orders_promotions_get($ord_address['id']);
			if( !empty($order_promotions) ){
				foreach($order_promotions as $order_promo){
					$rpmt = pmt_codes_get( $order_promo['pmt_id'] );
					if( $rpmt && ria_mysql_num_rows($rpmt) ){
						$pmt = ria_mysql_fetch_array( $rpmt );
						if( $pmt['free_shipping'] ){
							$port_val = 0;
							break;
						}
					}
				}
			}

			// si spécifié, crée la ligne de frais de port adéquat
			if( $do_all_work ){
				$rprd_info = prd_products_get_simple( $config['port_id']  );
				if( !$rprd_info || !ria_mysql_num_rows($rprd_info) ){
					return false;
				}
				$prd_info = ria_mysql_fetch_array( $rprd_info );

				ord_products_del( $ord_id, $prd_info['id'] );
				if( !ord_products_add_free( $ord_id, $prd_info['ref'], $prd_info['name'], $port_val ) ){
					return false;
				}
			}

			break;
		}
		case 27 :{ // poubelle pro et mobilier de ville
			$srv = ria_mysql_fetch_array(dlv_services_get($srv_id));

			if( isset($config['socolissimo_srv']) && $srv_id == $config['socolissimo_srv'] ){

				// récupère le poids de la commande
				$weight = ord_orders_weight_get( $ord_id, 'g' );
				$weight = $weight / 1000;

				$rpackage = dlv_package_prices_get( null, $srv_id );
				if( $rpackage && ria_mysql_num_rows($rpackage) ){
					while( $package = ria_mysql_fetch_array($rpackage) ){
						if( $weight < $package['value_min'] ){
							$port_val = $package['price_ht'];
							break;
						}
					}
				}

			}
			else{
				$ord_weight_sum = ord_orders_weight_get( $ord_id );

				if( $ord_weight_sum===false ){
					return false;
				}

				require_once('sys.zones.inc.php');
				require_once('delivery.price.inc.php');

				$zone_id = null;

				// chargement de l'adresse de livraison
				$ord_address = ria_mysql_fetch_array(ord_orders_get_with_adresses( 0, $ord_id ));

				$rzone = false;
				$zip = mb_substr(str_pad( $ord_address['dlv_postal_code'], 5, '0', STR_PAD_LEFT ), 0, 2, 'UTF-8');
				if( $simulate_zipcode!==false ){
					$zip = $simulate_zipcode;
				}
				$rzone = sys_zones_get( 0, $zip, '', false, 0, '', _ZONE_DPT_FRANCE );
				if( $rzone && ria_mysql_num_rows($rzone) ){
					// Code postal trouvé
					$zone = ria_mysql_fetch_array($rzone);
					$zone_id = $zone['id'];
				}

				$ex_zone_id = $zone_id;

				if( $simulate_zipcode===false ){
					$rzone_zip = sys_zones_get( 0, $ord_address['dlv_postal_code'], '', false, $zone_id, '', _ZONE_ZIPCODES );
					if( $rzone_zip && ria_mysql_num_rows($rzone_zip) ){
						// code postal trouvé
						$zone_zip = ria_mysql_fetch_array($rzone_zip);
						$rzone_city = sys_zones_get( 0, '', $ord_address['dlv_city'], true, $zone_zip['id'], '', _ZONE_INSEE, array(), -1, -1, false );
						if( $rzone_city && ria_mysql_num_rows($rzone_city) ){
							// ville INSEE trouvée
							$zone_city = ria_mysql_fetch_array($rzone_city);
							$zone_id = $zone_city['id'];
						}
					}
				}

				if( !$zone_id ){
					return false;
				}

				$pass = false;
				// Chargement des colis
				if( $rpack = dlv_package_prices_get( null, $srv_id, null, null, $ord_weight_sum ) ){
					while( $pack = ria_mysql_fetch_array($rpack) ){
						if( dlv_package_price_zones_exists( $pack['id'], $zone_id ) ){ // Zone du colis

							$port_val = $pack['price_ht'];
							if( $pack['is_prorata'] ){ // calcul de répartition par tranches
								$ord_weight_sum -= $pack['slice'];
								// On déduit deux fois $pack['slice'], car pour un poids de 105kg et une tranche à 100kg, le prix est celui de 100kg, pas de 200kg
								while( ($ord_weight_sum - $pack['slice']) > 0 ){
									$ord_weight_sum -= $pack['slice'];
									$port_val += $pack['sl_price'];
								}
							}

							// le premier est le bon, car dlv_package_prices_get() trie par quantité décroissante, donc on est sur la plus proche du poids réeel
							$pass = true;
							break;
						}
					}
				}

				if( !$pass && $ex_zone_id ){
					// Chargement des colis
					if( $rpack = dlv_package_prices_get( null, $srv_id, null, null, $ord_weight_sum ) ){
						while( $pack = ria_mysql_fetch_array($rpack) ){
							if( dlv_package_price_zones_exists( $pack['id'], $ex_zone_id ) ){ // Zone du colis

								$port_val = $pack['price_ht'];
								if( $pack['is_prorata'] ){ // calcul de répartition par tranches
									$ord_weight_sum -= $pack['slice'];
									// On déduit deux fois $pack['slice'], car pour un poids de 105kg et une tranche à 100kg, le prix est celui de 100kg, pas de 200kg
									while( ($ord_weight_sum - $pack['slice']) > 0 ){
										$ord_weight_sum -= $pack['slice'];
										$port_val += $pack['sl_price'];
									}
								}

								// le premier est le bon, car dlv_package_prices_get() trie par quantité décroissante, donc on est sur la plus proche du poids réeel
								$pass = true;
								break;
							}
						}
					}
				}

				// dans le cas de schenker le prix au dessus de 100 k est différent
				if( $ord_weight_sum > 100 ){
					$port_val = $port_val * ( $ord_weight_sum / 100 );
				}

				// test si la zone est exclu pour le franco
				$rcurrent_zone = sys_zones_get( $zone_id );
				if( $rcurrent_zone && ria_mysql_num_rows($rcurrent_zone) ){
					$current_zone = ria_mysql_fetch_array($rcurrent_zone);
					if( !in_array( mb_substr( $current_zone['code'], 0, 2, 'UTF-8'), $config['franco_excluded']) ){

						// récupère le franco
						$franco = 0;
						$dlv = dlv_services_get( $srv_id );
						if( $dlv && ria_mysql_num_rows( $dlv ) ){
							$dlv = ria_mysql_fetch_array( $dlv );
							$franco = $dlv['dealer-free-ht'];
						}

						if( $port_val && ord_orders_get_total_without_port( $ord_id ) > $franco  ){
							$port_val = 0;
						}

					}
				}
			}

			// test le franco du srv
			$total_ht = ord_orders_get_total_without_port( $ord_id );
			if( $srv['dealer-free-ht'] > 0 && $srv['dealer-free-ht'] < $total_ht ){
				$port_val = 0;
			}


			// test si un produit dans la commande donne le droit au franco
			$roprd = ord_products_get($ord_id);
			if( $roprd && ria_mysql_num_rows($roprd) ){
				while( $oprd = ria_mysql_fetch_array($roprd) ){
					if( fld_object_values_get($oprd['id'], $config['fld_prd_franco']) == 'Oui'){
						$port_val = 0;
						break;
					}
				}
			}

			if( $port_val > 0 && isset($config['wst_id']) && $config['wst_id'] != 46 ){ // ajout de 2 euro que pour poubelle pro
				$port_val += 2;
			}

			// si spécifié, crée la ligne de frais de port adéquat
			if( $do_all_work ){
				$rprd_info = prd_products_get_simple( $config['default_port_id']  );
				if( !$rprd_info || !ria_mysql_num_rows($rprd_info) ){
					return false;
				}
				$prd_info = ria_mysql_fetch_array( $rprd_info );

				ord_products_del( $ord_id, $prd_info['id'] );
				if( !ord_products_add_free( $ord_id, $prd_info['ref'], $prd_info['name'], $port_val ) ){
					return false;
				}
			}

			break;
		}

		case 40 :{
			/*
			BtoB :
			Paris et Banlieue : FRANCO
			France métropolitaine et Corse :
			De 0 à 209 €HT: 15 €HT
			Au dela de 209 €HT Franco

			BtoC :
			Paris et Banlieue, France métropolitaine et Corse :
			De 0 à 99 €TTC: 10 €TTC
			Au dela de 99 € TTC Franco

			pour les frais de port à l'international :
			20€ HT pour les comptes professionels
			20€ TTC pour les comptes particuliers
			*/

			// accès au service de livraison
			$rservice = dlv_services_get($srv_id);
			if( !$rservice || !ria_mysql_num_rows( $rservice ) ){
				return false;
			}

			// accès à l'adresse de livraison
			$ord_address = ria_mysql_fetch_assoc(ord_orders_get_with_adresses( 0, $ord_id ));
			$zip = mb_substr(str_pad($ord_address['dlv_postal_code'], 5, '0', STR_PAD_LEFT ), 0, 2, 'UTF-8');

			// nous considérons que le pays par défaut est la france.
			if (trim($ord_address['dlv_country']) == '' || strtoupper2($ord_address['dlv_country']) == 'FR') {
				$ord_address['dlv_country'] = 'FRANCE';
			}

			// accès au montant de la commande
			// la carte cadeau n'est pas considéré comme une remise mais comme un moyen de paiement
			// donc le franco de port s'applique quand même
			// on doit donc récupérer le montant sans la remise, used_total = false
			$no_gift_card = true;
			if( is_array($pmts) ){
				foreach ($pmts as $promo) {
					if( $promo['pmt_type'] == _PMT_TYPE_GIFTS ){
						$no_gift_card = false;
						break;
					}
				}
			}
			$total_ht = ord_orders_get_total_without_port( $ord_id, false, $no_gift_card );

			// Calcul du frais de port suivant le code postal et le montant de la commande
			$ile_de_france = strtoupper($ord_address['dlv_country']) == 'FRANCE' && ( $zip == 75 || $zip == 77 || $zip == 78 || $zip == 91 || $zip == 92 || $zip == 93 || $zip == 94 || $zip == 95 );

			$profile_id = gu_users_get_prf($ord['user']);

			$port_val = 0;
			if( $config['wst_id'] == 65 ){
				if( $ile_de_france ){
					$port_val = 0;
				}elseif( strtoupper($ord_address['dlv_country']) == 'FRANCE' ){
					if( $total_ht <= 209 ){
						$port_val = 15;
					}
				}else{
					$port_val = $profile_id == 2 ? 20/1.2 : 20.0;
				}
			}else{
				if( strtoupper($ord_address['dlv_country']) == 'FRANCE' ){
					if( $total_ht <= 99/1.2 ){
						$port_val = 10/1.2;
					}
				}else{
					$port_val = $profile_id == 2 ? 20/1.2 : 20.0;
				}
			}
			break;
		}
	}

	return $port_val;
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/**	Permet Récupérer le port franco (Pour Proloisirs)
 *	Le franco est à 2500€ par défaut
 * 	Il passe à 2000 € HT du 1/06 au 31/09
 *	@return int le port franco correspondant à la date du jour
 *
 */
function ord_orders_portf_get(){
	$month = date('m');
	$price = 2500;

	// Si mois courant entre le 6 et le 9
	if( $month >= 6 && $month <= 9 ) {
		$price = 2000;
	}

	return $price;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de recuperer la quantité de produits en cours de commande chez un utilisateur
 *	@param int $usr_id identifiant de l'utilisateur
 *	@param int $prd_id identifiant du produit
 *	@return int la qte d'un produit en cours de commande chez l'utilisateur passé
 */
function ord_supplier_pending( $usr_id, $prd_id ){
	global $config;
	$qte = "sum( cmd.cqte - ifnull(bl.cqte, 0) )";
	if( !$config['use_decimal_qte'] ){
		$qte = "cast(sum( cmd.cqte - ifnull(bl.cqte, 0) ) as signed)";
	}
	$select = ' select '.$qte.' as qte
				from (
					select prd_ord_id, prd_id, sum(prd_qte) as cqte
					from riashop.ord_products, riashop.ord_orders
					where ord_tnt_id='.$config['tnt_id'].' and prd_tnt_id=ord_tnt_id and prd_parent_id is null and prd_ord_id in (
							select ord_id from riashop.ord_orders where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$usr_id.' and ord_parent_id is null
						) and prd_id='.$prd_id.'
						and ord_id = prd_ord_id
						and ord_state_id not in (9,10)
						and prd_qte > 0
					group by prd_ord_id, prd_id
				) as cmd

				left join (
					select prd_ord_id, prd_id, sum(prd_qte) as cqte
					from riashop.ord_bl_products
					where prd_tnt_id='.$config['tnt_id'].' and prd_bl_id in (
							select bl_id from riashop.ord_bl
							where bl_tnt_id='.$config['tnt_id'].' and bl_usr_id='.$usr_id.' and bl_masked = 0
						)
						and prd_id='.$prd_id.'
						and prd_qte > 0
					group by prd_ord_id, prd_id
				) as bl on (
						cmd.prd_id=bl.prd_id and cmd.prd_ord_id=bl.prd_ord_id
					)

				';

	return ria_mysql_query($select);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour la date de livraison prévue d'une commande.
 *	@param int $ord Identifiant de la commande.
 *	@param string $date_livr Date de livraison prévue de la commande (format FR ou EN, avec ou sans la partie horaire. Si cette dernière est spécifiée, elle n'est pas prise en compte). Si la date spécifiée est invalide, la date de livraison de la commande est assignée à NULL, aucune erreur n'est retournée.
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_orders_set_date_livr( $ord, $date_livr ){

	if( !is_numeric($ord) || $ord <= 0 ){
		return false;
	}

	$datesql = 'NULL';
	if( isdateheure($date_livr) ){
		$datesql = 'DATE("'.dateheureparse($date_livr).'")';
	}

	global $config;

	$prev_date = '';
	$rdate = ria_mysql_query('
		select ord_date_livr, ord_date from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$ord.'
	');
	if( ria_mysql_num_rows($rdate) ){
		$prev_date = ria_mysql_result( $rdate, 0, 0 );
	}

	$res = ria_mysql_query('
		update riashop.ord_orders
		set ord_date_livr = '.$datesql.'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord.'
	');

	// En cas de changement de date de livraison, une notification est envoyée
	if( $res ){
		ord_orders_notify_new_datelivr( $ord, $prev_date, $date_livr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction envoie une alerte email pour notifier une nouvelle date de livraison pour une commande
 * 	Elle a été développée pour l'instant uniquement pour les Cheminées de Chazelles
 * 	@param int $ord_id Obligatoire, identifiant de la commande
 *  @param string $prev_date_livr Obligatoire, date de livraison précédente
 *  @param string $new_date_livr Obligatoire, nouvelle date de livraison
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_notify_new_datelivr( $ord_id, $prev_date_livr, $new_date_livr ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	// Uniquement Chazelles pour l'instant
	if( $config['tnt_id']!=8 ){
		return false;
	}

	// Charge le compte client lié à cette commande (force le fait qu'il doit s'agit d'un compte professionnel)
	// Attention Chazelles : on contrôle que le champ avancé 10070 (Peut se connecter à l'Extranet ?) est bien à Oui
	$r_user = ria_mysql_query('
		select usr_email as email, ord_ref, ord_piece, ord_id, ord_wst_id as wst_id
		from gu_users
			join ord_orders on ( ord_tnt_id = usr_tnt_id and ord_usr_id = usr_id and ord_id = '.$ord_id.' and ord_masked = 0 )
			join fld_object_values on (pv_tnt_id = usr_tnt_id and pv_obj_id_0 = usr_id and pv_obj_id_1 = 0 and pv_obj_id_2 = 0 and pv_fld_id = 10070 and pv_value = "Oui")
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_date_deleted is null
	');

	if( !$r_user || !ria_mysql_num_rows($r_user) ){
		return false;
	}

	$user = ria_mysql_fetch_assoc($r_user);

	$config_copy = $config;

	// Chargement de la configuration en fonction du site de la commande
	$ar_to_reload = array(
		'email_html_header', 'email_html_footer',
	);

	// Rechargement de ses variables avec le nouveau site
	if( $rconf_from_ord = cfg_overrides_get( $user['wst_id'], array(), $ar_to_reload ) ){
		while( $conf_from_ord = ria_mysql_fetch_assoc($rconf_from_ord) ){
			$config[ $conf_from_ord['code'] ] = $conf_from_ord['value'];
		}
	}
	$config['wst_id'] = $user['wst_id'];

	// Charge la configuration mail en fonction du site de la commande
	// Cela fait office pour savoir si la fonctionnalité est activé ou non
	$rcfg = cfg_emails_get('ord-date-livr');
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		$config = $config_copy;
		return true;
	}
	$en_prev_date = dateheureparse( $prev_date_livr );
	$en_new_date = dateheureparse( $new_date_livr );

	// Vérifie si ce sont bien des dates
	if( !$en_prev_date || !$en_new_date ){
		$config = $config_copy;
		return false;
	}
	$prev_date_livr = new DateTime( $en_prev_date );
	$Date_error = DateTime::getLastErrors();

	// Vérifie $prev_date_livr est bien un objet DateTime
	if( !$prev_date_livr || (is_array($Date_error) && isset($Date_error['error_count']) && $Date_error['error_count'] > 0) ){
		$config = $config_copy;
		return false;
	}

	// Les commandes en retard de plus de 6 mois ne seront plus notifiées
	$date_limit = strtotime('6 months ago');
	if( $prev_date_livr->getTimestamp() < $date_limit ){
		$config = $config_copy;
		return false;
	}

	$new_date_livr = new DateTime( $en_new_date );
	$Date_error = DateTime::getLastErrors();

	// Vérifie $new_date_livr est bien un objet DateTime
	if( !$new_date_livr || (is_array($Date_error) && isset($Date_error['error_count']) && $Date_error['error_count'] > 0) ){
		$config = $config_copy;
		return false;
	}

	// Uniquement si les dates sont différentes et pas la même semaine
	if( $prev_date_livr==$new_date_livr || $prev_date_livr->format('W') == $new_date_livr->format('W') ){
		$config = $config_copy;
		return false;
	}

	// Uniquement si la prochaine date de livraison est valide
	if( $new_date_livr==false ){
		$config = $config_copy;
		return false;
	}

	$cfg = ria_mysql_fetch_assoc($rcfg);

	$ord_name = ord_orders_name($user['ord_ref'], $user['ord_piece'], $user['ord_id']);

	// Récupération des produits
	$rprd = ord_products_get($ord_id);
	$ord_dlv_timestamp = $new_date_livr->getTimestamp();
	$products = [];

	if( ria_mysql_num_rows($rprd) ){

		while($prd = ria_mysql_fetch_assoc($rprd) ){

			if( prd_products_is_port($prd['ref']) ){
				continue;
			}
			$prd_date = new DateTime($prd['date_livr_en']);
			$prd_timestamp = $prd_date->getTimestamp();

			// Vérification de la date de livraison du produit
			if( $prd_timestamp == $ord_dlv_timestamp ){
				$products[] = trim($prd['ref']) != '' ? $prd['ref'] : $prd['id'];

			}
		}
	}
	$count_prd = count($products);

	// Préparation et envoie de l'email
	$email = new Email();
	$email->setFrom($cfg['from']);
	$email->addTo($user['email']);

	$email->setSubject('Changement de date de livraison');

	$email->addHtml( $config['email_html_header'] );

	$email->addParagraph('Chère cliente, cher client,');

	$fr_prev_date = $prev_date_livr->format('d/m/Y');
	$fr_new_date = $new_date_livr->format('d/m/Y');

	if( $count_prd === 1 ){
		$email->addParagraph('Le produit '.implode(', ', $products).' de votre commande n°'.$ord_name.' initialement prévu en livraison pour le '.$fr_prev_date.' a fait l’objet d’une modification de délai, nous vous informons que la nouvelle date sera la suivante : '.$fr_new_date.'.');

	}elseif( $count_prd > 1 ){
		$email->addParagraph('Les produits '.implode(', ', $products).' de votre commande n°'.$ord_name.' initialement prévus en livraison pour le '.$fr_prev_date.' ont fait l’objet d’une modification de délai, nous vous informons que la nouvelle date sera la suivante : '.$fr_new_date.'.');

	}else{
		$email->addParagraph('La livraison de votre commande n°'.$ord_name.' initialement prévue pour le '.$fr_prev_date.' a fait l’objet d’une modification de délai, nous vous informons que la nouvelle date sera la suivante : '.$fr_new_date.'.');

	}
	$url_order = 'https://extranet.chazelles.com/mes-commandes/commande/?ord='.$ord_id;

	$email->addParagraph( 'Pour accéder à votre suivi de livraison pour cette commande vous pouvez vous rendre sur votre espace Mon Compte accessible depuis : <a href="'.$url_order.'">'.$url_order.'</a>.' );
	$email->addParagraph( 'Nous vous remercions de votre compréhension,' );

	$email->addHtml( $config['email_html_footer'] );

	$config = $config_copy;
	return $email->send();
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le contact qui à pris la commande, attention il n'y a pas de contrôle sur le fait que ce soit un contact ou un utilisateur, il est donc possible d'avoir dans ce champs des comptes qui ne sont pas des contacts.
 *	@param int $ord Identifiant de la commande.
 *	@param int $contact_id identifiant du contact qui à pris la commande
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_orders_set_contact_id( $ord, $contact_id ){
	global $config;

	if( !is_numeric($ord) || $ord <= 0 ){
		return false;
	}
	if( !is_numeric($contact_id) || ($contact_id > 0 && !gu_users_exists($contact_id)) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_contact_id = '.( $contact_id <= 0 ? 'null' : $contact_id ).'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le revendeur qui à pris la commande, attention il n'y a pas de contrôle sur le fait que ce soit un utilisateur-revendeur.
 *	@param int $ord Identifiant de la commande.
 *	@param int $reseller_id identifiant du revendeur qui à pris la commande
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_orders_set_reseller_id( $ord, $reseller_id ){
	global $config;

	if( !is_numeric($ord) || $ord <= 0 ){
		return false;
	}
	if( !is_numeric($reseller_id) || ($reseller_id > 0 && !gu_users_exists($reseller_id)) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_reseller_id = '.( $reseller_id <= 0 ? 'null' : $reseller_id ).'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord.'
	');

}
// \endcond
// \cond onlyria
/**	Cette fonction permet de mettre à jour le contact revendeur qui à pris la commande, attention il n'y a pas de contrôle sur le fait que ce soit un contact ou un utilisateur, il est donc possible d'avoir dans ce champs des comptes qui ne sont pas des contacts.
 *	@param int $ord Identifiant de la commande.
 *	@param $reseller_contact_id identifiant du contact revendeur qui à pris la commande
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_orders_set_reseller_contact_id( $ord, $reseller_contact_id ){
	global $config;

	if( !is_numeric($ord) || $ord <= 0 ){
		return false;
	}
	if( !is_numeric($reseller_contact_id) || ($reseller_contact_id > 0 && !gu_users_exists($reseller_contact_id)) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_reseller_contact_id = '.( $reseller_contact_id <= 0 ? 'null' : $reseller_contact_id ).'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord.'
	');

}
// \endcond

// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/**	récupère la date de livraison sur l'entête d'une commande
 *	@param int $ord Identifiant de la commande
 *	@param $brut Permet de récupérer le format d'origine de la date
 *	@return La date de livraison prévue de la commande, au format jj/mm/aaaa à hh:mm (sauf si $brut)
 *	@return bool False en cas d'échec
 */
function ord_orders_get_date_livr( $ord, $brut=false ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	$field = 'ord_date_livr';
	if( !$brut ){
		$field = 'date_format(ord_date_livr, "%d/%m/%Y à %H:%i")';
	}

	$r = ria_mysql_query(
		'select '.$field.' as date_livr
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 'date_livr' );
}

/**	Cette fonction permet de récupérer la date de passation d'une commande
 *	@param int $ord Identifiant de la commande
 *	@param $brut Permet de récupérer le format d'origine de la date
 *	@return La date de livraison prévue de la commande, au format jj/mm/aaaa à hh:mm (sauf si $brut)
 *	@return bool False en cas d'échec
 */
function ord_orders_get_date( $ord, $brut=false ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	$field = 'ord_date';
	if( !$brut ){
		$field = 'date_format(ord_date, "%d/%m/%Y à %H:%i")';
	}

	$r = ria_mysql_query(
		'select '.$field.' as date
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 'date' );
}

// \cond onlydev
/// @}
// \endcond

// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/** Cette fonction permet de récupérer le pays de livraison d'une commande
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@return Le pays de livraison
 */
function ord_orders_get_livr_country( $ord_id ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select if( ifnull( dlv.adr_country, "" ) != "", dlv.adr_country, inv.adr_country ) as livr_country
		from riashop.ord_orders
			join riashop.gu_adresses as inv on ((ord_tnt_id = inv.adr_tnt_id or 0=inv.adr_tnt_id) and ord_adr_invoices = inv.adr_id)
			join riashop.gu_adresses as dlv on ((ord_tnt_id = dlv.adr_tnt_id or 0=dlv.adr_tnt_id) and ord_adr_delivery = dlv.adr_id)
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['livr_country'];
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/**	Cette fonction retourne la liste des commandes n'étant plus synchronisées avec la gestion commerciale.
 *	@param $detect_type Optionnel, détermine si la détection est réalisée sur les entêtes (head), les lignes (line) ou les deux (all).
 *	@param $states Optionnel, tableau d'identifiants de statuts.
 *	@return array Un tableau contenant les identifiants de commandes non synchronisées.
 *	@return bool False en cas d'échec.
 */
function ord_orders_get_need_sync( $detect_type='all', $states=array() ){

	$states = control_array_integer( $states, false );
	if( $states === false ){
		return false;
	}

	global $config;

	$sql_head = '
		select
			ord_id as id,
			ord_date as date
		from riashop.ord_orders
		join riashop.gu_users on ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_need_sync = 1
			and ord_parent_id is null
			and ord_masked = 0
	';
	if( sizeof($states) ){
		$sql_head .= ' and ord_state_id in ('.implode(', ', $states).')';
	}

	// filtrage uniquement sur les clients synchronisés (la variable de configuration est vrai par défaut)
	if( $config['import_orders_from_sync_usr'] ){
		$sql_head .= ' and (usr_ref != "" or exists ( ';
		$sql_head .= '	select 1 ';
		$sql_head .= '	from riashop.rel_relations_hierarchy ';
		$sql_head .= '	join riashop.gu_users cu2 on rrh_tnt_id=cu2.usr_tnt_id and rrh_src_0=cu2.usr_id ';
		$sql_head .= '	where rrh_rrt_id='.REL_USR_HIERARCHY.' ';
		$sql_head .= '		and rrh_tnt_id='.$config['tnt_id'].' and rrh_dst_0=ord_usr_id and rrh_dst_1=0 and rrh_dst_2=0 ';
		$sql_head .= '		and cu2.usr_is_sync=1 ';
		$sql_head .= '		and cu2.usr_ref != "" ';
		$sql_head .= '		and cu2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') ';
		$sql_head .= ')) ';
	}

	$sql_line = '
		select
			ord_id as id,
			ord_date as date
		from riashop.ord_products
		join riashop.ord_orders on prd_tnt_id = ord_tnt_id and prd_ord_id = ord_id
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_need_sync = 1
			and prd_parent_id is null
			and ord_parent_id is null
			and ord_masked = 0
	';
	if( sizeof($states) ){
		$sql_line .= ' and ord_state_id in ('.implode(', ', $states).')';
	}

	$sql = 'select * from (';
	switch( strtolower(trim($detect_type)) ){
		case 'head':
			$sql .= $sql_head;
			break;
		case 'line':
			$sql .= $sql_line;
			break;
		default:
			$sql .= $sql_head.' union '.$sql_line;
			break;
	}
	$sql .= ") as t group by id";

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	$ords_ar = array();

	while( $ord = ria_mysql_fetch_assoc($res) ){
		$ords_ar[] = $ord;
	}

	return $ords_ar;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de marquer l'entête d'une commande comme étant synchronisé avec la gestion commerciale
 *	@param int $ord Obligatoire, identifiant de la commande
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_set_is_sync( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_need_sync=0
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de marquer l'entête d'une commande comme n'étant pas/plus synchronisé avec la gestion commerciale
 *	@param int $ord Obligatoire, identifiant de la commande
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_set_need_sync( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_need_sync=1
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour du total de la commande pour les produits qui on été livré
 *	@param int $bl_id Obligatoire Identifiant du Bon de livraison
 *
 * 	@return bool Retourne true en cas de succès et false en cas d'échec
 */
function ord_update_total_delivred( $bl_id ){
	if (!is_numeric($bl_id) || $bl_id <= 0) {
		return false;
	}

	global $config;

	$r_order = ria_mysql_query('
		select distinct prd_ord_id as id
		from riashop.ord_bl_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl_id. '
			and prd_ord_id is not null
	');

	$result = true;
	if($r_order && ria_mysql_num_rows($r_order)>0){
		while($ord = ria_mysql_fetch_array($r_order)){
			$result = ria_mysql_query('
				update riashop.ord_orders
				set ord_total_ht_delivered=(
						select sum(prd_price_ht*prd_qte) as price
						from riashop.ord_bl_products
						where prd_tnt_id='.$config['tnt_id'].'
							and prd_ord_id = '.$ord['id'].'
					)
				where ord_tnt_id='.$config['tnt_id'].'
					and ord_id='.$ord['id'].'
					and ord_masked = 0
			');
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère le dépôt de livraison d'une commande
 *	@param int $ord Identifiant de la commande
 *	@return identifiant du dépot (peut être null) en cas de succès, False sinon
 */
function ord_orders_get_deposit( $ord ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_dps_id
		from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['ord_dps_id'];
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'identifiant de la commande dans le moteur de recherche.
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param int $cnt_id Optionnel, identifiant de la commande dans le moteur de recherche
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function ord_orders_set_cnt_id( $ord_id, $cnt_id=0 ){
	global $config;

	if (!is_numeric($ord_id) || $ord_id <= 0) {
		return false;
	}

	if (!is_numeric($cnt_id) || $cnt_id < 0) {
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_cnt_id = '.( $cnt_id > 0 ? $cnt_id : 'null' ).'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le dépôt de livraison d'une commande fournisseur
 *	@param int $ord Obligatoire, Identifiant de la commande
 *	@param int $dps Obligatoire, Identifiant du dépôt, 0 permet de valoriser le champ par NULL
 *	@return bool True en cas de succès, False sinon
 */
function ord_orders_set_deposit( $ord, $dps ){
	global $config;

	if( !ord_orders_exists( $ord ) ){
		return false;
	}
	if( !is_numeric( $dps ) || $dps<0 ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_dps_id='.( $dps==0 ? 'NULL' : $dps ).'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le type de package ( Cartons, palette, ... )
 *	@param int $ord Obligatoire, identifiant de la commande
 *	@param int $pkg Obligatoire, identifiant de l'emballage, 0 permet de valoriser le champ par NULL
 *	@return bool True en cas de succès, False sinon
 */
function ord_orders_set_package( $ord, $pkg ){
	global $config;

	if( !ord_orders_exists( $ord ) ){
		return false;
	}
	if( !is_numeric( $pkg ) || $pkg<0 ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_pkg_id='.( $pkg==0 ? 'NULL' : $pkg ).'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);

}
// \endcond

// \cond onlyria
/** Cette fonction calcule le montant total d'une commande sans ses frais de port
 *	Cette fonction part du montant précalculé dans l'entête et retire les montants des frais de port (pas de recalcul total)
 *	@param int $id Obligatoire, identifiant de la commande
 *	@param bool $ttc Facultatif, détermine si le montant retourné est HT ou TTC
 *	@param bool $used_total Facultatif, par défaut on utilise le total de la commande, mettre False pour calculer le total selon les produits dans la commande
 *	@param int $zone Facultatif, Identifiant d'une zone
 *	@param array $prd_ids Facultatif, tableau d'identifiants de produits sur lesquels filtrer de résultat
 *	@param array $cat_ids Facultatif, tableau d'identifiants de catégories de produits sur lesquels filtrer de résultat
 *	@return bool False en cas d'échec
 *	@return float Le montant total de la commande sans frais de port
 */
function ord_orders_get_total_without_port( $id, $ttc=false, $used_total=true, $zone='', $prd_ids=array(), $cat_ids=array() ){
	global $config;

	if( !ord_orders_exists( $id ) ){
		return false;
	}

	$excluded_products = 0;

	if( !$used_total ){
		$piece = ord_orders_get_piece($id);
		$user_id = ord_orders_get_user($id);

		// Charge la catégorie tarifaire à partir du compte client
		$prc = isset($_SESSION['usr_prc_id']) && prd_prices_categories_exists($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : $config['default_prc_id'];
		$prf = isset($_SESSION['usr_prf_id']) && is_numeric($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] ? $_SESSION['usr_prf_id'] : false;
		if( $user_id ){
			$prc_id = gu_users_get_prc($user_id);
			if( $prc_id ){
				$prc = $prc_id;
			}
			$prf_id = gu_users_get_prf($user_id);
			if( $prf_id ){
				$prf = $prf_id;
			}
		}

		// Détermine si le tarif TTC doit être arrondi (suivant si le client est HT ou non).
		$round_ttc = prd_prices_categories_get_ttc($prc);

		$ratio = isset($config['weight_col_calc_lines']) && is_numeric($config['weight_col_calc_lines']) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;

		$sub_sql = ord_orders_colisage_sub_sql('"'.addslashes($piece).'"', $ratio, 'op.');

		$round_ttc_count = isset($config['round_digits_count_header']) && $config['round_digits_count_header'] > -1 ? $config['round_digits_count_header'] : $config['round_digits_count'];
		$round_dec = $round_ttc_count;
		if( $piece != '' && $config['tnt_id']==13 ){
			$round_dec = '( if(ifnull(p.prd_sell_weight, 0) = 1, '.$config['round_digits_count'].' + log10('.$ratio.'), '.$round_ttc_count.') )';
		}

		$sum_ht = 'sum( op.prd_price_ht * ('.$sub_sql.') )';
		$sum_ttc = 'sum( '.($round_ttc ? 'round(' : '' ).'ifnull(op.prd_price_ttc, op.prd_price_ht * op.prd_tva_rate)'.($round_ttc ? ','.$round_dec.')' : '').' * ('.$sub_sql.') ) ';

		$round_after_qte = isset($config['round_digits_after_qte']) && $config['round_digits_after_qte'];
		if( $round_after_qte ){
			$sum_ht = 'sum( round(op.prd_price_ht * ('.$sub_sql.'),'.$round_dec.') ) ';
			$sum_ttc = 'sum( round(ifnull(op.prd_price_ttc, op.prd_price_ht * op.prd_tva_rate) * ('.$sub_sql.'),'.$round_dec.') ) ';
		}

		$sql = '
			select '.( $ttc ? $sum_ttc : $sum_ht ).' as total
			from riashop.ord_products as op
				left join riashop.prd_products as p on p.prd_tnt_id=op.prd_tnt_id and p.prd_id=op.prd_id
				left join riashop.fld_object_values on ( op.prd_tnt_id=pv_tnt_id and op.prd_ord_id=pv_obj_id_0 and op.prd_id=pv_obj_id_1 and op.prd_line_id=pv_obj_id_2 and pv_fld_id='._FLD_PRD_COL_ORD_PRODUCT.' )
				left join riashop.prd_colisage_types on ( op.prd_tnt_id=col_tnt_id and ifnull(pv_value, 0)=col_id and col_is_deleted = 0 )
			where op.prd_tnt_id='.$config['tnt_id'].'
				and op.prd_ord_id='.$id.'
				and op.prd_ref not in (\''.implode( '\', \'', $config['dlv_prd_references'] ).'\')
		';

		if( is_array($cat_ids) && sizeof($cat_ids)>0){
			$sql .= ' and prd_id in (select cly_prd_id from riashop.prd_classify where cly_cat_id in ('.implode(', ', $cat_ids).'))';
		}

		if( is_array($prd_ids) && sizeof($prd_ids)>0){
			$sql .= ' and op.prd_id in ('.implode(', ', $prd_ids).')';
		}

		$res = ria_mysql_query( $sql );
		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		$tot = ria_mysql_result( $res, 0, 'total' );

		if (is_numeric($zone) && $zone > 0){
			$excluded_products = ord_orders_excluded_products_amount_get($ttc ? 'price_ttc' : 'price_ht', $id, $zone, $tot);
		}

		$tot -= $excluded_products;

		return $tot;
	} elseif( $rord = ord_orders_get( 0, $id ) ){
		if( $ord = ria_mysql_fetch_array( $rord ) ){

			// récupére la promotion sur la commande si elle existe
			$calc = true;
			$order_promotions = ord_orders_promotions_get($ord['id']);
			if( !empty($order_promotions) ){
				foreach ($order_promotions as $ord_promo) {
					$rpmt = pmt_codes_get( $ord_promo['pmt_id'] );
					if( $rpmt && ria_mysql_num_rows($rpmt) ){
						$pmt = ria_mysql_fetch_array( $rpmt );
						if( $pmt['free_shipping'] ){
							$services = pmt_codes_get_services( $ord['pmt_id'] );
							if( !$ord['str_id'] && !$ord['srv_id'] ){
								$calc = false;
								break;
							}elseif( in_array($ord['srv_id'], $services) ){
								$calc = false;
								break;
							}elseif( $ord['str_id'] && in_array(-1, $services) ){
								$calc = false;
								break;
							}
						}
					}
				}
			}
			$code_services = ord_orders_get_free_shipping( $id );
			if( is_array($code_services) && sizeof($code_services) ){
				if( ( !$ord['srv_id'] && !$ord['str_id'] ) || ( $ord['str_id'] && in_array(-1, $code_services) ) || in_array($ord['srv_id'], $code_services) ){
					$calc = false;
				}
			}
			$tot = $ttc ? $ord['total_ttc'] : $ord['total_ht'];

			if( $calc ){
				if( $rport = ord_products_get_port_details( $id ) ){
					while( $port = ria_mysql_fetch_assoc($rport) ){
						$tot -= $ttc ? $port['prd_price_ht'] * $port['prd_qte'] * $port['prd_tva_rate'] : $port['prd_price_ht'] * $port['prd_qte'];
					}
				}
			}

			if (is_numeric($zone) && $zone > 0){
				$excluded_products = ord_orders_excluded_products_amount_get($ttc ? 'price_ttc' : 'price_ht', $id, $zone, $tot);
			}

			$tot -= $excluded_products;

			return $tot;
		}
	}

	return false;
}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet de récupérer le total de l'écotaxe sur les lignes de la commande hors tva
 *
 * @param int $ord_id
 * @return int Retourne le total de la TVA
 * @throws InvalidArgumentException Si $ord_id n'est pas correcte
 * @throws RuntimeException Si il y a une erreur a l'exécution du sql
 */
function ord_orders_get_total_ecotaxe($ord_id){
	global $config;

	if( !is_numeric($ord_id) ||  $ord_id < 0){
		throw new InvalidArgumentException('ord_id doit être un entier positif');
	}

	$res = ria_mysql_query('
		select sum(prd_ecotaxe * prd_qte) as total
		from riashop.ord_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord_id.'
			and prd_ref not in (\''.implode( '\', \'', $config['dlv_prd_references'] ).'\')
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		throw new RuntimeException('Erreur exécution mysql');
	}

	$total = ria_mysql_result( $res, 0, 'total' );

	return $total;
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le moyen de paiement d'une pièce de vente
 *	Comme le moyen de paiement n'est stocké qu'au niveau de la commande, met à jour le moyen de paiement de toutes les commandes rattachées à une pièce supérieure
 *	@param int $id Identifiant de la pièce à mettre à jour
 *	@param $type Identifiant du type de pièce ( 1-ORD, 2-PL, 3-BL, 4-INV )
 *	@param int $pay_id Identifiant du moyen de paiement
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_piece_set_payment( $id, $type, $pay_id ){

	if( !is_numeric($type) || $type<1 || $type>4 ) return false;

	if( $type == 1 && !ord_orders_exists( $id ) ) return false;
	if( $type == 2 && !ord_pl_exists( $id ) ) return false;
	if( $type == 3 && !ord_bl_exists( $id ) ) return false;
	if( $type == 4 && !ord_invoices_exists( $id ) )  return false;

	if( $type == 1 )
		return ord_orders_pay_type_set( $id, $pay_id );
	else{
		if( $type == 2 )
			$rord = ord_pl_orders_get( $id );
		else if( $type == 3 )
			$rord = ord_bl_orders_get( $id );
		else
			$rord = ord_inv_orders_get( $id );

		if( !ria_mysql_num_rows( $rord ) ) return false;

		while( $ord = ria_mysql_fetch_array( $rord ) ){
			if( !ord_orders_pay_type_set( $ord['id'], $pay_id ) ) return false;
		}

		return true;
	}
}
// \endcond

// \cond onlyria
/** Cette fonction permet de transformer le contenu d'une commande pour une facturation HT
 *	Les règles de gestion permettant de savoir quels sont les comptes exonérés sont propres à chaque boutique
 *	@param int $ord Identifiant de la commande
 *	@return bool True en cas de succès, False sinon
 */
function ord_orders_remove_taxes( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}

	$result = ria_mysql_query( 'update riashop.ord_products set prd_tva_rate=1.000 where prd_ord_id='.$ord.' and prd_tnt_id='.$config['tnt_id'] );

	// met à jour le total
	if( $result ){
		ord_orders_update_totals( $ord );
	}

	return $result;
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le booléen is_relanced d'une commande donnée
 *	@param int $ord Identifiant de la commande
 *	@param bool $is_relanced Booléen déterminant la valeur du champ
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_set_relanced( $ord, $is_relanced ){
	global $config;

	if( !ord_orders_exists( $ord ) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders set ord_relanced='.( $is_relanced===true ? '1' : '0' ).'
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord
	);
}
// \endcond

// \cond onlydev
/** \ingroup models_orders_delivery
 *	@{
 */
// \endcond

/**	Cette fonction permet d'assigner un point-relais en tant qu'adresse d'expédition d'une commande
 *	Attention, si $rly_id est non-null, la fonction annule et remplace la destionation d'expédition précédente (magasin ou adresse particulière)
 *	@param int $ord_id Identifiant de la commande
 *	@param int $rly_id Identifiant du point-relais, ou NULL pour retirer le point-relais en place
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_set_relay( $ord_id, $rly_id ){
	global $config;

	if( !ord_orders_exists( $ord_id ) ){
		return false;
	}
	if( $rly_id!==null && !dlv_relays_exists( $rly_id ) ){
		return false;
	}

	$res = ria_mysql_query('
		update riashop.ord_orders set ord_rly_id='.( $rly_id ? $rly_id : 'NULL' ).'
		where ord_id='.$ord_id.' and ord_tnt_id='.$config['tnt_id']
	);

	// Annule les destinations précédentes
	if( $res && $rly_id ){
		ria_mysql_query('
			update riashop.ord_orders set ord_adr_delivery=NULL, ord_str_id=NULL
			where ord_id='.$ord_id.' and ord_tnt_id='.$config['tnt_id']
		);
	}

	return $res;
}

// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/** Cette fonction notifie les clients qui possèdent une commande en cours dont la date de livraison prévue d'un des articles a été modifiée
 *	@param int $prd Identifiant de l'article mis à jour
 *	@return bool False en cas d'échec, true sinon
 */
function ord_orders_notify_stock_livr( $prd ){
	global $config;

	if( !is_numeric( $prd ) || $prd<=0 ){
		return false;
	}

	// récupère les commandes en cours qui contiennent l'article mis à jour
	$result = ria_mysql_query('
		select ord_id
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
		and ord_state_id in (3, 4, 5, 6, 7, 8, 11)
		and ord_date_archived is null
		and ord_parent_id is null
		and ord_usr_id in (
			select usr_id
			from riashop.gu_users
			where usr_date_deleted is null and
			usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.','.PRF_RESELLER.') and
			usr_tnt_id='.$config['tnt_id'].'
		) and ord_id in (
			select op.prd_ord_id
			from riashop.ord_products as op
			where op.prd_tnt_id='.$config['tnt_id'].' and
			op.prd_id='.$prd.' and op.prd_qte>0 and op.prd_date_livr is null
			and not exists (
				select 1 from riashop.ord_pl_products as pp
				where pp.prd_tnt_id='.$config['tnt_id'].' and op.prd_id=pp.prd_id and op.prd_ord_id=pp.prd_ord_id and op.prd_line_id=pp.prd_line_id
			) and not exists (
				select 1 from riashop.ord_bl_products as bp
				where bp.prd_tnt_id='.$config['tnt_id'].' and op.prd_id=bp.prd_id and op.prd_ord_id=bp.prd_ord_id and op.prd_line_id=bp.prd_line_id
			) and not exists (
				select 1 from riashop.ord_inv_products as ip
				where ip.prd_tnt_id='.$config['tnt_id'].' and op.prd_id=ip.prd_id and op.prd_ord_id=ip.prd_ord_id and op.prd_line_id=ip.prd_line_id
			)
		)
	');

	if( $result==false ){
		return false;
	}

	while( $r=ria_mysql_fetch_array($result) ){
		ord_orders_set_alert_livr( $r['ord_id'],true );
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'avoir une estimation sur la date de d'expédition Bigship
 *	@param int $id Identifiant de la commande
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- delay : Date de livraison estimée
 *		- id : Identifiant de l'article
 */
function ord_orders_get_date_expe( $id ){
	global  $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$dps = prd_deposits_get_main();
	if( !$dps ) $dps = 0;

	$ref_exclude = '\''.implode('\',\'',$config[ 'dlv_prd_references' ]).'\'';

	if( $options = dlv_options_get() ){
		if( $options['gift-ref'] )
			$ref_exclude .= ',\''.$options['gift-ref'].'\'';
	}

	return ria_mysql_query('
		select
			(if(' . prd_stocks_get_sql() . '-sto_prepa>0,1,if(sto_com>0,if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),1000000,TO_DAYS(ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY)) - TO_DAYS(prd_stock_livr)),if(prd_sleep=1,if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),1000000,TO_DAYS(ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY)) - TO_DAYS(prd_stock_livr)),ps_delay)))) as delay,
			p.prd_id as id
		from riashop.ord_products as o
			left join riashop.prd_products as p on ( o.prd_tnt_id=p.prd_tnt_id and o.prd_id=p.prd_id )
			left join riashop.prd_suppliers as ps on ( o.prd_tnt_id=ps_tnt_id and o.prd_id=ps_prd_id )
			left join riashop.prd_stocks on ( o.prd_tnt_id=sto_tnt_id and o.prd_id=sto_prd_id and sto_is_deleted=0)
		where
			o.prd_tnt_id = '.$config['tnt_id'].' and ifnull(ps_main, 1)=1 and ifnull(sto_dps_id, '.$dps.')='.$dps.' and
			prd_ord_id='.$id.' and prd_parent_id is null and p.prd_ref not in ('.$ref_exclude.')
		order by
			ps_ref asc
	');

}
// \endcond

// \cond onlyria
/** Cette fonction retourne la date de création d'une commande.
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $always Optionnel, par défaut la véritable date de création est retournée (disponible depuis le 17/02/2014), mettre true pour retourne la date de la commande
 *	@return La date de création
 */
function ord_orders_get_date_created( $ord_id, $always=false ){
	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	global $config;

	if( !$always ){
		$sql = '
			select oos_datetime as date
			from riashop.ord_orders_states
			where oos_tnt_id='.$config['tnt_id'].'
				and oos_ord_id='.$ord_id.'
				and oos_state_id='._STATE_BASKET.'
		';
	}else{
		$sql = '
			select ifnull(oos_datetime, ord_date) as date
			from riashop.ord_orders
				left join riashop.ord_orders_states on (ord_tnt_id=oos_tnt_id and ord_id=oos_ord_id)
			where ord_tnt_id='.$config['tnt_id'].'
				and ord_id='.$ord_id.'
				and ifnull(oos_state_id, '._STATE_BASKET.')='._STATE_BASKET.'
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $sql, 0, 'date');
}
// \endcond

// \cond onlyria
/** Détermine l'anticipation d'achat d'un produit pour le mois suivant pour un client, à partir de son historique sur une durée déterminée
 *	@param int $usr Identifiant du client
 *	@param int $prd Identifiant du produit
 *	@param $months Nombre de mois d'historique (6 minimal, 12 par défaut)
 *
 *	@return La quantité anticipée pour le mois prochain, 0 en cas d'échec
 */
function ord_orders_get_next_month_qte( $usr, $prd, $months=12 ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($months) || $months<6 ) return false;

	$result = ria_mysql_query('
		select ( sum(prd_qte) / '.$months.' ) as qte
		from
			riashop.ord_orders
			join riashop.ord_products
				on ( prd_ord_id=ord_id and prd_tnt_id=ord_tnt_id )
		where
			ord_usr_id='.$usr.' and
			prd_id='.$prd.' and
			DATE_ADD( ord_date, INTERVAL '.$months.' MONTHS )>=now()
	');

	if( $result===false || !ria_mysql_num_rows($result) ) return 0;

	return ria_mysql_result( $result, 0, 0 );
}
// \endcond


/**    Détermine la quantité en réservation pour un client donné, éventuellement pour un des produits donnés, à partir des commandes en portefeuille
 *     @param int $usr_id Obligatoire, identifiant du client
 *     @param $products Facultatif, identifiant d'un produit ou tableau d'identifiants de produits
 *     @param $sync_only Facultatif, si True, seules les commandes synchronisées avec la gestion commerciale seront prises en compte (recommandé pour les boutiques dont l'historique est synchronisé)
 *
 *     @return bool False en cas d'échec
 *     @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *             - id : Identifiant du produit
 *             - stock : Quantité actuellement réservée pour ce client
 *     @deprecated sauf erreur, cette fonction n'est utilisée nulle part dans le moteur
 *     @todo Mal nommée et mal placée mais utilisée encore dans
 *     /var/www/extranet.proloisirs.fr/cron/rdvdeco/stock.phpet /var/www/extranet.proloisirs.fr/cron/raviday/stock.php
 */
function ord_orders_get_user_reservation( $usr_id, $products = array(), $sync_only = false ){
	global $config;

	if( !gu_users_exists( $usr_id ) ){
			return false;
	}
	if( !is_array($products) ){
			$products = array( $products );
	}
	foreach( $products as $p ){
		if( !prd_products_exists($p) ){
				return false;
		}
	}

	$sql = '
			select
					p.prd_id as id, sum(op.prd_qte) as stock
			from
					riashop.ord_products as op
					join riashop.ord_orders as o on ( op.prd_tnt_id=o.ord_tnt_id and op.prd_ord_id=o.ord_id )
					join riashop.prd_products as p on ( o.ord_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
			where
					op.prd_tnt_id='.$config['tnt_id'].'
					and o.ord_state_id in (3, 4, 5)
					and o.ord_date_archived is null
					and o.ord_masked=0
					and o.ord_usr_id='.$usr_id.'
					and p.prd_date_deleted is null
	';
	if( $sync_only ){
			$sql .= '
				and o.ord_piece!=\'\'
			';
	}
	if( sizeof($products) ){
			$sql .= '
				and p.prd_id in ('.implode( ', ', $products ).')
			';
	}
	$sql .= '
			group by
					p.prd_id
	';

	return ria_mysql_query( $sql );
}



// \cond onlyria
/**	Cette fonction met à jour le site du locataire sur lequel une commande a été passée
 *	Note : le website ne peut pas être réinitialisé à NULL par cette fonction, bien que la structure SQL l'autorise
 *	@param int $order Obligatoire, Identifiant de la commande
 *	@param int $website Obligatoire, Identifiant du site
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_set_website( $order, $website ){
	global $config;

	// Contrôle des paramètres
	if( !ord_orders_exists($order) ){
		return false;
	}
	if( !wst_websites_exists($website) ){
		return false;
	}

	// Charge le site web
	$rweb = wst_websites_get( $website );
	if( !$rweb || !ria_mysql_num_rows($rweb) ){ return false; }
	$web = ria_mysql_fetch_array($rweb);

	if( $web['tnt_id'] != $config['tnt_id'] ){ return false; }

	return ria_mysql_query('
		update riashop.ord_orders set ord_wst_id='.$website.' where ord_id='.$order.' and ord_tnt_id='.$config['tnt_id'].'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère l'identifiant du site rattaché à une commande
 *	@param $order Identifiant de la commande
 *	@return int L'identifiant du site, ou une chaine vide si aucun site n'est spécifié
 */
function ord_orders_get_website( $order ){
	global $config;

	if( !is_numeric($order) ){
		return false;
	}

	$r = ria_mysql_query('
		select ord_wst_id
		from riashop.ord_orders
		where ord_id='.$order.'
			and ord_tnt_id='.$config['tnt_id'].'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 0 );
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère la date d'archivage d'une commande
 *	@param int $ord_id Identifiant de la commande
 *	@return Date d'archivage si spécifiée, False sinon ou en cas d'erreur
 */
function ord_orders_get_date_archived( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select ord_date_archived from riashop.ord_orders
		where ord_id='.$ord_id.' and ord_tnt_id='.$config['tnt_id'].'
	');

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'archivage d'une commande. Elle est à privilégier à la place de ord_orders_update_status()
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param $date Facultatif, date spécifique d'archivage (par défaut la date du jour est utilisée)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_set_date_archived( $ord_id, $date=false ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}
	if( $date!==false && !isdate($date) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_date_archived='.( $date===false ? 'now()' : '\''.dateparse($date).'\'' ).'
		where ord_id='.$ord_id.' and ord_tnt_id='.$config['tnt_id'].'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le total de la commande à payer (utiliser sur les classes de paiements)
 *	@param int $ord_id Obligatoire, identifiant de la commande concerné
 *	@param bool $ttc Facultatif, retourne le montant en ttc si true sinon en ht
 *	@param bool $simulate_total Facultatif, permet de simuler un total de commande plutôt que de prendre celui d'origine
 *
 *	@return float|bool un montant en cas de succès, et false en cas d'échec
 */
function ord_orders_get_total_to_pay( $ord_id, $ttc=false, $simulate_total=false ){
	global $config;

	if( !ord_orders_exists($ord_id) ){
		return false;
	}

	if( $config['tnt_id'] == 23 ){
		if( !isset($_SESSION['current_paiement']) || $_SESSION['current_paiement'] != 'sofinco' ){

			$ord = ria_mysql_fetch_array(ord_orders_get(0,$ord_id));
			if( is_numeric($simulate_total) ){
				$total_ord = $simulate_total;
			}else{
				$total_ord = ( $ttc ? $ord['total_ttc'] : $ord['total_ht'] );
			}

			if( $total_ord < $config['accompte_plafond'] ){
				return $total_ord;
			}

			$accompte = $total_ord * $config['accompte'];
			return $accompte;

		}
	}

	return ord_orders_get_total($ord_id, $ttc);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour la date d'une commande
 *	@param int $id Obligatoire, Identifiant de la commande
 *	@param $date Obligatoire, Nouvelle date
 *	@param $with_hour Facultatif, booléen indiquant si la date passé dispose des heures/minutes/secondes (true) ou non (false, valeur par défaut)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_set_date( $id, $date, $with_hour=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}
	if( !$with_hour && !isdate($date) ){
		return false;
	}
	if( $with_hour && !isdateheure($date) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_date="'.( !$with_hour ? dateparse($date) : dateheureparse($date) ).'"
		where ord_id='.$id.' and ord_tnt_id='.$config['tnt_id'].'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le numero de piece d'une commande
 *  @param int $id Obligatoire, Identifiant de la commande
 * 	@param string $piece Obligatoire, Nouveau numero de piece de la commande
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function ord_orders_set_piece( $id, $piece ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !is_string($piece) ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_piece = "'.addslashes($piece).'"
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$id.'
	');

}
// \endcond

// \cond onlyria
/**	Détermine si la commande donnée est au statut spécifié (ou inclue dans les statuts spécifiés)
 *	@param int $ord_id Identifiant de la commande
 *	@param int|array $states Identifiant du statut, ou tableau d'identifiants de statuts
 *
 *	@return bool True si la commande est au statut spécifié, False sinon
 */
function ord_orders_is_state( $ord_id, $states ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}
	if( is_array($states) ){
		if( !sizeof($states) ){
			return false;
		}
		foreach( $states as $s ){
			if( !is_numeric($s) || $s<=0 ){
				return false;
			}
		}
	}elseif( !is_numeric($states) || $states<=0 ){
		return false;
	}else{
		$states = array($states);
	}

	$r = ria_mysql_query('
		select 1 from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$ord_id.'
		and ord_state_id in ('.implode(', ', $states).')
	');

	return $r && ria_mysql_num_rows($r);
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si une commande donnée est un panier
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param $and_model Optionnel, détermine si les modèles de panier sont également considérés comme des paniers (par défaut oui)
 *	@param $old_states Optionnel, détermine si certains statuts anciens doivent être inclus comme étant des paniers (2, 13...). Par défaut non
 *	@param $and_devis Optionnel détermine si le statut "Devis" doit être pris en considération.
 *
 *	@return bool True si la commande donnée est un panier, False sinon
 */
function ord_orders_is_cart( $ord_id, $and_model=true, $old_states=false, $and_devis=false ){
	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	$states = ord_states_get_uncompleted( $and_devis );
	if( $and_model ){
		$states[] = _STATE_MODEL;
	}
	if( $old_states ){
		$states[] = _STATE_NO_FINISH;
		$states[] = _STATE_BASKET_CANCEL;
	}

	return ord_orders_is_state( $ord_id, $states );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de tester si la commande à besoin d'une subdivision des lignes d'abonnement.
 *	@param int $ord_id Identifiant de la commande
 *	@return bool True si la commande à besoin d'un split ( ord_orders_split_by_subscription() ), False sinon. Les commandes avec code promotion retournent systématiquement False
 */
function ord_orders_need_subscription_split( $ord_id ){

	if( !$ord_id || !is_numeric($ord_id) || ord_orders_is_cart( $ord_id ) ){
		return false;
	}

	$rord = ord_orders_get( 0, $ord_id );
	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}
	$pmt_id = ria_mysql_result($rord, 0, 'pmt_id');

	// récupère les abonnnements de la commande si aucun ou 1 pas besoin de spliter la commande.
	$rsubs = ord_subscriptions_get( $ord_id );
	if( !$rsubs || ria_mysql_num_rows($rsubs) < 2 ){
		return false;
	}

	if( $pmt_id ){
		error_log(__FILE__.' - ord_orders_need_subscription_split() impossible de splitter une commande '.$ord_id.' avec code promotion.');
		return false;
	}

	// s'assure que les abonnements trouvés font référence à des lignes existantes
	$count_ok = 0;
	while( $sub = ria_mysql_fetch_assoc($rsubs) ){
		$roprd = ord_products_get( $ord_id, false, $sub['prd_id'], '', $sub['line_id'] );
		if( $roprd && ria_mysql_num_rows($roprd) ){
			$count_ok++;
		}
	}

	if( $count_ok != ria_mysql_num_rows($rsubs) ){
		error_log(__FILE__.' - ord_orders_need_subscription_split() les abonnements de la commande '.$ord_id.' ne font pas tous référence à une ligne dans la table ord_products.');
	}

	return $count_ok > 1;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de subdiviser une commande contenant plusieurs lignes d'abonnement en plusieurs commandes (ayant les mêmes propriétés)
 *	Les autres produits que les abonnements sont laissés sur la commande d'origine
 *	Les commandes avec code promotion ne peuvent pas être traitées actuellement
 *	Cette fonction doit être utilisé pour l'écriture dans commandes dans la gestion commerciale Sage, qui n'autorise qu'un seul abonnement par commande
 *	@param int $ord_id Identifiant de la commande
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_split_by_subscription( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id<=0 || ord_orders_is_cart( $ord_id ) ){
		return false;
	}

	$rord = ord_orders_get( 0, $ord_id );
	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}

	$ord = ria_mysql_fetch_array( $rord );

	if( $ord['pmt_id'] ){
		error_log( __FILE__.':'.__LINE__.' impossible de splitter une commande ('.$ord_id.') avec code promotion' );
		return false;
	}

	// récupère les abonnnements de la commande si aucun ou 1 pas besoin de spliter la commande.
	$rsubs = ord_subscriptions_get( $ord_id );
	if( !$rsubs || ria_mysql_num_rows($rsubs)<2 ){
		return true;
	}

	$sub_ar = array();
	while( $sub = ria_mysql_fetch_array($rsubs) ){
		$sub_ar[] = $sub;
	}

	// ce marqueur indique que la commande d'origine a subie au moins une modification (même si la fonction n'a pas été au bout)
	$have_lost = false;

	$cpt=0;
	foreach( $sub_ar as $sub ){

		if( $cpt>0 ){

			// récupère le produit dans la commande actuel
			$rprd = ord_products_get( $ord['id'], false, $sub['prd_id'], '', $sub['line_id'] );
			if( !$rprd || !ria_mysql_num_rows($rprd) ){
				continue;
			}
			$prd = ria_mysql_fetch_array( $rprd );

			// création d'un nouvelle commmande
			$new_ord_id = ord_orders_add_sage( $ord['user'], $ord['date_en'], _STATE_BASKET, '', $ord['piece'], $ord['ref'], false, $ord['wst_id'] );

			if( !$new_ord_id ){
				if( $have_lost ){
					error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
				}
				return false;
			}

			// ajout de la ligne produit dans la commande
			if( ord_products_add_sage( $new_ord_id, $prd['id'], $prd['line'], $prd['ref'], $prd['name'], $prd['qte'], $prd['price_ht'], $prd['tva_rate'], $prd['date-livr'], $prd['price_ttc'], $prd['ecotaxe'] ) ){

				// attache le relay
				if( $ord['rly_id'] && !ord_orders_set_relay( $new_ord_id, $ord['rly_id'] ) ){
					if( $have_lost ){
						error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
					}
					ord_orders_del_sage( $new_ord_id );
					return false;
				}

				// attache le magasin
				if( $ord['str_id'] && !ord_orders_set_dlv_store( $new_ord_id, $ord['str_id'] ) ){
					if( $have_lost ){
						error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
					}
					ord_orders_del_sage( $new_ord_id );
					return false;
				}

				// attache le service de livraison
				if( $ord['srv_id'] && !ord_orders_set_dlv_service( $new_ord_id, $ord['srv_id'] ) ){
					if( $have_lost ){
						error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
					}
					ord_orders_del_sage( $new_ord_id );
					return false;
				}

				// attache l'adresse de livraison
				if( $ord['adr_delivery'] && !ord_orders_adr_delivery_set( $new_ord_id, $ord['adr_delivery'] ) ){
					if( $have_lost ){
						error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
					}
					ord_orders_del_sage( $new_ord_id );
					return false;
				}

				// ajoute la note, le revendeur, le commentaire, la date de livraison, le type de CB et le message cadeau
				if( $ord['dlv-notes'] ) ord_orders_dlv_notes_set( $new_ord_id, $ord['dlv-notes'] );
				if( $ord['seller_id'] ) ord_orders_set_seller_id( $new_ord_id, $ord['seller_id'] );
				if( $ord['comments'] ) 	ord_orders_comments_update( $new_ord_id, $ord['comments'] );
				if( $ord['ord_livr'] ) 	ord_orders_set_date_livr( $new_ord_id, $ord['ord_livr'] );
				if( $ord['card_id'] ) 	ord_card_type_set( $new_ord_id, $ord['card_id'] );
				if( $ord['opt-gift'] ) 	ord_orders_opt_gift_set( $new_ord_id, $ord['opt-gift'], $ord['opt-gift-message'] );

				// met à jour le moyen de paiement
				if( !ord_orders_pay_type_set( $new_ord_id, $ord['pay_id'] ) ){
					if( $have_lost ){
						error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
					}
					ord_orders_del_sage( $new_ord_id );
					return false;
				}

				$rsub_model = prd_subscriptions_get( $sub['sub_id'] );
				if( $rsub_model && ria_mysql_num_rows($rsub_model) ){
					$sub_model = ria_mysql_fetch_array($rsub_model);
					if( $sub_model['period'] != $sub['period'] || $sub_model['period_type'] != $sub['period_type'] || $sub_model['length_type'] != $sub['length_type'] ){
						error_log( __FILE__.':'.__LINE__.' Commande '.$ord['id'].' vers '.$new_ord_id.' : les propriétés fixes de l\'abonnement '.$sub['sub_id'].' ont été modifiées avant le split().' );
					}
				}

				// le test isdate ne fonctionne que sur la date sans l'heure
				if( trim($sub['date_start']) ){
					$sub['date_start'] = substr($sub['date_start'], 0, 10);
				}

				// ajoute la ligne de l'abonnement
				if( ord_subscriptions_add( $new_ord_id, $prd['id'], $prd['line'], $sub['sub_id'], !isdate($sub['date_start']) ? false : $sub['date_start'] ) ){

					// met à jour la longueur de l'abonnement le cas échéant
					if( isset($sub_model) && $sub_model['length'] != $sub['length'] ){
						if( !$sub['is_free'] ){
							error_log( __FILE__.':'.__LINE__.' Mise à jour anormale du paramètre "length" de l\'abonnement (opt_is_free est à False).' );
						}elseif( !ord_subscriptions_update_length( $new_ord_id, $prd['id'], $sub['length'], $prd['line'], $sub['sub_id'] ) ){
							error_log( __FILE__.':'.__LINE__.' Attention, ord_subscriptions_update_length('.$new_ord_id.', '.$prd['id'].', '.$sub['length'].', '.$prd['line'].', '.$sub['sub_id'].') a échoué.' );
						}
					}

					// supprime la ligne de l'abonnement de la commande principale
					if( !ord_subscriptions_del( $ord['id'], $prd['id'], $prd['line'], $sub['sub_id'] ) ){
						error_log( __FILE__.':'.__LINE__.' Attention, échec de ord_subscriptions_del('.$ord['id'].' - '.$prd['id'].' - '.$prd['line'].' - '.$sub['sub_id'].')' );
					}

					$have_lost = true;

					// supprime la ligne dans la commande principale
					if( !ord_products_del( $ord['id'], $prd['id'], $prd['line'] ) ){
						error_log( __FILE__.':'.__LINE__.' Attention, échec de ord_products_del('.$ord['id'].' - '.$prd['id'].' - '.$prd['line'].')' );
					}

				}else{
					if( $have_lost ){
						error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
					}
					ord_orders_del_sage( $new_ord_id );
					return false;
				}

				// met à jour l'état de la nouvelle commande
				if( !ord_orders_state_update( $new_ord_id, _STATE_WAIT_PAY, '', false ) ){
					error_log( __FILE__.':'.__LINE__.' Attention, échec de ord_orders_state_update('.$new_ord_id.' - '._STATE_WAIT_PAY.')' );
					return false;
				}

				if( $ord['state_id'] != _STATE_WAIT_PAY ){
					if( !ord_orders_state_update( $new_ord_id, $ord['state_id'], '', false ) ){
						error_log( __FILE__.':'.__LINE__.' Attention, échec de ord_orders_state_update('.$new_ord_id.' - '.$ord['state_id'].')' );
						return false;
					}
				}

				// remet la date de la commande initiale
				ord_orders_set_date( $new_ord_id, $ord['date_en'], true );

			}else{
				if( $have_lost ){
					error_log( __FILE__.':'.__LINE__.' Erreur critique dans la subdivision de '.$ord['id'] );
				}
				return false;
			}

		}

		$cpt++;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction subdivise une commande en deux, séparant les produits disponibles et ceux en reliquat.
 *	Il est possible de considérer les produits disponibles à terme (sous X jours) comme faisant partie de la commande "disponible". Même chose pour les produits en contremarque. Les produits type frais de port et non suivis en stock sont considérés comme disponibles.
 *	Une nomenclature variable est considérée comme disponible si tous ses composants le sont. Les nomenclatures simples utilisent le système classique de gestion des stocks.
 *	L'identifiant de la commande actuelle est utilisée pour les produits disponibles, une nouvelle commande est crée pour les reliquats.
 *	Une commande contenant un code promotion ne peut être subdivisée, car son total est dépendant de l'agencement des lignes qui la compose.
 *
 *	@param int $ord_id Obligatoire, identifiant de la commande à subdiviser.
 *	@param $days_available Optionnel, nombre de jours maximal avant réapprovisionnement du produit, permettant de considérer celui-ci comme étant disponible, même s'il est actuellement indisponible.
 *	@param $countermark_available Optionnel, détermine si les produits en contremarque seront ou non dans la commande "disponible".
 *	@param $split_partial Optionnel, active ou désactive le découpage en deux des lignes partiellement disponibles.
 *
 *	@return int L'identifiant de la commande "reliquat" crée.
 *	@return bool True si l'opération a réussi mais qu'aucune commande supplémentaire n'a été crée.
 *	@return bool False en cas d'échec.
 */
function ord_orders_split_by_availability( $ord_id, $days_available = 0, $countermark_available = false, $split_partial = true ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	$split_tnt_activated = array(22, 29);
	if( !in_array($config['tnt_id'], $split_tnt_activated) ){
		error_log('sécurité ord_orders_split_by_availability() : pas de split possible pour ce client.');
		return true;
	}

	// charge la commande
	$rord = ord_orders_get( 0, $ord_id );
	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}
	$ord = ria_mysql_fetch_assoc($rord);

	// charge les données spécifiques TPLC
	$tplc_adr_id = $tplc_rly_id = $tplc_srv_id = $tplc_port_amount = 0;
	if( $config['tnt_id'] == 22 ){
		if( !isset($config['fld_order_dlv_addr'], $config['fld_order_dlv_relay'], $config['fld_order_dlv_srv_id'], $config['fld_order_dlv_port']) ){
			return true;
		}
		$tplc_adr_id = fld_object_values_get( $ord['id'], $config['fld_order_dlv_addr'], '', false, true );
		$tplc_rly_id = fld_object_values_get( $ord['id'], $config['fld_order_dlv_relay'], '', false, true );
		$tplc_srv_id = fld_object_values_get( $ord['id'], $config['fld_order_dlv_srv_id'], '', false, true );
		$tplc_port_amount = fld_object_values_get( $ord['id'], $config['fld_order_dlv_port'], '', false, true );
		$tplc_port_amount = str_replace(array(',', ' '), array('.', ''), $tplc_port_amount);
		if( !$tplc_adr_id && !$tplc_rly_id ){
			return true;
		}
	}

	$tplc_promotions_codes_to_apply_forced = array();
	$tplc_amount_of_pmt = array();

	$order_promotions = ord_orders_promotions_get($ord['id']);
	if( !empty($order_promotions) ){
		if( $config['tnt_id'] != 22 ){
			// pas de subdivision possible si code promotion
			return false;
		}
		foreach($order_promotions as $order_promotion){
			$rpmt = pmt_codes_get( $order_promotion['pmt_id'] );
			if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
				error_log('Echec de la subdivision d\'une commande TPLC : le code promotion n\'a pas pu être chargé.');
				return false;
			}
			$pmt = ria_mysql_fetch_assoc($rpmt);
			if( $pmt['apply_on'] != 'order' ){
				error_log('Echec de la subdivision d\'une commande TPLC : le code promotion n\'est pas de type simple "order".');
				return false;
			}
			$rcnds = pmt_code_conditions_get( $order_promotion['pmt_id'] );
			if( $rcnds && ria_mysql_num_rows($rcnds) ){
				error_log('Echec de la subdivision d\'une commande TPLC : le code promotion contient des conditions spéciales.');
				return false;
			}
			if( $pmt['discount_type'] == 0 ){
				$tplc_amount_of_pmt[] = $pmt;
			}else{
				$tplc_promotions_codes_to_apply_forced[] = $pmt['code'];
			}
		}
	}

	if( !is_numeric($days_available) || $days_available <= 0 ){
		$days_available = 0;
	}

	$products = ord_products_get( $ord_id );

	if( !$products ){
		return false;
	}

	if( !ria_mysql_num_rows($products) ){
		return true;
	}

	// tableau des lignes qui seront à créer dans la commande "reliquat"
	// pour les nomenclatures, une colonne "childs_detail" est ajoutée : elle contient un tableau des ord_products enfants
	$rows_unstock = array();
	// nombre de lignes "en stock"
	$rows_instock_count = 0;
	// lignes partiellement en stock, avec ajout des colonnes "qte_rel" et "qte_sto" (répartition quantité reliquat et stock)
	$rows_partial = array();

	while( $p = ria_mysql_fetch_assoc($products) ){

		// charge le détail prd_products_get() du produit
		$rp_info = prd_products_get_simple( $p['id'] );
		if( !$rp_info || !ria_mysql_num_rows($rp_info) ){
			return false;
		}
		$p_info = ria_mysql_fetch_assoc($rp_info);

		// ajoute le stock décrémenté pour cette commande
		$p_info['stock'] += $p['qte'] * $p['col_qte'];

		if( $p_info['nomenclature_type'] == NM_TYP_VARIABLE && $p['child-line'] !== null ){
			// cas particulier des nomenclatures variables
			// il n'y a pas de partiellement disponible

			// chargement des composants
			$pchilds = ord_products_get( $ord_id, false, 0, '', null, false, -1, $p['id'], $p['child-line'] );
			if( !$pchilds ){
				return false;
			}

			$nom_is_unstock = false; // indique si la nomenclature est effectivement indisponible
			$childs_detail = array(); // enregistre pour utilisation ultérieure le détail de la nomenlature

			while( $ch = ria_mysql_fetch_assoc($pchilds) ){

				$childs_detail[] = $ch;

				// charge le détail prd_products_get() du composant
				$rpc_info = prd_products_get_simple( $ch['id'] );
				if( !$rpc_info || !ria_mysql_num_rows($rpc_info) ){
					return false;
				}
				$pc_info = ria_mysql_fetch_assoc($rpc_info);

				// ajoute le stock décrémenté pour cette commande
				$pc_info['stock'] += $ch['qte'] * $ch['col_qte'];

				if( prd_products_is_port( $ch['ref'] ) ){
					// frais de port
				}elseif( !$pc_info['follow_stock'] ){
					// non suivi en stock
				}elseif( $pc_info['countermark'] ){
					// contremarque
					if( !$countermark_available ){
						$nom_is_unstock = true;
					}
				}elseif( $pc_info['stock'] >= ( $ch['qte'] * $ch['col_qte'] ) ){
					// disponible
				}elseif( $pc_info['stock_livr'] ){
					// délai réappro connu
					if( strtotime($pc_info['stock_livr']) > strtotime('+'.$days_available.' days') ){
						$nom_is_unstock = true;
					}
				}else{
					// rupture
					$nom_is_unstock = true;
				}

			}

			if( $nom_is_unstock ){
				$p['childs_detail'] = $childs_detail;
				$rows_unstock[] = $p;
			}else{
				$rows_instock_count++;
			}

		}elseif( prd_products_is_port( $p['ref'] ) ){
			// frais de port
			// pas de décompte "instock"
		}elseif( !$p_info['follow_stock'] ){
			// non suivi en stock
			$rows_instock_count++;
		}elseif( $p_info['countermark'] ){
			// contremarque
			if( !$countermark_available ){
				$rows_unstock[] = $p;
			}else{
				$rows_instock_count++;
			}
		}elseif( $p_info['stock'] >= ( $p['qte'] * $p['col_qte'] ) ){
			// disponible
			$rows_instock_count++;
		}elseif( $p_info['stock_livr'] ){
			// délai réappro connu
			if( strtotime($p_info['stock_livr']) > strtotime('+'.$days_available.' days') ){
				if( $split_partial && $p_info['stock'] >= $p['col_qte'] ){
					// partielle
					$p['qte_sto'] = floor($p_info['stock'] / $p['col_qte']);
					$p['qte_rel'] = $p['qte'] - $p['qte_sto'];
					$rows_partial[] = $p;
				}else{
					$rows_unstock[] = $p;
				}
			}else{
				$rows_instock_count++;
			}
		}else{
			// rupture
			if( $split_partial && $p_info['stock'] >= $p['col_qte'] ){
				// partielle
				$p['qte_sto'] = floor($p_info['stock'] / $p['col_qte']);
				$p['qte_rel'] = $p['qte'] - $p['qte_sto'];
				$rows_partial[] = $p;
			}else{
				$rows_unstock[] = $p;
			}
		}
	}

	// pas de commande reliquat à créer
	// note : s'il y a des lignes partielles, il y a forcément deux commandes
	$count_unstock = sizeof($rows_unstock);
	$count_partial = sizeof($rows_partial);
	if( !$count_partial && ( !$count_unstock || !$rows_instock_count ) ){
		return true;
	}

	// création de la commande reliquat
	$new_ord_id = ord_orders_add_sage( $ord['user'], $ord['date_en'], _STATE_BASKET, '', $ord['piece'], $ord['ref'], false, $ord['wst_id'] );

	if( !$new_ord_id ){
		return false;
	}

	if( $config['tnt_id'] == 22 ){

		// gestion de la livraison spécifique pour TPLC
		if( $tplc_adr_id ){
			ord_orders_adr_delivery_set( $new_ord_id, $tplc_adr_id );
		}elseif( $tplc_rly_id ){
			ord_orders_set_relay( $new_ord_id, $tplc_rly_id );
		}
		if( $tplc_srv_id ){
			ord_orders_set_dlv_service( $new_ord_id, $tplc_srv_id );
		}

	}else{

		// point-relais de livraison
		if( $ord['rly_id'] ){
			ord_orders_set_relay( $new_ord_id, $ord['rly_id'] );
		}
		// magasin de livraison
		if( $ord['str_id'] ){
			ord_orders_set_dlv_store( $new_ord_id, $ord['str_id'] );
		}
		// service de livraison
		if( $ord['srv_id'] ){
			ord_orders_set_dlv_service( $new_ord_id, $ord['srv_id'] );
		}
		// adresse de livraison
		if( $ord['adr_delivery'] ){
			ord_orders_adr_delivery_set( $new_ord_id, $ord['adr_delivery'] );
		}
		// consignes
		if( $ord['dlv-notes'] ){
			ord_orders_dlv_notes_set( $new_ord_id, $ord['dlv-notes'] );
		}

	}

	// représentant
	if( $ord['seller_id'] ){
		ord_orders_set_seller_id( $new_ord_id, $ord['seller_id'] );
	}

	// commentaires
	$libel_reliquat = 'Reliquat commande N° '.$ord_id;
	if( trim($ord['comments']) ){
		$ord['comments'] .= "\n".$libel_reliquat;
	}else{
		$ord['comments'] = $libel_reliquat;
	}

	ord_orders_comments_update( $new_ord_id, $ord['comments'] );

	// type de carte
	if( $ord['card_id'] ){
		ord_card_type_set( $new_ord_id, $ord['card_id'] );
	}
	// option cadeau
	if( $ord['opt-gift'] ){
		ord_orders_opt_gift_set( $new_ord_id, $ord['opt-gift'], $ord['opt-gift-message'] );
	}
	// signature
	if( $rsign = ord_orders_signature_get( $ord_id ) ){
		if( $sign = ria_mysql_fetch_assoc($rsign) ){
			ord_orders_signature_add( $new_ord_id, $sign['signature'], $sign['usr_id'], $sign['firstname'], $sign['lastname'], $sign['function'] );
		}
	}

	// traitement de chaque ligne à déplacer
	foreach( $rows_unstock as $row ){

		// mise à jour du "prd_ord_id" des lignes
		ria_mysql_query('
			update riashop.ord_products
			set prd_ord_id = '.$new_ord_id.'
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
			and prd_id = '.$row['id'].' and prd_line_id = '.$row['line'].'
		');

		// données complémentaires de la ligne
		{
			ria_mysql_query('
				update riashop.ord_products_promotions
				set opm_ord_id = '.$new_ord_id.'
				where opm_tnt_id = '.$config['tnt_id'].' and opm_ord_id = '.$ord_id.'
				and opm_prd_id = '.$row['id'].' and opm_line_id = '.$row['line'].'
			');

			ria_mysql_query('
				update riashop.fld_object_values
				set pv_obj_id_0 = '.$new_ord_id.'
				where pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = '.$ord_id.'
				and pv_obj_id_1 = '.$row['id'].' and pv_obj_id_2 = '.$row['line'].'
			');

			ria_mysql_query('
				update riashop.ord_subscriptions
				set ops_ord_id = '.$new_ord_id.'
				where ops_tnt_id = '.$config['tnt_id'].' and ops_ord_id = '.$ord_id.'
				and ops_prd_id = '.$row['id'].' and ops_line_id = '.$row['line'].'
			');
		}

		// mise à jour des composants de nomenclature
		if( isset($row['childs_detail']) ){
			foreach( $row['childs_detail'] as $chrow ){
				ria_mysql_query('
					update riashop.ord_products
					set prd_ord_id = '.$new_ord_id.'
					where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
					and prd_id = '.$chrow['id'].' and prd_line_id = '.$chrow['line'].'
				');

				// données complémentaires de la ligne nomenclaturée
				{
					ria_mysql_query('
						update riashop.ord_products_promotions
						set opm_ord_id = '.$new_ord_id.'
						where opm_tnt_id = '.$config['tnt_id'].' and opm_ord_id = '.$ord_id.'
						and opm_prd_id = '.$chrow['id'].' and opm_line_id = '.$chrow['line'].'
					');

					ria_mysql_query('
						update riashop.fld_object_values
						set pv_obj_id_0 = '.$new_ord_id.'
						where pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = '.$ord_id.'
						and pv_obj_id_1 = '.$chrow['id'].' and pv_obj_id_2 = '.$chrow['line'].'
					');

					ria_mysql_query('
						update riashop.ord_subscriptions
						set ops_ord_id = '.$new_ord_id.'
						where ops_tnt_id = '.$config['tnt_id'].' and ops_ord_id = '.$ord_id.'
						and ops_prd_id = '.$chrow['id'].' and ops_line_id = '.$chrow['line'].'
					');
				}
			}
		}

	}

	// division des lignes partielles en 2
	foreach( $rows_partial as $row ){

		// crée la ligne dans la commande reliquat avec la quantité restante
		$ok_new_row = ria_mysql_query('
			insert into riashop.ord_products (
				prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_date_created,
				prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_brd_id, prd_date_livr,
				prd_notes, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id
			)
			select
				prd_tnt_id, '.$new_ord_id.', prd_id, prd_line_id, '.$row['qte_rel'].', prd_ref, prd_name, now(),
				prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_brd_id, prd_date_livr,
				prd_notes, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id
			from
				riashop.ord_products
			where
				prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
				and prd_id = '.$row['id'].' and prd_line_id = '.$row['line'].'
		');

		if( $ok_new_row ){

			// met à jour la quantité dans la commande "stock"
			ria_mysql_query('
				update riashop.ord_products
				set prd_qte = '.$row['qte_sto'].'
				where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
				and prd_id = '.$row['id'].' and prd_line_id = '.$row['line'].'
			');

			// mise à jour des données complémentaires de la ligne
			{
				if( is_numeric($row['cod']) && $row['cod'] > 0 ){
					ria_mysql_query('
						insert into riashop.ord_products_promotions
							(opm_tnt_id, opm_ord_id, opm_prd_id, opm_line_id, opm_cod_id)
						select
							opm_tnt_id, '.$new_ord_id.', opm_prd_id, opm_line_id, opm_cod_id
						from
							riashop.ord_products_promotions
						where
							opm_tnt_id = '.$config['tnt_id'].' and opm_ord_id = '.$ord_id.'
							and opm_prd_id = '.$row['id'].' and opm_line_id = '.$row['line'].'
							and opm_cod_id = '.$row['cod'].'
					');
				}

				ria_mysql_query('
					insert into riashop.fld_object_values
						(pv_tnt_id, pv_obj_id_0, pv_obj_id_1, pv_obj_id_2, pv_lng_code, pv_fld_id, pv_value)
					select
						pv_tnt_id, '.$new_ord_id.', pv_obj_id_1, pv_obj_id_2, pv_lng_code, pv_fld_id, pv_value
					from
						riashop.fld_object_values
					where
						pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = '.$ord_id.'
						and pv_obj_id_1 = '.$row['id'].' and pv_obj_id_2 = '.$row['line'].'
				');

				ria_mysql_query('
					insert into riashop.ord_subscriptions (
						ops_tnt_id, ops_ord_id, ops_prd_id, ops_line_id, ops_sub_id,
						ops_period_type, ops_period, ops_length_type, ops_length, ops_date_start, ops_is_free
					)
					select
						ops_tnt_id, '.$new_ord_id.', ops_prd_id, ops_line_id, ops_sub_id,
						ops_period_type, ops_period, ops_length_type, ops_length, ops_date_start, ops_is_free
					from
						riashop.ord_subscriptions
					where
						ops_tnt_id = '.$config['tnt_id'].' and ops_ord_id = '.$ord_id.'
						and ops_prd_id = '.$row['id'].' and ops_line_id = '.$row['line'].'
				');
			}

		}else{
			error_log('[ord_orders_split_by_availability] échec de ord_products_add_sage( '.$new_ord_id.', '.$row['id'].', '.$row['line'].', '.$row['ref'].', '.$row['name'].', '.$row['qte_rel'].', '.$row['price_ht'].', '.$row['tva_rate'].', '.$row['date_livr_en'].', null, '.$row['ecotaxe'].' )');
		}

	}

	if( $config['tnt_id'] == 22 ){

		$tplc_port_ref = 'PORT';

		// ajout de la ligne de frais de port
		if( $tplc_port_amount > 0 ){

			ord_products_add_free( $new_ord_id, $tplc_port_ref, 'Frais de port', $tplc_port_amount );

			// on déduit le montant dans la ligne de frais de port originale
			ria_mysql_query('
				update riashop.ord_products
				set prd_price_ht = prd_price_ht - '.$tplc_port_amount.'
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_ord_id = '.$ord_id.'
					and prd_ref = "'.addslashes($tplc_port_ref).'"
			');

		}

		// force l'application d'un code promotion en % sur la 2éme commande
		if( !empty($tplc_promotions_codes_to_apply_forced) ){
			foreach ($tplc_promotions_codes_to_apply_forced as $promotion_code){
				pmt_codes_apply_forced( $promotion_code, $new_ord_id );
			}
		}

		// recalcul des totaux sur les deux commandes
		ord_orders_update_totals( $ord_id );
		ord_orders_update_totals( $new_ord_id );

		// répartition d'un code promotion en valeur sur les deux commandes
		if( !empty($tplc_amount_of_pmt) ){

			/**	On charge le total de la commande 1 sans la remise appliquée
			 *	On charge le montant cette fois avec la remise appliquée
			 *	On compare les deux montants avec le montant théorique de la remise du code promotion
			 *	S'il reste un montant > 0 à appliquer, il est appliquer sur la 2ème commande
			 */

			$total_without_disc = ord_orders_get_total( $ord_id, false, false, array($tplc_port_ref) ) + ord_orders_port_get( $ord_id );
			$disc_amount = $total_without_disc - ord_orders_get_total( $ord_id );

			$error = false;
			foreach ($tplc_amount_of_pmt as $promotion){
				$disc_gap_amount = $promotion['discount'] - $disc_amount;
				if( $disc_gap_amount > 0 ){

					// création d'une copie du code promotion, spécifique au client en cours, pour un montant égal à l'écart de remise
					$new_pmt_code = pmt_codes_copy( $promotion['id'], $ord['user'], $disc_gap_amount, true );
					if( trim($new_pmt_code) ){
						// application à la seconde commande et recalcule de son total
						pmt_codes_apply_forced( $new_pmt_code, $new_ord_id );

					}else{
						$error = true;
						error_log('Subdivision de commande TPLC : échec de création du deuxième code promotion pour le montant restant.');
					}

				}

			}
			if( !$error) {
				ord_orders_update_totals( $new_ord_id );
			}

		}

	}else{
		// recalcul des totaux sur les deux commandes
		ord_orders_update_totals( $ord_id );
		ord_orders_update_totals( $new_ord_id );
	}

	// mise à jour du moyen de paiement avant de valider
	ord_orders_pay_type_set( $new_ord_id, $ord['pay_id'] );

	// valide la commande
	ord_orders_state_update( $new_ord_id, _STATE_WAIT_PAY, '', false );

	// passe la nouvelle commande au même état que celle de base
	if( $ord['state_id'] != _STATE_WAIT_PAY ){
		ord_orders_state_update( $new_ord_id, $ord['state_id'], '', false );
	}

	return $new_ord_id;

}
// \endcond

// \cond onlyria
/**	Cette fonction subdivise une commande en plusieurs en suivant la valeur d'un champs avancé
 *  Attention la fonction à été faite pour colmarfrais à la base, il est possible qu'elle ne fonctionne pas correctement pour certaine donnée comme les promos
 * 	Il est conseillé d'utiliser un masquage de la commande en amont avant le traitement.
 *
 *	@param int $ord_id Obligatoire, identifiant de la commande à subdiviser.
 *	@param int $fld_id Obligatoire, identifiant du champs permettant la découpe
 *
 *	@return bool True si l'opération a réussi
 *	@return bool False en cas d'échec.
 */
function ord_orders_split_by_fields( $ord_id, $fld_id ){

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}
	if( !is_numeric($fld_id) || $fld_id <= 0 ){
		return false;
	}

	global $config;

	$split_tnt_activated = array(41);

	if( !in_array($config['tnt_id'], $split_tnt_activated) ){
		error_log('sécurité ord_orders_split_by_fields() : pas de split possible pour ce client.');
		return true;
	}

	// charge la commande
	$rord = ord_orders_get( 0, $ord_id, 0, 0, null, false, false, false, false, false, false, '', false, false, false, false, false, false, false, false, true );
	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}
	$ord = ria_mysql_fetch_assoc($rord);

	if( $ord['pmt_id'] ){
		return false;
	}

	$products = ord_products_get( $ord_id );

	if( !$products ){
		return false;
	}

	if( !ria_mysql_num_rows($products) ){
		return true;
	}

	// tableau des lignes suivant la valeur du champs
	$rows_fld = array();
	$has_empty_field = false;

	while( $p = ria_mysql_fetch_assoc($products) ){

		// récupère la valeur pour ce produit
		$val = fld_object_values_get( $p['id'], $fld_id );
		if( trim($val) ){
			if( !isset($rows_fld[ strtoupper(trim($val)) ]) ){
				$rows_fld[ strtoupper(trim($val)) ] = array();
			}
			$rows_fld[ strtoupper(trim($val)) ][] = $p;
		}else{
			$has_empty_field = true;
		}

	}

	$keys = array_keys($rows_fld);
	$keys = array_unique($keys);

	// dans le cas ou je n'ai pas de produit sans valeur et que j'ai qu'une valeur par de coupure de la commande
	if( !$has_empty_field && sizeof( $keys ) == 1 ){
		return true;
	}

	$cpt = 1;
	foreach( $keys as $key ){

		$ref = $key;

		if( $config['tnt_id'] == 41 ){
			if( !in_array($ref, array('POISSON','GLACE')) ){
				$ref = "";
			}
		}

		// pas de création de commande pour le dernier champs si tous les produits on un champs
		if( !$has_empty_field && $cpt == sizeof($keys) ){
			ord_orders_ref_update($ord['id'], $ref);
			break;
		}

		$cpt++;

		// création de la commande
		$new_ord_id = ord_orders_add_sage( $ord['user'], $ord['date_en'], _STATE_BASKET, '', $ord['piece'], $ref, false, $ord['wst_id'] );

		if( !$new_ord_id ){
			return false;
		}

		// point-relais de livraison
		if( $ord['rly_id'] ){
			ord_orders_set_relay( $new_ord_id, $ord['rly_id'] );
		}
		// magasin de livraison
		if( $ord['str_id'] ){
			ord_orders_set_dlv_store( $new_ord_id, $ord['str_id'] );
		}
		// service de livraison
		if( $ord['srv_id'] ){
			ord_orders_set_dlv_service( $new_ord_id, $ord['srv_id'] );
		}
		// adresse de livraison
		if( $ord['adr_delivery'] ){
			ord_orders_adr_delivery_set( $new_ord_id, $ord['adr_delivery'] );
		}
		// adresse de facturation
		if( $ord['adr_invoices'] ){
			ord_orders_adr_ord_adr_invoice_set( $new_ord_id, $ord['adr_invoices'] );
		}
		// consignes
		if( $ord['dlv-notes'] ){
			ord_orders_dlv_notes_set( $new_ord_id, $ord['dlv-notes'] );
		}
		// représentant
		if( $ord['seller_id'] ){
			ord_orders_set_seller_id( $new_ord_id, $ord['seller_id'] );
		}
		// date de livraison
		if( $ord['ord_livr'] ){
			ord_orders_set_date_livr( $new_ord_id, $ord['ord_livr'] );
		}
		// dépot de la commande
		if( $ord['dps_id'] ){
			ord_orders_set_deposit( $new_ord_id, $ord['dps_id'] );
		}
		// payement
		if( $ord['pay_id'] ){
			ord_orders_pay_type_set( $new_ord_id, $ord['pay_id'] );
		}
		// signature
		if( $rsign = ord_orders_signature_get( $ord_id ) ){
			if( $sign = ria_mysql_fetch_assoc($rsign) ){
				ord_orders_signature_add( $new_ord_id, $sign['signature'], $sign['usr_id'], $sign['firstname'], $sign['lastname'], $sign['function'] );
			}
		}

		// traitement pour la ligne
		foreach( $rows_fld[$key] as $row ){

			ria_mysql_query('
				update riashop.ord_products
				set prd_ord_id = '.$new_ord_id.'
				where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
				and prd_id = '.$row['id'].' and prd_line_id = '.$row['line'].'
			');

			// données complémentaires de la ligne
			{
				ria_mysql_query('
					update riashop.ord_products_promotions
					set opm_ord_id = '.$new_ord_id.'
					where opm_tnt_id = '.$config['tnt_id'].' and opm_ord_id = '.$ord_id.'
					and opm_prd_id = '.$row['id'].' and opm_line_id = '.$row['line'].'
				');

				ria_mysql_query('
					update riashop.fld_object_values
					set pv_obj_id_0 = '.$new_ord_id.'
					where pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = '.$ord_id.'
					and pv_obj_id_1 = '.$row['id'].' and pv_obj_id_2 = '.$row['line'].'
				');

				ria_mysql_query('
					update riashop.ord_subscriptions
					set ops_ord_id = '.$new_ord_id.'
					where ops_tnt_id = '.$config['tnt_id'].' and ops_ord_id = '.$ord_id.'
					and ops_prd_id = '.$row['id'].' and ops_line_id = '.$row['line'].'
				');

				// todo : statuts à la ligne de commandes
			}

		}

		// recalcul des totaux sur les deux commandes
		ord_orders_update_totals( $new_ord_id );
		ord_orders_update_totals( $ord_id );

		// valide la commande
		ord_orders_state_update( $new_ord_id, _STATE_WAIT_PAY, '', false );

		// passe la nouvelle commande au même état que celle de base
		if( $ord['state_id'] != _STATE_WAIT_PAY ){
			ord_orders_state_update( $new_ord_id, $ord['state_id'], '', false );
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de sauvegarder le changement de status d'une commande.
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $state_id Obligatoire, identifiant du statut
 *	@param int $usr_id Facultatif, identifiant du compte qui à déclencher le changement de status
 *	@return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
 */
function ord_orders_states_add( $ord_id, $state_id, $usr_id=0 ){
	global $config;

	if( !ord_orders_exists($ord_id) ){
		return false;
	}

	if( !is_numeric($usr_id) ){
		return false;
	}

	if( !ord_states_exists($state_id) ){
		return false;
	}

	$res = ria_mysql_query('
		insert into riashop.ord_orders_states
			( oos_tnt_id, oos_ord_id, oos_state_id, oos_datetime, oos_usr_id )
		values
			( '.$config['tnt_id'].', '.$ord_id.', '.$state_id.', now(), '.($usr_id > 0 ? $usr_id:'null').' )
	');

	if( $res ){
		// Envoi d'une notification mail pour prévenir de l'arrivé d'un panier, d'un devis ou d'une commande
		if( in_array($state_id, [_STATE_BASKET, _STATE_DEVIS, _STATE_WAIT_PAY, _STATE_PAY_CONFIRM]) ){
			$r_ord = ord_orders_get_simple( ['id' => $ord_id] );
			if( $r_ord && ria_mysql_num_rows($r_ord) ){
				$ord = ria_mysql_fetch_assoc( $r_ord );
				flow_notifications_send( CLS_ORDER, dev_devices_get_object_simplified(CLS_ORDER, array($ord['id']), $ord) );
			}
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère l'historique des status d'une commande.
 *	@param int $ord_id Optionnel, identifiant d'une commande
 *	@param $state_id Optionnel, identifiant d'un état de commande
 *	@param $sort Optionnel, paramètre de tri
 *	@param $period Optionnel, tableau contenant une date (inclus) de début et/ou de fin à prendre en compte, ex. array('start'=>'', 'end'=>'');
 *	@return resource Un résultat MySQL contenant :
 *					- ord_id : identifiant de la commande
 *					- state_id : identifiant du statut
 *					- state_name : nom du statut
 *					- date_en : date du changement au format EN
 *					- date : date du changement au format FR
 *					- usr_id : identifiant de la personne qui est intervenu sur le changement de l'état
 */
function ord_orders_states_get( $ord_id=0, $state_id=0, $sort=false, $period=array() ){
	if( !is_numeric($ord_id) || $ord_id<0 ){
		return false;
	}

	$state_id = control_array_integer($state_id, false);
	if ($state_id === false) {
		return false;
	}

	global $config;

	$sql = '
		select
			oos_ord_id as ord_id, state_id, ifnull(stn_name, state_name) as "state_name", oos_datetime as date_en,
			date_format(oos_datetime,"%d/%m/%Y à %H:%i:%s") as date, ifnull(oos_usr_id, ord_usr_id) as usr_id, oos_usr_id as raw_usr_id
		from riashop.ord_orders_states
			join riashop.ord_states on oos_state_id=state_id
			left join riashop.ord_orders on oos_ord_id=ord_id and oos_tnt_id=ord_tnt_id
			left join riashop.ord_states_name on oos_state_id=stn_stt_id and oos_tnt_id=stn_tnt_id and ifnull(ord_wst_id, '.$config['wst_id'].')=stn_wst_id
		where oos_tnt_id='.$config['tnt_id'].'
	';

	if ($ord_id) {
		$sql .= ' and oos_ord_id='.$ord_id;
	}

	if (count($state_id)) {
		$sql .= ' and oos_state_id in ('.implode(', ', $state_id).')';
	}

	if (is_array($period) && count($period)) {
		if (array_key_exists('start', $period)) {
			$date_start = dateheureparse($period['start']);
			if (isdateheure($date_start)) {
				$sql .= ' and oos_datetime >= "'.$date_start.'"';
			}
		}
		if (array_key_exists('end', $period)) {
			$date_end = dateheureparse($period['end']);
			if (isdateheure($date_end)) {
				$sql .= ' and oos_datetime <= "'.$date_end.'"';
			}
		}
	}

	// Converti le paramètre de tri en SQL
	if( is_array($sort) ){
		$sort_final = array();
		foreach( $sort as $col => $dir ){
			$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
			switch( strtolower(trim($col)) ){
				case 'ord_id':
					$sort_final[] = '`ord_id` ' . $dir;
					break;
				case 'state_id':
					$sort_final[] = '`state_id` ' . $dir;
					break;
				case 'state_name':
					$sort_final[] = '`state_name` ' . $dir;
					break;
				case 'datetime':
					$sort_final[] = '`date_en` ' . $dir;
					break;
				case 'usr_id':
					$sort_final[] = '`usr_id` ' . $dir;
					break;
			}
		}

		if( sizeof($sort_final) ){
			$sql .= ' order by '.implode(', ', $sort_final);
		}
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction charge un tableau contenant les identifiants de status et qui en ai l'auteur pour une commande
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $only_author Optionnel, par défaut tous les status sont mis dans le tableau, mettre true pour n'avoir que les statuts dont l'auteur est connu
 *	@param $dates Optionnel, tableau pouvant contenir une date de début (key : 'start') et / ou une date de fin (key : 'end')
 *	@return Un table clé correspondant au statut et la valeur correspondant à l'identifiant du compte auteur
 */
function ord_orders_states_get_array( $ord_id, $only_author=false, $dates=array() ){
	$ar_states = array();

	if (!is_numeric($ord_id) || $ord_id <= 0) {
		return $ar_states;
	}

	if (array_key_exists('start', $dates)) {
		$dates['start'] = dateheureparse($dates['start']);

		if (!isdateheure($dates['start'])) {
			return false;
		}
	}

	if (array_key_exists('end', $dates)) {
		$dates['end'] = dateheureparse($dates['end']);

		if (!isdateheure($dates['end'])) {
			return false;
		}
	}

	global $config;

	$sql = '
		select oos_state_id, oos_usr_id
		from riashop.ord_orders_states
		where oos_tnt_id = '.$config['tnt_id'].'
			and oos_ord_id = '.$ord_id.'
	';

	if ($only_author) {
		$sql .= ' and ifnull(oos_usr_id, 0) != 0';
	}

	if (array_key_exists('start', $dates)) {
		$sql .= ' and oos_datetime >= "'.$dates['start'].'"';
	}

	if (array_key_exists('end', $dates)) {
		$sql .= ' and oos_datetime <= "'.$dates['end'].'"';
	}

	$res = ria_mysql_query($sql);
	if (!$res) {
		return $ar_states;
	}

	while ($r = ria_mysql_fetch_assoc($res)) {
		$ar_states[$r['oos_state_id']] = $r['oos_usr_id'];
	}

	return $ar_states;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les commandes synchronisées qui ont été annulées et remplacées
 *	@param $delay_min Nombre de jours minimal depuis la création de la commande (permet d'éviter les traitements sur des commandes trop récentes)
 *	@param $get_array Si activé le résultat est un tableau, sinon un jeu de données MySQL
 *	@return bool False en cas d'échec
 *	@return resource Un résultat MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la commande
 */
function ord_orders_get_sync_replaced( $delay_min, $get_array ){

	if( !is_numeric($delay_min) || $delay_min <= 0 ){
		return false;
	}

	global $config;

	$r = ria_mysql_query('
		select
			distinct o.ord_id as "id"
		from
			riashop.ord_orders as o
			join riashop.ord_orders as o2 on o.ord_tnt_id = o2.ord_tnt_id and o.ord_piece = o2.ord_piece
		where
			o.ord_tnt_id = '.$config['tnt_id'].'
			and o.ord_piece != ""
			and o.ord_state_id = '._STATE_CANCEL_MERCHAND.'
			and datediff(now(), o.ord_date) > '.$delay_min.'
			and o2.ord_state_id != '._STATE_CANCEL_MERCHAND.'
			and o.ord_parent_id is null
			and o2.ord_parent_id is null
	');

	if( !$r ){
		return false;
	}

	if( !$get_array ){
		return $r;
	}

	$ord_ar = array();

	while( $ord = ria_mysql_fetch_assoc($r) ){
		$ord_ar[] = $ord['id'];
	}

	return $ord_ar;

}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si une commande donnée est l'enfant d'une autre commande
 *	@param int $ord_id Identifiant de la commande à tester
 *	@param $reverse Optionnel : si activé, teste si la commande est parent
 *	@return bool True si la commande spécifiée est un enfant, False sinon
 */
function ord_orders_is_child( $ord_id, $reverse = false ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	$table = $reverse ? 'o_parent' : 'o_child';

	$r = ria_mysql_query('
		select count(*)
		from riashop.ord_orders as o_parent
		join riashop.ord_orders as o_child on o_parent.ord_id = o_child.ord_parent_id and o_parent.ord_tnt_id = o_child.ord_tnt_id
		where '.$table.'.ord_id = '.$ord_id.' and '.$table.'.ord_tnt_id = '.$config['tnt_id'].'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0) > 0;

}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si une commande donnée est le parent d'une ou plusieurs commandes
 *	@param int $ord_id Identifiant de la commande à tester
 *	@return bool True si la commande spécifiée est un parent, False sinon
 */
function ord_orders_is_parent( $ord_id ){
	return ord_orders_is_child( $ord_id, true );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de savoir si une commande est masquée.
 *	@param int $ord_id Identifiant de la commande à tester
 *	@return bool True Si la commande est masquée, False dans le cas contraire
 */
function ord_orders_is_masked( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
			and ord_masked = 1
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de créer une commande enfant à la commande actuellement en session
 *	@param $params Optionnel, tableau associatif de paramètres qui remplaceront les valeurs par défaut (celles-ci étant récupérées depusi la commande parent).
 *		La liste des paramètres personnalisables est la suivante :
 *			- inv_id : identifiant de l'adresse de facturation (doit appartenir au client / usr_id)
 *			- dlv_id : identifiant de l'adresse de livraison (doit appartenir au client / usr_id)
 *			- ref : référence de la commande
 *			- date_livr_en : date de livraison estimée de la commande
 *			- srv_id : identifiant du service de livraison
 *			- str_id : identifiant du magasin
 *			- dlv-notes : consignes de livraison
 *			- opt-gift : option cadeau activée oui / non
 *			- opt-gift-msg : message de l'option cadeau
 *			- comments : commentaires sur la commande
 *			- rly_id : identifiant d'un point-relais
 *	@return int L'identifiant de la commande enfant, False en cas d'échec
 */
function ord_orders_add_child( $params = array() ){
	global $config;
	if( !isset($_SESSION['ord_id']) || !is_numeric($_SESSION['ord_id']) || $_SESSION['ord_id'] <= 0 ){
		error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' pas de session ord_id');
		return false;
	}

	if( $_SESSION['ord_id']==1653071 ){ mail('<EMAIL>', 'ord_orders_add_child', ''); }
	// charge la commande parent
	$base = ord_orders_get_with_adresses( 0, $_SESSION['ord_id'] );
	if( !$base || !ria_mysql_num_rows($base) ){
		error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' impossible de récupérer la commande');
		return false;
	}
	$base = ria_mysql_fetch_assoc($base);

	if( !is_array($params) ){
		$params = array();
	}

	// vérification des paramètres
	{
		// adresse de facturation
		if( array_key_exists('inv_id', $params) ){
			if( !is_numeric($params['inv_id']) || $params['inv_id'] <= 0 ){
				error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' adresse de facturation incorrect');
				return false;
			}elseif( !( $radr = gu_adresses_get( $base['user'], $params['inv_id'] ) ) ){
				error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' impossible de récupérer l\'adresse de facturation');
				return false;
			}elseif( !ria_mysql_num_rows($radr) ){
				error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' aucune adresse de facturation');
				return false;
			}
		}

		// date de livraison estimée
		if( array_key_exists('date_livr_en', $params) ){
			if( $params['date_livr_en'] !== null && !isdateheure($params['date_livr_en']) ){
				error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' date de livraison invalide');
				return false;
			}
		}

		// service de livraison
		if( array_key_exists('srv_id', $params) ){
			if( $params['srv_id'] !== null && !dlv_services_exists( $params['srv_id'] ) ){
				error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' service de livraison invalide');
				return false;
			}
		}

		// propriétés de livraison de la commande parent
		$have_adr_id = is_numeric($base['dlv_id']) && $base['dlv_id'] > 0;
		$have_str_id = is_numeric($base['str_id']) && $base['str_id'] > 0;
		$have_rly_id = is_numeric($base['rly_id']) && $base['rly_id'] > 0;

		// adresse de livraison
		if( array_key_exists('dlv_id', $params) ){
			if( $params['dlv_id'] !== null ){
				if( !is_numeric($params['dlv_id']) || $params['dlv_id'] <= 0 ){
					error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' adresse de livraison incorrect');
					return false;
				}elseif( !( $radr = gu_adresses_get( $base['user'], $params['dlv_id'] ) ) ){
					error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' impossible de récupérer l\'adresse de livraison');
					return false;
				}elseif( !ria_mysql_num_rows($radr) ){
					error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' aucune adresse de livraison');
					return false;
				}
				$have_adr_id = true;
			}else{
				$have_adr_id = false;
			}
		}

		// magasin de livraison
		if( array_key_exists('str_id', $params) ){
			if( $params['str_id'] !== null ){
				if( !dlv_stores_exists( $params['str_id'] ) ){
					error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' magasin invalide');
					return false;
				}
				$have_str_id = true;
			}else{
				$have_str_id = false;
			}
		}

		// point-relais de livraison
		if( array_key_exists('rly_id', $params) ){
			if( $params['rly_id'] !== null ){
				if( !dlv_relays_exists( $params['rly_id'] ) ){
					error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' point relais invalide');
					return false;
				}
				$have_rly_id = true;
			}else{
				$have_rly_id = false;
			}
		}

		if( ( $have_adr_id && $have_str_id ) || ( $have_str_id && $have_rly_id ) || ( $have_adr_id && $have_rly_id ) ){
			error_log('ord_orders_add_child:'.__FILE__.':'.__LINE__.' deux adresses de livraison sur la même commande');
			return false;
		}
	}

	global $config;

	$fields = array( 'ord_tnt_id', 'ord_parent_id', 'ord_usr_id', 'ord_date', 'ord_state_id', 'ord_wst_id' );
	$values = array( $config['tnt_id'], $_SESSION['ord_id'], (is_numeric($base['user']) && $base['user'] > 0 ? $base['user'] : 'null'), '"'.$base['date_en'].'"', $base['state_id'], $base['wst_id'] );

	// adresse de facturation
	{
		$fields[] = 'ord_adr_invoices';
		if( array_key_exists('inv_id', $params) ){
			if( $params['inv_id'] !== null ){
				$values[] = $params['inv_id'];
			}else{
				$values[] = 'NULL';
			}
		}elseif( $base['inv_id'] ){
			$values[] = $base['inv_id'];
		}else{
			$values[] = 'NULL';
		}
	}

	// adresse de livraison
	{
		$fields[] = 'ord_adr_delivery';
		if( array_key_exists('dlv_id', $params) ){
			if( $params['dlv_id'] !== null ){
				$values[] = $params['dlv_id'];
			}else{
				$values[] = 'NULL';
			}
		}elseif( $base['dlv_id'] ){
			$values[] = $base['dlv_id'];
		}else{
			$values[] = 'NULL';
		}
	}

	// référence de la commande
	{
		$fields[] = 'ord_ref';
		if( array_key_exists('ref', $params) ){
			$values[] = '"'.addslashes($params['ref']).'"';
		}else{
			$values[] = '"'.addslashes($base['ref']).'"';
		}
	}

	// date de livraison estimée
	{
		$fields[] = 'ord_date_livr';
		if( array_key_exists('date_livr_en', $params) ){
			if( $params['date_livr_en'] === null ){
				$values[] = 'NULL';
			}else{
				$values[] = '"'.dateheureparse($params['date_livr_en']).'"';
			}
		}elseif( $base['date_livr_en'] ){
			$values[] = '"'.dateheureparse($base['date_livr_en']).'"';
		}else{
			$values[] = 'NULL';
		}
	}

	// service de livraison
	{
		$fields[] = 'ord_srv_id';
		if( array_key_exists('srv_id', $params) ){
			if( $params['srv_id'] === null ){
				$values[] = 'NULL';
			}else{
				$values[] = $params['srv_id'];
			}
		}elseif( $base['srv_id'] ){
			$values[] = $base['srv_id'];
		}else{
			$values[] = 'NULL';
		}
	}

	// magasin
	{
		$fields[] = 'ord_str_id';
		if( array_key_exists('str_id', $params) ){
			if( $params['str_id'] === null ){
				$values[] = 'NULL';
			}else{
				$values[] = $params['str_id'];
			}
		}elseif( $base['str_id'] ){
			$values[] = $base['str_id'];
		}else{
			$values[] = 'NULL';
		}
	}

	// consignes de livraison
	{
		$fields[] = 'ord_dlv_notes';
		if( array_key_exists('dlv-notes', $params) ){
			$values[] = '"'.addslashes($params['dlv-notes']).'"';
		}else{
			$values[] = '"'.addslashes($base['dlv-notes']).'"';
		}
	}

	// option cadeau
	{
		$fields[] = 'ord_opt_gift';
		if( array_key_exists('opt-gift', $params) ){
			$values[] = $params['opt-gift'] ? '1' : '0';
		}else{
			$values[] = $base['opt-gift'] ? '1' : '0';
		}

		$fields[] = 'ord_opt_gift_message';
		if( array_key_exists('opt-gift-msg', $params) ){
			$values[] = '"'.addslashes($params['opt-gift-msg']).'"';
		}else{
			$values[] = '"'.addslashes($base['opt-gift-msg']).'"';
		}
	}

	// commentaires
	{
		$fields[] = 'ord_comments';
		if( array_key_exists('comments', $params) ){
			$values[] = '"'.addslashes($params['comments']).'"';
		}else{
			$values[] = '"'.addslashes($base['comments']).'"';
		}
	}

	// point-relais
	{
		$fields[] = 'ord_rly_id';
		if( array_key_exists('rly_id', $params) ){
			if( $params['rly_id'] === null ){
				$values[] = 'NULL';
			}else{
				$values[] = $params['rly_id'];
			}
		}elseif( $base['rly_id'] ){
			$values[] = $base['rly_id'];
		}else{
			$values[] = 'NULL';
		}
	}

	// code promo
	{
		$promotions_to_add = array();
		$fields[] = 'ord_pmt_id';
		if( array_key_exists('pmt_id', $params) ){
			if( $params['pmt_id'] === null ){
				$values[] = 'NULL';
			}else{
				$values[] = $params['pmt_id'];
				$promotions_to_add[] = $params['pmt_id'];
			}
		}elseif( $base['pmt_id'] ){
			$values[] = $base['pmt_id'];
			// Si la base est utilisé, les promotions de la base sont copiés pour la commande enfant
			$promotions_to_add = ord_orders_promotions_get($base['id']);
		}else{
			$values[] = 'NULL';
		}
	}

	$sql = 'insert into riashop.ord_orders ('.implode(', ', $fields).') values ('.implode(', ', $values).')';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$order_child_id = ria_mysql_insert_id();

	// Si il y a des promotion a ajouter
	if( !empty($promotions_to_add) ){
		foreach($promotions_to_add as $order_promo){
			ord_orders_promotions_add($order_child_id, $order_promo['pmt_id'], $order_promo['pmt_type']);
		}
	}

	return $order_child_id;
}
// \endcond

// \cond onlyria
/**	Cette fonction est un alias des fonctions ord_orders_get() et ord_orders_get_with_adresses() pour les commandes enfants.
 *	@param int $parent_id Obligatoire, identifiant de la commande parent.
 *	@param bool $with_addresss Optionnel, détermine quel est la méthode sous-jacente utilisée : par défaut, il s'agit de ord_orders_get() ; spécifier True pour utiliser ord_orders_get_with_adresses().
 *	@param $params Optionel, tableau associatif de paramètres complémentaires pour la fonction sous-jacente. Quelques paramètres fonctionnels :
 *		user, state, limit, sort, is_web, wst_id, piece, wst, date_start, date_end
 *	@return bool False en cas d'échec.
 *	@return Le résultat ord_orders_get() ou ord_orders_get_with_adresses() des commandes enfants de la commande spécifiée.
 */
function ord_orders_get_childs( $parent_id, $with_addresss = false, $params = array() ){

	if( !is_numeric($parent_id) || $parent_id <= 0 ){
		return false;
	}

	if( !is_array($params) ){
		$params = array();
	}

	if( !isset($params['user']) ){
		$params['user'] = 0;
	}
	if( !isset($params['state']) ){
		$params['state'] = 0;
	}

	if( $with_addresss ){

		if( !isset($params['piece']) ){
			$params['piece'] = '';
		}
		if( !isset($params['wst']) ){
			$params['wst'] = 0;
		}
		if( !isset($params['date_start']) ){
			$params['date_start'] = false;
		}
		if( !isset($params['date_end']) ){
			$params['date_end'] = false;
		}

		return ord_orders_get_with_adresses( $params['user'], 0, $params['state'], $params['piece'], $params['date_start'], $params['date_end'], false, false, null, false, false, false, false, 0, $params['wst'], false, false, $parent_id );
	}

	if( !isset($params['limit']) ){
		$params['limit'] = 0;
	}
	if( !isset($params['sort']) ){
		$params['sort'] = false;
	}
	if( !isset($params['is_web']) ){
		$params['is_web'] = false;
	}
	if( !isset($params['wst_id']) ){
		$params['wst_id'] = false;
	}

	return ord_orders_get( $params['user'], 0, $params['state'], $params['limit'], null, $params['sort'], false, false, false, false, false, '', $params['is_web'], false, false, $params['wst_id'], false, false, false, false, false, $parent_id );

}
// \endcond

/** Cette fonction permet de calculer le coût d'une commande en points de fidélité.
 *	@param int $ord_id Obligatoire, Identifiant de la commande auquel on souhaite connaitre le prix.
 *	@param int $prd_id Obligatoire, Identifiant du produit concerné.
 *	@return int Nombre de points de fidélité
 */
function ord_orders_sum_points($ord_id, $prd_id=0){

	if (!is_numeric($ord_id) || $ord_id <= 0) {
		return false;
	}

	if (!is_numeric($prd_id) || $prd_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select sum(prd_sell_points * prd_qte) as total
		from ord_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '. $ord_id .'
	';
	if($prd_id > 0){
		$sql .=	'and prd_id = '.$prd_id;
	}

	$res = ria_mysql_query($sql);
	if($res && !ria_mysql_num_rows($res)){
		return false;
	}

	return ria_mysql_result( $res, 0, 0 );
}

// \cond onlyria
/**	Cette fonction supprime une ou des commandes enfants d'une commande parent. Les lignes associées repassent sur la commande de base.
 *	Elle ne fonctionne que sur des paniers.
 *	@param int $parent_id Obligatoire, identifiant de la commande parent.
 *	@param $child_id Optionnel, identifiant de la commande enfant. Si le paramètre n'est pas spécifié, tous les enfants seront retirés.
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_del_childs( $parent_id, $child_id = 0 ){

	// charge le parent
	if( !is_numeric($parent_id) || $parent_id <= 0 ){
		return false;
	}
	$rbase = ord_orders_get( 0, $parent_id );
	if( !$rbase || !ria_mysql_num_rows($rbase) ){
		return false;
	}
	$base = ria_mysql_fetch_assoc($rbase);

	// le parent doit être un panier
	if( !in_array($base['state_id'], ord_states_get_uncompleted()) ){
		return false;
	}

	global $config;

	// mise à jour des lignes de la commande (RAZ)
	$sql = '
		update riashop.ord_products
		set prd_ord_child_id = NULL
		where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$parent_id.'
	';
	if( is_numeric($child_id) && $child_id > 0 ){
		$sql .= ' and prd_ord_child_id = '.$child_id;
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	// suppression de la / les commande(s) enfant(s)
	$sql = '
		delete from riashop.ord_orders
		where ord_tnt_id = '.$config['tnt_id'].' and ord_parent_id = '.$parent_id.'
	';
	if( is_numeric($child_id) && $child_id > 0 ){
		$sql .= ' and ord_id = '.$child_id;
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de détacher une commande enfant de sa commande parente.
 *	Attention : aucune autre action est réalisé, les informations nécessaire à son traitement doivent être renseignées en amont.
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@return bool True si le détachement s'est correctement déroulé, False dans le cas contraire
 */
function ord_orders_child_dettach( $ord_id ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	$parent_id = 0;

	$r_parent = ria_mysql_query('select ord_parent_id from riashop.ord_orders where ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$ord_id);
	if( $r_parent && ria_mysql_num_rows($r_parent) ){
		$parent = ria_mysql_fetch_assoc( $r_parent );

		if( is_numeric($parent['ord_parent_id']) && $parent['ord_parent_id'] > 0 ){
			$parent_id = $parent['ord_parent_id'];
		}
	}

	if( $parent_id > 0 ){
		$res = ria_mysql_query('
			update riashop.ord_products
			set prd_ord_id = '.$ord_id.',
				prd_ord_child_id = null
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_ord_child_id = '.$ord_id.'
		');

		if( !$res ){
			return false;
		}

		$res = ria_mysql_query('
			update riashop.ord_orders
			set ord_parent_id = null
			where ord_tnt_id = '.$config['tnt_id'].'
				and ord_id = '.$ord_id.'
		');

		if( !$res ){
			return false;
		}

		ord_orders_update_totals( $parent_id );
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de rattacher une commande à une commande principale.
 *	Attention : aucune autre action est réalisé, les informations nécessaire à son traitement doivent être renseignées en amont.
 *	@param int $parent_id Obligatoire, identifiant de la commande parente
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@return bool True si le rattachement s'est correctement déroulé, False dans le cas contraire
 */
function ord_orders_child_attach( $parent_id, $ord_id ){
	if( !is_numeric($parent_id) || $parent_id<=0 ){
		return false;
	}

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	global $config;


	$res = ria_mysql_query('
		update riashop.ord_products
		set prd_ord_id = '.$parent_id.',
			prd_ord_child_id = '.$ord_id.'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '.$ord_id.'
	');

	if( !$res ){
		return false;
	}

	$res = ria_mysql_query('
		update riashop.ord_orders
		set ord_parent_id = '.$parent_id.'
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( !$res ){
		return false;
	}

	ord_orders_update_totals( $parent_id );

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de remettre les informations d'une commande enfant sur sa commande parente.
 *	@param $ord_parent_id Obligatoire, identifiant de la commande parente
 *	@param $ord_child_id Obligatoire, identifiant de la commande enfant
 *	@return bool True Si tout s'est correctement déroulé, False dans le cas contraire
 */
function ord_orders_child_revert_to_parent( $ord_parent_id, $ord_child_id ){
	if( !is_numeric($ord_parent_id) || !$ord_parent_id ){
		return false;
	}

	if( !is_numeric($ord_child_id) || !$ord_child_id ){
		return false;
	}

	global $config;

	// Récupère les informations de la commande enfant à transmettre sur la commande parent
	$rinfo_child = ria_mysql_query('
		select ord_adr_invoices, ord_adr_delivery, ord_ref, ord_date_livr, ord_srv_id, ord_str_id, ord_dlv_notes, ord_opt_gift, ord_opt_gift_message, ord_comments, ord_rly_id
		from riashop.ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$ord_child_id.'
	');

	if( !$rinfo_child || !ria_mysql_num_rows($rinfo_child) ){
		return false;
	}

	$info_child = ria_mysql_fetch_assoc( $rinfo_child );

	$info_child['ord_ref'] 				= '"'.addslashes( $info_child['ord_ref'] ).'"';
	$info_child['ord_date_livr'] 		= '"'.addslashes( $info_child['ord_date_livr'] ).'"';
	$info_child['ord_dlv_notes']		= '"'.addslashes( $info_child['ord_dlv_notes'] ).'"';
	$info_child['ord_opt_gift_message'] = '"'.addslashes( $info_child['ord_opt_gift_message'] ).'"';
	$info_child['ord_comments'] 		= '"'.addslashes( $info_child['ord_comments'] ).'"';

	$info_child['ord_adr_delivery']		= !is_numeric($info_child['ord_adr_delivery']) || !$info_child['ord_adr_delivery'] ? 'null' : $info_child['ord_adr_delivery'];
	$info_child['ord_srv_id']			= !is_numeric($info_child['ord_srv_id']) || !$info_child['ord_srv_id'] ? 'null' : $info_child['ord_srv_id'];
	$info_child['ord_str_id']			= !is_numeric($info_child['ord_str_id']) || !$info_child['ord_str_id'] ? 'null' : $info_child['ord_str_id'];
	$info_child['ord_rly_id']			= !is_numeric($info_child['ord_rly_id']) || !$info_child['ord_rly_id'] ? 'null' : $info_child['ord_rly_id'];

	return ria_mysql_query('
		update riashop.ord_orders
		set ord_adr_invoices = '.$info_child['ord_adr_invoices'].',
			ord_adr_delivery = '.$info_child['ord_adr_delivery'].',
			ord_ref = '.$info_child['ord_ref'].',
			ord_date_livr = '.$info_child['ord_date_livr'].',
			ord_srv_id = '.$info_child['ord_srv_id'].',
			ord_str_id = '.$info_child['ord_str_id'].',
			ord_dlv_notes = '.$info_child['ord_dlv_notes'].',
			ord_opt_gift = '.$info_child['ord_opt_gift'].',
			ord_opt_gift_message = '.$info_child['ord_opt_gift_message'].',
			ord_comments = '.$info_child['ord_comments'].',
			ord_rly_id = '.$info_child['ord_rly_id'].'
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$ord_parent_id.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère l'identifiant d'une commande à partir de son numéro de pièce.
 *	Le numéro de pièce est unique pour les commandes qui ne sont pas masquées et pas à l'état 10.
 *	La recherche ne prend pas en compte les commmandes masquées.
 *	@param string $piece Numéro de pièce.
 *	@return int L'identifiant de la commande, ou False en cas d'échec
 */
function ord_orders_get_id( $piece ){
	global $config;

	$piece = trim($piece);
	if( $piece == '' ){
		return false;
	}

	$r = ria_mysql_query('
		select ord_id
		from riashop.ord_orders
		where
			ord_tnt_id = '.$config['tnt_id'].'
			and ord_piece = "'.addslashes($piece).'"
			and (
				ord_masked = 0 or ord_state_id != '._STATE_CANCEL_MERCHAND.'
			)
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère des commandes validées pour lesquelles il existe un écart entre les totaux de lignes et d'entête.
 *	Les commandes comportant un code promotion sont exclues (l'écart de montant étant alors logique).
 *	La comparaison se fait sur le total HT arrondi à 2 chiffres.
 *
 *	@param string $date_start Optionnel, date à partir de laquelle les commandes sont prises en compte.
 *	@param int $fld_mark_check Optionnel, identifiant d'un champ avancé (type booléen) indiquant, si valeur "Oui", que la commande a déjà été vérifiée et ne peut apparaitre dans le résultat. Le code appelant doit se charger de spécifier cette valeur sur les commandes récupérées à partir de cette fonction.
 *	@param int $wst_id Optionnel, identifiant d'un site spécifique.
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la commande.
 *		- piece : numéro de pièce.
 *		- state_id : identifiant de statut.
 *		- date : date de la commande (format brut).
 *		- usr_id : identifiant du client.
 *		- total_ht : total HT sur l'entête.
 *		- rows_total_ht : total HT sur les lignes.
 *	@return bool False en cas d'échec.
 */
function ord_orders_get_amount_gap( $date_start = false, $fld_mark_check = 0, $wst_id = 0 ){

	if( !is_numeric($fld_mark_check) || $fld_mark_check < 0 ){
		return false;
	}

	if( $date_start !== false ){
		if( !isdateheure($date_start) ){
			return false;
		}else{
			$date_start = dateheureparse($date_start);
		}
	}

	global $config;

	$sql = '
		select
			ord_id as "id", ord_piece as "piece", ord_state_id as "state_id", ord_date as "date",
			ord_usr_id as "usr_id", ord_total_ht as "total_ht", round((
				select sum(prd_price_ht * prd_qte) from riashop.ord_products
				where prd_tnt_id = ord_tnt_id and prd_ord_id = ord_id
			), '.$config['round_digits_count'].') as "rows_total_ht"
		from
			riashop.ord_orders
		where
			ord_tnt_id = '.$config['tnt_id'].'
			and ord_state_id in ('.implode(', ', ord_states_get_ord_valid( true )).')
			and ord_pmt_id is null
	';

	// filtre date
	if( $date_start !== false ){
		$sql .= ' and ord_date >= "'.addslashes($date_start).'"';
	}

	// filtre site
	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and ord_wst_id = '.$wst_id;
	}

	// filtre commandes non marquées
	if( $fld_mark_check > 0 ){
		$sql .= '
			and not exists (
				select 1 from riashop.fld_object_values
				where pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = ord_id and pv_fld_id = '.$fld_mark_check.'
				and pv_lng_code = "'.addslashes($config['i18n_lng']).'"
				and lower(pv_value) in ("oui", "1")
			)
		';
	}

	// écart de total HT
	$sql .= '
		and round((
			select sum(prd_price_ht * prd_qte) from riashop.ord_products
			where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = ord_id
		), '.$config['round_digits_count'].') != round(ord_total_ht, '.$config['round_digits_count'].')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction notifie les produits d'une commande en reliquat (commandes au statut 6, 7, 8, ou 11 contenant des produits non préparés / livrés).
 *	Pour que cela fonctionne, la numérotation "line_id" des pièces de vente doit être cohérente (un produit commandé, préparé livré, et facturé à le même "line_id" dans tous les documents). C'est le cas de la plupart des gestions commerciales.
 *	L'implémentation de l'email à proprement parler se fait au cas par cas du client, il n'y a pas de template par défaut actuellement.
 *
 *	@param int $ord_id Identifiant de la commande.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 *
 */
function ord_orders_notify_reliquat( $ord_id ){

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	// chargement de la commande
	$rorder = ord_orders_get_with_adresses( 0, $ord_id );
	if( !$rorder || !ria_mysql_num_rows($rorder) ){
		return false;
	}
	$order = ria_mysql_fetch_assoc($rorder);

	// celle-ci doit être un statut "préparé", "livré" ou "facturé"
	if( !in_array($order['state_id'], array(_STATE_PREPARATION, _STATE_BL_READY, _STATE_BL_PARTIEL_EXP, _STATE_BL_EXP, _STATE_INVOICE, _STATE_BL_STORE, _STATE_INV_STORE)) ){
		return false;
	}

	// chargement des produits
	$products = ord_products_get( $ord_id );
	if( !$products || !ria_mysql_num_rows($products) ){
		return false;
	}

	// récupère les lignes des pièces supérieures
	$all_rows_sup = array();

	if( $rpl = ord_orders_pl_get( $ord_id ) ){
		while( $pl = ria_mysql_fetch_assoc($rpl) ){
			if( $rp_pl = ord_pl_products_get( $pl['id'], $ord_id ) ){
				while( $p_pl = ria_mysql_fetch_assoc($rp_pl) ){
					$all_rows_sup[] = $p_pl;
				}
			}
		}
	}

	if( $rbl = ord_orders_bl_get( $ord_id ) ){
		while( $bl = ria_mysql_fetch_assoc($rbl) ){
			if( $rp_bl = ord_bl_products_get( $bl['id'], $ord_id ) ){
				while( $p_bl = ria_mysql_fetch_assoc($rp_bl) ){
					$all_rows_sup[] = $p_bl;
				}
			}
		}
	}

	if( $rinv = ord_orders_invoices_get( $ord_id ) ){
		while( $inv = ria_mysql_fetch_assoc($rinv) ){
			if( $rp_inv = ord_inv_products_get( $inv['id'], false, false, null, null, $ord_id ) ){
				while( $p_inv = ria_mysql_fetch_assoc($rp_inv) ){
					$all_rows_sup[] = $p_inv;
				}
			}
		}
	}

	// différentiel (lignes de la commande n'étant pas dans des pièces supérieures
	$ord_prd_ar = array();
	while( $p = ria_mysql_fetch_assoc($products) ){
		$contains_in_sup = false;
		foreach( $all_rows_sup as $one_row_sup ){
			if( $one_row_sup['id'] == $p['id'] && $one_row_sup['line'] == $p['line'] ){
				$contains_in_sup = true;
				break;
			}
		}
		if( !$contains_in_sup ){
			$ord_prd_ar[] = $p;
		}
	}

	// pas de lignes en reliquat
	if( !sizeof($ord_prd_ar) ){
		return false;
	}

	// configuration d'email activée
	$rcfg = cfg_emails_get( 'ord-reliquat', $order['wst_id'] );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}
	$cfg = ria_mysql_fetch_assoc($rcfg);

	global $config;

	// fait une copie de la configuration
	$config_copy = $config;

	// charge les valeurs spécfiques au site de la commande
	if( $config['wst_id'] != $order['wst_id'] ){
		$ar_to_reload = array(
			'email_alerts_enabled', 'days_order_notify', 'allow_orders_update_state',
			'notify_state_wait_pay_for_cb', 'notify-seller', 'site_name', 'email_html_header',
			'site_dir', 'email_html_header_order', 'site_have_user_space',
			'adr_pay_cheque', 'site_url', 'contact_page_url', 'email_html_footer', 'ord_notify_only_web',
			'show_barcode_in_order_notify', 'weight_col_calc_lines', 'assurance_ref', 'prd_ref_cbmulti',
			'show_fields_in_notify', 'show_price_in_weight_unit', 'show_port_in_order_confirmation',
			'show_notify_code_discount', 'show_original_port_name', 'site_have_user_histo', 'subscribe_notify_reliquat', 'active_email_perso',
		);
		if( $rconf_from_ord = cfg_overrides_get( $order['wst_id'], array(), $ar_to_reload ) ){
			while( $conf_from_ord = ria_mysql_fetch_assoc($rconf_from_ord) ){
				$config[ $conf_from_ord['code'] ] = $conf_from_ord['value'];
			}
		}
	}

	// Notifications désactivées
	if( !$config['email_alerts_enabled'] ){
		$config = $config_copy;
		return false;
	}

	// Commande non web
	if( !$config['ord_notify_only_web'] && !$order['pay_id'] ){
		$config = $config_copy;
		return false;
	}

	// chargement du client
	$rusr = gu_users_get( $order['user'] );
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		$config = $config_copy;
		return false;
	}
	$usr = ria_mysql_fetch_assoc($rusr);

	// le client est-il abonné à la notification ?
	$rvar = cfg_overrides_get( $order['wst_id'], array(), 'subscribe_notify_reliquat', $usr['id'] );
	if( $rvar && ria_mysql_num_rows($rvar) ){
		$config['subscribe_notify_reliquat'] = ria_mysql_result($rvar, 0, 'value');
	}

	// client non abonné à la notification
	if( !$config['subscribe_notify_reliquat'] ){
		$config = $config_copy;
		return false;
	}

	// Crée le message
	$email = new Email();
	$email->setFrom( $cfg['from'] );

	// phase de test
	//$email->addTo( '<EMAIL>' );
	$email->addTo( '<EMAIL>' );

	// Extranet Chazelles
	if( $config['tnt_id'] == 8 && $order['wst_id'] == 22 ){
		require_once($config['site_dir'].'/include/view.email.inc.php');
		$send_ok = send_confirmation_mail( $order, $usr, $ord_prd_ar, $email, null, gu_users_rights_used( '_RGH_DWL_PRC', $usr['id'] ), true );
		$config = $config_copy;
		return $send_ok;
	}

	$config = $config_copy;
	return false;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de formater les données d'une commande pour préparer des fichier CSV pour les étiquettes : Chronopost, Colissimo et Mondial Relay
 *	@param resource $order Obligatoire, résultat MySQL d'une commande retourné par ord_orders_get_with_adresses()
 *	@param int $length_addr Optionnel, taille maximale pour les champs adresses (par défaut : 38)
 *	@return array Un tableau contenant les informations suivante :
 *				- user_id : identifiant du compte client
 *				- rly_ref : référence du point relai
 *				- rly_country : pays (ISO) du poit relai
 *				- lastname : nom du client
 *				- firstname : prénom du client
 *				- society : raison sociale du client
 *				- country : pays (ISO) du client
 *				- phone : numéro de téléphone (mobile sinon téléphone fixe)
 *				- email : adresse mail du client
 *				- zipcode : code postal du client
 *				- city : ville du client
 *				- addr1 : adresse 1 du client
 *				- addr2 : adresse 2 du client
 *				- addr3 : adresse 3 du client
 *				- ref_order : référence de la commande
 *				- weight_order : poids total de la commande
 *				- total_ttc : total TTC de la commande
 *				- dlv-notes : consignes de livraison
 *				- title_id : identifiant de la civilité
 */
function ord_orders_shipment_formatted( $order, $length_addr=38 ){
	if( !ria_array_key_exists(array('rly_id', 'inv_lastname', 'inv_firstname', 'inv_country', 'inv_mobile', 'inv_phone', 'inv_email', 'inv_postal_code', 'inv_city', 'dlv_lastname', 'dlv_firstname', 'dlv_country', 'dlv_mobile', 'dlv_phone', 'dlv_email', 'dlv_postal_code', 'dlv_city', 'inv_address1', 'inv_address2', 'inv_address3', 'user', 'ref', 'id', 'srv_id'), $order) ){
		return false;
	}

	global $config;

	$rly_id = $title_id = $assurance = 0;
	$rly_ref = $rly_country = $firstname = $lastname = $society = $addr1 = $addr2 = $addr3 = $compl_addr2 = $compl_addr3 = $zipcode = $city = $country = $phone = $mobile = $email = '';

	if( is_numeric( $order['rly_id'] ) && $order['rly_id'] > 0 ){
		$rly_id = $order['rly_id'];
	}

	if( strtoupper2($order['inv_country']) == "FRANCE METROPOLITAINE" ){
		$order['inv_country'] = 'FRANCE';
	}

	if( strtoupper2($order['dlv_country']) == "FRANCE METROPOLITAINE" ){
		$order['dlv_country'] = 'FRANCE';
	}



	$inv_country_iso = sys_countries_get_code( $order['inv_country'], true );
	$dlv_country_iso = sys_countries_get_code( $order['dlv_country'], true );

	if( $config['tnt_id'] == 43 ){
		if( trim($inv_country_iso) == '' || trim($dlv_country_iso) == '' ){
			$ar_zones = array();

			$r_zone = dlv_zones_get( 0, true );
			if( $r_zone ){
				while( $zone = ria_mysql_fetch_assoc($r_zone) ){
					$ar_zones[] = array( 'code' => $zone['desc'], 'name' => $zone['name'] );
				}
			}
		}

		if( trim($inv_country_iso) == '' ){
			foreach( $ar_zones as $data ){
				if( strtoupper2($data['name']) == strtoupper2($order['inv_country']) ){
					$inv_country_iso = $data['code'];
					break;
				}
			}
		}

		if( trim($dlv_country_iso) == '' ){
			foreach( $ar_zones as $data ){
				if( strtoupper2($data['name']) == strtoupper2($order['dlv_country']) ){
					$dlv_country_iso = $data['code'];
					break;
				}
			}
		}
	}

	$mobile = trim($order['dlv_mobile']) != '' ? $order['dlv_mobile'] : $order['inv_mobile'];
	$order['inv_mobile'] = $mobile;
	$order['dlv_mobile'] = $mobile;

	if( $rly_id > 0 ){
		$r_relay = dlv_relays_get_simple( $rly_id );
		if( !$r_relay || !ria_mysql_num_rows($r_relay) ){
			error_log(__FILE__.':'.__LINE__.' ['.$order['id'].'] error dlv_relays_get_simple()');
			return false;
		}

		$relay = ria_mysql_fetch_assoc( $r_relay );

		$rly_ref 	 = $relay['ref'];
		$rly_country = $relay['cnt_code'];

		if( trim($rly_ref) == '' ){
			error_log(__FILE__.':'.__LINE__.' ['.$order['id'].'] error dlv_relays_get_ref()');
			return false;
		}

		$title_id 	= $order['inv_title_id'];
		$lastname 	= $order['inv_lastname'];
		$firstname 	= $order['inv_firstname'];
		$society 	= $order['inv_society'];
		$country 	= $inv_country_iso;
		$phone 		= $order['inv_phone'];
		$mobile		= $order['inv_mobile'];
		$email 		= trim($order['inv_email']) != '' ? $order['inv_email'] : $email;
		$zipcode 	= $order['inv_postal_code'];
		$city 		= $order['inv_city'];
	}else{
		$title_id 	= $order['dlv_title_id'];
		$lastname 	= $order['dlv_lastname'];
		$firstname 	= $order['dlv_firstname'];
		$society 	= $order['dlv_society'];
		$country 	= $dlv_country_iso;
		$phone 		= $order['dlv_phone'];
		$mobile		= $order['dlv_mobile'];
		$email 		= trim($order['dlv_email']) != '' ? $order['dlv_email'] : $email;
		$zipcode 	= $order['dlv_postal_code'];
		$city 		= $order['dlv_city'];
	}

	if( trim($country) == '' ){
		$country = 'FR';
	}

	$addr1 = ria_adresses_postal_abbr( $rly_id > 0 ? $order['inv_address1'] : $order['dlv_address1'] );
	if( strlen($addr1) > $length_addr ){
		$addr1 = strcut( $addr1, $length_addr, '' );
		$compl_addr2 = str_replace( $addr1, '', $addr1 );
	}

	if( trim($compl_addr2) != '' ){
		$addr2 = ' '.$compl_addr2;
	}

	$addr2 = ria_adresses_postal_abbr( $rly_id > 0 ? $order['inv_address2'].' '.$order['inv_address3'] : $order['dlv_address2'].' '.$order['inv_address3'] ).$addr2;
	if( trim($addr2) != '' && strlen($addr2) > $length_addr ){
		$addr2 = strcut( $addr2, $length_addr, '' );
		$compl_addr3 = str_replace( $addr2, '', $addr2 );

		if( trim($compl_addr3) != '' ){
			$addr3 = strcut( $compl_addr3, $length_addr, '' );
		}
	}

	$addr1 = strtoupper2( $addr1 );
	$addr2 = strtoupper2( $addr2 );
	$addr3 = strtoupper2( $addr3 );
	$city  = strtoupper2( $city );

	$email = gu_users_get_email( $order['user'] );
	if( trim($email) == '' ){
		error_log(__FILE__.':'.__LINE__.' ['.$order['id'].'] error gu_users_get_email()');
		return false;
	}

	$ref_order 		= trim($order['ref']) != '' ? $order['ref'] : $order['id'];
	$weight_order 	= ord_orders_weight_get( $order['id'] );

	switch( $config['tnt_id'] ){
		case 43 : {
			$weight_order = fld_object_values_get( $order['id'], 3592 );
			$weight_order = str_replace( array(' ', ','), array('', '.'), $weight_order );

			$assurance = fld_object_values_get( $order['id'], 3593 );
			if( is_numeric($weight_order) && $weight_order > 0 ){
				$weight_order = $weight_order * 1000;
			}

			$ref_order = $order['id'];
			break;
		}
	}

	return array(
		'id'				=> $order['id'],
		'user_id'			=> $order['user'],
		'rly_id'			=> $rly_id,
		'rly_ref'			=> $rly_ref,
		'rly_country'		=> $rly_country,
		'title_id'			=> $title_id,
		'lastname'			=> $lastname,
		'firstname'			=> $firstname,
		'society'			=> $society,
		'country'			=> $country,
		'phone'				=> $phone,
		'mobile'			=> $mobile,
		'email'				=> $email,
		'zipcode'			=> $zipcode,
		'city'				=> $city,
		'addr1'				=> $addr1,
		'addr2'				=> $addr2,
		'addr3'				=> $addr3,
		'ref_order'			=> $ref_order,
		'weight_order'		=> $weight_order,
		'total_ttc' 		=> $order['total_ttc'],
		'dlv-notes' 		=> $order['dlv-notes'],
		'assurance' 		=> $assurance
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'envoyer le fichier CSV pour les étiquettes : Chronopost, Colissimo et Mondial Relay par mail.
 *	TODO : Configurer les adresses e-mails
 *	@param string $filename Obligatoire, adresse physique du fichier CSV
 *	@param string $srv_name Obligatoire, nom du service de livraison
 *	@param array $ar_ord_ids Obligatoire, tableau contenant les identifiants de commande présentes dans le fichier CSV
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 */
function ord_orders_shipment_send( $filename, $srv_name, $ar_ord_ids ){
	if( !file_exists($filename) || trim($srv_name) == '' ){
		return false;
	}

	if( !is_array($ar_ord_ids) || !sizeof($ar_ord_ids) ){
		return false;
	}

	global $config;

	// Envoi par mail du rapport
	$email = new Email();

	$email->setFrom( 'RiaShop <<EMAIL>>');
	$email->addTo( '<EMAIL>');
	// $email->addBcc( '<EMAIL>');
	$email->setSubject( '[Purebike] Export des commandes '.$srv_name );
	$email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
	$email->addParagraph('Bonjour,');
	$email->addParagraph('Vous trouverez en pièce joint l\'export des commandes pour leur intégrations dans '.$srv_name.'.');
	$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );
	$email->addAttachment( $filename );

	if( $email->send() ){
		// Marque les commandes comme étant notifiée
		foreach( $ar_ord_ids as $one_ord ){
			fld_object_values_set( $one_ord, $config['fld_ord_exp_dlv'], 'Oui' );
		}
	}else{
		error_log(__FILE__.':'.__LINE__.' error send_order_for_etiquette : '.implode(' || ', $ar_ord_ids));
	}
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter pour une commande un code promotion
 * @param int $order_id          Identifiant de la commande
 * @param int $promotion_id      Identifiant du code promotion
 * @param int $promotion_type_id Identifiant du type de promotion
 *
 * @return bool true en cas de succès, false en cas d'erreur
 */
function ord_orders_promotions_add($order_id, $promotion_id, $promotion_type_id){
	if( !is_numeric($order_id) || $order_id <= 0 ){
		return false;
	}
	if( !is_numeric($promotion_id) || $promotion_id <= 0 ){
		return false;
	}

	if( !in_array($promotion_type_id, pmt_types_get_ids()) ){
		return false;
	}

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'oop_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'oop_ord_id';
	$values[] = $order_id;

	$fields[] = 'oop_pmt_id';
	$values[] = $promotion_id;

	$fields[] = 'oop_type';
	$values[] = $promotion_type_id;

	return ria_mysql_query('
		replace into riashop.ord_orders_promotions
			(' . implode(',', $fields) . ')
		values
			(' . implode(',', $values) . ')
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les promotions liées à une commande
 * @param int $order_id          Obligatoire, Identifiant de la commande
 * @param int $promotion_type_id Facultatif,tableau d'identifiants de type de promotion sur lequel filtrer le résultat
 *
 * @return array Retourne un tableau avec les codes promotions appliqués à une commande
 *                                       - ord_id : identifiant de la commande
 *                                       - tnt_id : identifiant du tenant
 *                                       - pmt_id : identifiant du code promotion
 *                                       - pmt_type : identifiant du type de code promotion
 */
function ord_orders_promotions_get($order_id, $promotion_type_id=array()){
	$ord_pmt = array();

	if( !is_numeric($order_id) || $order_id <= 0 ){
		return $ord_pmt;
	}

	if( !empty($promotion_type_id) && !control_array_integer($promotion_type_id) ){
		return $ord_pmt;
	}

	global $config;

	$sql = '
		select oop_tnt_id as tnt_id,
				oop_ord_id as ord_id,
				oop_pmt_id as pmt_id,
				oop_type as pmt_type
		from riashop.ord_orders_promotions
		where oop_tnt_id = ' . $config['tnt_id'] .'
	';

	if( $order_id ){
		$sql .= ' and oop_ord_id=' . $order_id;
	}

	if( !empty($promotion_type_id) ){
		$sql .= ' and oop_type in (' . implode($promotion_type_id) . ')';
	}

	$order_by = array(
		_PMT_TYPE_CODE,
		_PMT_TYPE_CREDIT,
		_PMT_TYPE_BA,
		_PMT_TYPE_PRD,
		_PMT_TYPE_REDUC,
		_PMT_TYPE_BUY_X_FREE_Y,
		_PMT_TYPE_REWARD,
		_PMT_TYPE_CHEEKBOOK,
		_PMT_TYPE_REMISE,
		_PMT_TYPE_SOLDES,
		_PMT_TYPE_GIFTS
	);
	$sql .= ' order by ' . sql_order_by_array( $order_by, 'oop_type') . ', oop_pmt_id asc';

	$result = ria_mysql_query($sql);

	if ($result && ria_mysql_num_rows($result)) {
		while ($pmt = ria_mysql_fetch_assoc($result)){
			$ord_pmt[] = $pmt;
		}
	}

	return $ord_pmt;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimé un ou tous les code promotions appliquer a une commande
 * @param int $order_id Obligatoire, Identifiant de la commande
 * @param int $promotion_id Facultatif, identifiant de la promotion
 *
 * @return bool True si succès, false si échec
 */
function ord_orders_promotions_del($order_id, $promotion_id=0){

	if( !is_numeric($order_id) || $order_id <= 0 ){
		return false;
	}

	if( !is_numeric($promotion_id) || $promotion_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from  riashop.ord_orders_promotions
		where oop_tnt_id = ' . $config['tnt_id'] .'
			and oop_ord_id=' . $order_id .'
	';

	if( $promotion_id ){
		$sql .= ' and oop_pmt_id=' . $promotion_id;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de définir le montant correspondant aux produits exclus pour une zone donnée
 * @param string $type Obligatoire, Chaine de caractères qui défini le type de calcul du tarif de livraison ('qte' pour la quantité, 'weight' pour le poids brut, etc...)
 * @param int $order Obligatoire, Identifiant d'une commande
 * @param int $zone Obligatoire, Identifiant de la zone de livraison
 * @param float $rule_value Facultatif, En fonction du type de critère, nombre de produits, poids (brut/net) ou prix (ht/ttc) dans la commande. Nécessaire dans le cas de locataire ayant des produits avec frais de ports spéciaux (Ex. Destock-Fenêtre)
 */
function ord_orders_excluded_products_amount_get($type, $order, $zone, $rule_value=0){

	if (!is_numeric($order) || $order < 0){
		return 0;
	}
	if (!is_numeric($zone) || $zone < 0){
		return 0;
	}
	if (!in_array($type, array('qte', 'weight', 'weight_net', 'price_ht', 'price_ttc'))){
		return 0;
	}

	global $config;

	$r_zone = dlv_zones_get($zone);
	$zo = ria_mysql_fetch_assoc($r_zone);

	$r_products = ord_products_get($order);
	$ar_products = array();

	if ($rule_value === 0){
		switch($type){
			case 'qte' :
				$rule_value = ord_products_get_all_qte($order);
				break;
			case 'weight' :
				$rule_value = ord_orders_weight_get($order, 'kg');
				break;
			case 'weight_net' :
				$rule_value = ord_orders_weight_get($order, 'kg', true);
				break;
			case 'price_ht' :
				$rule_value = ord_orders_get_total_without_port($order);
				break;
			case 'price_ttc' :
				$rule_value = ord_orders_get_total_without_port($order, true);
				break;
		}
	}

	while ($prd = ria_mysql_fetch_assoc($r_products)){
		$r_cly = prd_classify_get(false,$prd['id']);
		$ar_cats = array();
		while($cly = ria_mysql_fetch_assoc($r_cly)){
			$ar_cats[] = $cly['cat'];
		}

		$r_brd = prd_brands_get(0,false,$prd['brd_name']);
		$brd = ria_mysql_fetch_assoc($r_brd);

		$ar_products[$prd['id']] = array(
			'include' => $zo['all_catalog'],
			'brd' => $brd['id'],
			'cat' => $ar_cats,
			'qte' => $prd['qte'],
			'weight' => $prd['weight'] * $prd['qte'],
			'weight_net' => $prd['weight_net_total'],
			'price_ht' => $prd['total_ht'],
			'price_ttc' => $prd['total_ttc']
		);
	}

	$r_dlv_categories = dlv_zones_categories_get($zone);
	$ar_dlv_categories = array();
	while ($dlv_category = ria_mysql_fetch_assoc($r_dlv_categories)){
		$ar_dlv_categories[$dlv_category['id']] = $dlv_category['include'];
		$childs = prd_categories_childs_get_array($dlv_category['id']);
		foreach($childs as $key => $id){
			$ar_dlv_categories[$id] = $dlv_category['include'];
		}
	}

	$r_dlv_brands = dlv_zones_brands_get($zone);
	$ar_dlv_brands = array();
	while ($dlv_brands = ria_mysql_fetch_assoc($r_dlv_brands)){
		$ar_dlv_brands[$dlv_brands['id']] = $dlv_brands['include'];
	}

	$r_dlv_products = dlv_zones_products_get($zone);
	$ar_dlv_products = array();
	while ($dlv_products = ria_mysql_fetch_assoc($r_dlv_products)){
		$ar_dlv_products[$dlv_products['id']] = $dlv_products['include'];
	}

	$excluded_products = $zo['all_catalog'] ? 0 : $rule_value;
	if ($ar_dlv_categories != array() || $ar_dlv_brands != array() || $ar_dlv_products != array()){
		foreach ($ar_products as $prd_id => $data) {
			foreach ($data['cat'] as $key => $cat_id) {
				if (array_key_exists($cat_id, $ar_dlv_categories)){
					if ($ar_dlv_categories[$cat_id] != $zo['all_catalog']){
						$ar_products[$prd_id]['include'] = $ar_dlv_categories[$cat_id];
					}
				}
			}

			if (array_key_exists($data['brd'], $ar_dlv_brands)){
				$ar_products[$prd_id]['include'] = $ar_dlv_brands[$data['brd']];
			}

			if (array_key_exists($prd_id, $ar_dlv_products)){
				$ar_products[$prd_id]['include'] = $ar_dlv_products[$prd_id];
			}

			if (!$ar_products[$prd_id]['include'] && $zo['all_catalog']){ //Si le produit est exclu et que c'est une zone d'inclusion, alors on ajoute au compteur
				switch($type){
					case 'qte' :
						$excluded_products += $ar_products[$prd_id]['qte'];
						break;
					case 'weight' :
						$excluded_products += $ar_products[$prd_id]['weight'];
						break;
					case 'weight_net' :
						$excluded_products += $ar_products[$prd_id]['weight_net'];
						break;
					case 'price_ht' :
						$excluded_products += $ar_products[$prd_id]['price_ht'];
						break;
					case 'price_ttc' :
						$excluded_products += $ar_products[$prd_id]['price_ttc'];
						break;
				}
			} elseif($ar_products[$prd_id]['include'] && !$zo['all_catalog']) { //Si le produit est inclus et que c'est une zone d'exclusion, alors on soustrait du compteur
				switch($type){
					case 'qte' :
						$excluded_products -= $ar_products[$prd_id]['qte'];
						break;
					case 'weight' :
						$excluded_products -= $ar_products[$prd_id]['weight'];
						break;
					case 'weight_net' :
						$excluded_products -= $ar_products[$prd_id]['weight_net'];
						break;
					case 'price_ht' :
						$excluded_products -= $ar_products[$prd_id]['price_ht'];
						break;
					case 'price_ttc' :
						$excluded_products -= $ar_products[$prd_id]['price_ttc'];
						break;
				}
			}
		}
	}

	return $excluded_products;
}
// \endcond

/** Détermine si la commande contient un produit ayant des frais de port spécifiques.
 *
 * @param  int $id Identifiant de la commande
 * @return bool true si succès, false autrement
 */
function ord_orders_has_specific_shipping_charges( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from riashop.ord_products op
		where op.prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$id.' and exists (
				select 1
				from riashop.prd_products pp
				where pp.prd_tnt_id = '.$config['tnt_id'].'
					and op.prd_id = pp.prd_id
					and prd_has_specific_shipping_charges = 1
			)
	');

	return $res && ria_mysql_num_rows($res);
}

/** Détermine si la commande utilise la livraison sur plage horaires.
 *
 * @param  int $id Identifiant de la commande
 * @return bool true si succès, false autrement
 */
function ord_orders_is_using_plage_delivery( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from riashop.ord_orders_plage
		where oop_tnt_id = '.$config['tnt_id'].'
			and oop_ord_id = '.$id.'
			and oop_date_deleted is null
	');

	return $res && ria_mysql_num_rows($res);
}
/**
 * Cette fonction permet de mettre à jour la référence gescom d'une commande si la pièce et différente de l'identifiant
 *
 * @param integer $ord_id Identifiant de la commande
 * @param string $ref_gescom Référence gescom de la commande dans salesforce
 * @return bool Retourne true si succès false si erreur
 */
function ord_orders_ref_gescom_set($ord_id, $ref_gescom){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update riashop.ord_orders
			set ord_ref_gescom="'.addslashes($ref_gescom).'"
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

}

/**
 * Cette fonction permet de récupérer l'auteur pour une commande
 * @param integer $ord_id Identifiant de la commande
 * @return bool Retourne le résultat de gu_users_get pour l'auteur, false si erreur
 */
function ord_orders_get_authors($ord_id){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select oos_usr_id
		from riashop.ord_orders_states as ip
		join riashop.ord_orders o on ip.oos_ord_id = o.ord_id and ip.oos_tnt_id = o.ord_tnt_id
		where ip.oos_tnt_id = '.$config['tnt_id'].' and ip.oos_ord_id = '.$ord_id.'
			and (
				case
				when ord_state_id='._STATE_DEVIS.' then oos_state_id='._STATE_DEVIS.'
				when ord_state_id='._STATE_BASKET.' then oos_state_id='._STATE_BASKET.'
				when ord_state_id='._STATE_INTERVENTION_DEVIS.' then oos_state_id='._STATE_INTERVENTION_DEVIS.'
				else oos_state_id='._STATE_WAIT_PAY.'
				end
			)
		');

	if( $res && ria_mysql_num_rows($res)){
		$author_id = ria_mysql_result($res, 0, "oos_usr_id");

		$rusr = gu_users_get($author_id);
		if( $rusr && ria_mysql_num_rows($rusr) ) {
			return ria_mysql_fetch_assoc($rusr);
		}
	}

	return false;
}

/** Cette fonction permet de récupérer la date de la plus ancienne commandes
 * 	@return string Date de la plus ancienne commande au format yyyy-mm-dd hh:ii:ss
 */
function ord_orders_get_oldest(){
	global $config;

	$res = ria_mysql_query('
		select min(ord_date) as oldest
		from ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_masked = 0
			and year(ord_date) > 1950
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['oldest'];
}

/// @}
